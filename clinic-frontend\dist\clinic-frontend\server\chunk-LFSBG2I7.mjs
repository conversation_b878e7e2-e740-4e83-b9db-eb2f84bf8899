import './polyfills.server.mjs';
import {
  RouterLink,
  RouterModule
} from "./chunk-YKEX2NSQ.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts
var MedicalRecordListComponent = class _MedicalRecordListComponent {
  constructor() {
  }
  ngOnInit() {
  }
  static \u0275fac = function MedicalRecordListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MedicalRecordListComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MedicalRecordListComponent, selectors: [["app-medical-record-list"]], decls: 9, vars: 0, consts: [[1, "container", "mx-auto", "p-4"], [1, "text-2xl", "font-bold", "mb-4"], [1, "mb-4"], ["routerLink", "add", 1, "bg-blue-500", "text-white", "px-4", "py-2", "rounded", "hover:bg-blue-600"], [1, "grid", "gap-4"]], template: function MedicalRecordListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2, "Medical Records");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 2)(4, "a", 3);
      \u0275\u0275text(5, " Add New Record ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 4)(7, "p");
      \u0275\u0275text(8, "Medical records will be displayed here");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [CommonModule, RouterModule, RouterLink], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MedicalRecordListComponent, [{
    type: Component,
    args: [{ selector: "app-medical-record-list", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Records</h1>
      <div class="mb-4">
        <a routerLink="add" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
          Add New Record
        </a>
      </div>
      <div class="grid gap-4">
        <!-- Placeholder for medical records list -->
        <p>Medical records will be displayed here</p>
      </div>
    </div>
  ` }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MedicalRecordListComponent, { className: "MedicalRecordListComponent", filePath: "src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts", lineNumber: 25 });
})();
export {
  MedicalRecordListComponent
};
//# sourceMappingURL=chunk-LFSBG2I7.mjs.map
