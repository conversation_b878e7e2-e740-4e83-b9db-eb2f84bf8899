import {
  DoctorService
} from "./chunk-T7JLGHDG.js";
import {
  <PERSON>fa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>or,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-T7IIKLN2.js";
import {
  ActivatedRoute,
  Router
} from "./chunk-2NNV54NL.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-7FZJUQ36.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/doctors/doctor-form/doctor-form.component.ts
var DoctorFormComponent = class _DoctorFormComponent {
  fb;
  doctorService;
  route;
  router;
  doctorForm;
  isEditMode = false;
  doctorId = null;
  constructor(fb, doctorService, route, router) {
    this.fb = fb;
    this.doctorService = doctorService;
    this.route = route;
    this.router = router;
    this.doctorForm = this.fb.group({
      firstName: ["", Validators.required],
      lastName: ["", Validators.required],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: ["", Validators.required],
      specialization: ["", Validators.required],
      licenseNumber: ["", Validators.required]
    });
  }
  ngOnInit() {
    this.doctorId = this.route.snapshot.paramMap.get("id");
    if (this.doctorId) {
      this.isEditMode = true;
      this.loadDoctor();
    }
  }
  loadDoctor() {
    if (this.doctorId) {
      this.doctorService.getDoctor(this.doctorId).subscribe({
        next: (doctor) => {
          this.doctorForm.patchValue(doctor);
        },
        error: (error) => {
          console.error("Error loading doctor:", error);
          this.router.navigate(["/doctors"]);
        }
      });
    }
  }
  onSubmit() {
    if (this.doctorForm.valid) {
      const doctorData = this.doctorForm.value;
      if (this.isEditMode && this.doctorId) {
        this.doctorService.updateDoctor(this.doctorId, doctorData).subscribe({
          next: () => {
            this.router.navigate(["/doctors"]);
          },
          error: (error) => {
            console.error("Error updating doctor:", error);
          }
        });
      } else {
        this.doctorService.createDoctor(doctorData).subscribe({
          next: () => {
            this.router.navigate(["/doctors"]);
          },
          error: (error) => {
            console.error("Error creating doctor:", error);
          }
        });
      }
    }
  }
  static \u0275fac = function DoctorFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DoctorFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(DoctorService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DoctorFormComponent, selectors: [["app-doctor-form"]], decls: 34, vars: 4, consts: [[1, "container", "mx-auto", "px-4", "py-8"], [1, "text-2xl", "font-bold", "mb-6"], [1, "max-w-2xl", 3, "ngSubmit", "formGroup"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], [1, "form-group"], ["for", "firstName", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "text", "id", "firstName", "formControlName", "firstName", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["for", "lastName", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "text", "id", "lastName", "formControlName", "lastName", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["for", "email", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "email", "id", "email", "formControlName", "email", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["for", "phoneNumber", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "tel", "id", "phoneNumber", "formControlName", "phoneNumber", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["for", "specialization", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "text", "id", "specialization", "formControlName", "specialization", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["for", "licenseNumber", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["type", "text", "id", "licenseNumber", "formControlName", "licenseNumber", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], [1, "mt-6", "flex", "justify-end", "space-x-4"], ["type", "button", 1, "px-4", "py-2", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", 3, "click"], ["type", "submit", 1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "disabled:opacity-50", 3, "disabled"]], template: function DoctorFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "form", 2);
      \u0275\u0275listener("ngSubmit", function DoctorFormComponent_Template_form_ngSubmit_3_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(4, "div", 3)(5, "div", 4)(6, "label", 5);
      \u0275\u0275text(7, "First Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(8, "input", 6);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "div", 4)(10, "label", 7);
      \u0275\u0275text(11, "Last Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(12, "input", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "div", 4)(14, "label", 9);
      \u0275\u0275text(15, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 10);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "div", 4)(18, "label", 11);
      \u0275\u0275text(19, "Phone Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(20, "input", 12);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "div", 4)(22, "label", 13);
      \u0275\u0275text(23, "Specialization");
      \u0275\u0275elementEnd();
      \u0275\u0275element(24, "input", 14);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "div", 4)(26, "label", 15);
      \u0275\u0275text(27, "License Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(28, "input", 16);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(29, "div", 17)(30, "button", 18);
      \u0275\u0275listener("click", function DoctorFormComponent_Template_button_click_30_listener() {
        return ctx.router.navigate(["/doctors"]);
      });
      \u0275\u0275text(31, " Cancel ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "button", 19);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.isEditMode ? "Edit Doctor" : "Add New Doctor");
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.doctorForm);
      \u0275\u0275advance(29);
      \u0275\u0275property("disabled", !ctx.doctorForm.valid);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Update" : "Create", " ");
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName], styles: ["\n\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\ninput.ng-invalid.ng-touched[_ngcontent-%COMP%] {\n  border-color: #ef4444;\n}\n.error-message[_ngcontent-%COMP%] {\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n/*# sourceMappingURL=doctor-form.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DoctorFormComponent, [{
    type: Component,
    args: [{ selector: "app-doctor-form", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="container mx-auto px-4 py-8">\r
  <h1 class="text-2xl font-bold mb-6">{{ isEditMode ? 'Edit Doctor' : 'Add New Doctor' }}</h1>\r
\r
  <form [formGroup]="doctorForm" (ngSubmit)="onSubmit()" class="max-w-2xl">\r
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">\r
      <div class="form-group">\r
        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>\r
        <input\r
          type="text"\r
          id="firstName"\r
          formControlName="firstName"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
\r
      <div class="form-group">\r
        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>\r
        <input\r
          type="text"\r
          id="lastName"\r
          formControlName="lastName"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
\r
      <div class="form-group">\r
        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>\r
        <input\r
          type="email"\r
          id="email"\r
          formControlName="email"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
\r
      <div class="form-group">\r
        <label for="phoneNumber" class="block text-sm font-medium text-gray-700">Phone Number</label>\r
        <input\r
          type="tel"\r
          id="phoneNumber"\r
          formControlName="phoneNumber"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
\r
      <div class="form-group">\r
        <label for="specialization" class="block text-sm font-medium text-gray-700">Specialization</label>\r
        <input\r
          type="text"\r
          id="specialization"\r
          formControlName="specialization"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
\r
      <div class="form-group">\r
        <label for="licenseNumber" class="block text-sm font-medium text-gray-700">License Number</label>\r
        <input\r
          type="text"\r
          id="licenseNumber"\r
          formControlName="licenseNumber"\r
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"\r
        >\r
      </div>\r
    </div>\r
\r
    <div class="mt-6 flex justify-end space-x-4">\r
      <button\r
        type="button"\r
        (click)="router.navigate(['/doctors'])"\r
        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"\r
      >\r
        Cancel\r
      </button>\r
      <button\r
        type="submit"\r
        [disabled]="!doctorForm.valid"\r
        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"\r
      >\r
        {{ isEditMode ? 'Update' : 'Create' }}\r
      </button>\r
    </div>\r
  </form>\r
</div> `, styles: ["/* src/app/features/doctors/doctor-form/doctor-form.component.scss */\n.form-group {\n  margin-bottom: 1rem;\n}\ninput.ng-invalid.ng-touched {\n  border-color: #ef4444;\n}\n.error-message {\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n/*# sourceMappingURL=doctor-form.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: DoctorService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DoctorFormComponent, { className: "DoctorFormComponent", filePath: "src/app/features/doctors/doctor-form/doctor-form.component.ts", lineNumber: 14 });
})();
export {
  DoctorFormComponent
};
//# sourceMappingURL=chunk-TNV662RW.js.map
