import {
  UserRole
} from "./chunk-K2W23EYU.js";
import {
  AuthService
} from "./chunk-AUAHXWWU.js";
import {
  Router,
  RouterOutlet,
  bootstrapApplication,
  provideRouter
} from "./chunk-2NNV54NL.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  inject,
  provideHttpClient,
  setClassMetadata,
  withInterceptors,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-7FZJUQ36.js";
import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/app.component.ts
var AppComponent = class _AppComponent {
  title = "Clinic Management System";
  static \u0275fac = function AppComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppComponent, selectors: [["app-root"]], decls: 15, vars: 1, consts: [[1, "bg-gray-800"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8"], [1, "flex", "items-center", "justify-between", "h-16"], [1, "flex", "items-center"], [1, "flex-shrink-0"], [1, "text-white", "text-xl", "font-bold"], [1, "hidden", "md:block"], [1, "ml-10", "flex", "items-baseline", "space-x-4"], ["routerLink", "/dashboard", "routerLinkActive", "bg-gray-900 text-white", 1, "text-gray-300", "hover:bg-gray-700", "hover:text-white", "px-3", "py-2", "rounded-md", "text-sm", "font-medium"], ["routerLink", "/patients", "routerLinkActive", "bg-gray-900 text-white", 1, "text-gray-300", "hover:bg-gray-700", "hover:text-white", "px-3", "py-2", "rounded-md", "text-sm", "font-medium"], [1, "max-w-7xl", "mx-auto", "py-6", "sm:px-6", "lg:px-8"]], template: function AppComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "nav", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "span", 5);
      \u0275\u0275text(6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "div", 6)(8, "div", 7)(9, "a", 8);
      \u0275\u0275text(10, "Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "a", 9);
      \u0275\u0275text(12, "Patients");
      \u0275\u0275elementEnd()()()()()()();
      \u0275\u0275elementStart(13, "main", 10);
      \u0275\u0275element(14, "router-outlet");
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.title);
    }
  }, dependencies: [CommonModule, RouterOutlet], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  min-height: 100vh;\n  background-color: #f3f4f6;\n}\n/*# sourceMappingURL=app.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppComponent, [{
    type: Component,
    args: [{ selector: "app-root", standalone: true, imports: [CommonModule, RouterOutlet], template: `
    <nav class="bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <span class="text-white text-xl font-bold">{{ title }}</span>
            </div>
            <div class="hidden md:block">
              <div class="ml-10 flex items-baseline space-x-4">
                <a routerLink="/dashboard" routerLinkActive="bg-gray-900 text-white" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                <a routerLink="/patients" routerLinkActive="bg-gray-900 text-white" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">Patients</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <router-outlet></router-outlet>
    </main>
  `, styles: ["/* angular:styles/component:scss;a11e4d54b989e89ba7e016072ece737fd2214556f6c1c0569cfd6ece474d3086;F:/clinc/clinic-frontend/src/app/app.component.ts */\n:host {\n  display: block;\n  min-height: 100vh;\n  background-color: #f3f4f6;\n}\n/*# sourceMappingURL=app.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppComponent, { className: "AppComponent", filePath: "src/app/app.component.ts", lineNumber: 40 });
})();

// src/app/core/guards/auth.guard.ts
var authGuard = () => {
  const authService = inject(AuthService);
  const router = inject(Router);
  if (authService.isAuthenticated()) {
    return true;
  }
  return router.parseUrl("/login");
};

// src/app/core/guards/role.guard.ts
var roleGuard = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  if (!authService.isAuthenticated()) {
    return router.createUrlTree(["/login"], { queryParams: { returnUrl: state.url } });
  }
  const roles = route.data["roles"];
  if (!roles || roles.length === 0) {
    return true;
  }
  if (authService.hasRole(roles)) {
    return true;
  }
  return router.createUrlTree(["/unauthorized"]);
};

// src/app/app.routes.ts
var routes = [
  {
    path: "",
    redirectTo: "dashboard",
    pathMatch: "full"
  },
  __spreadValues({
    path: "login",
    loadComponent: () => import("./chunk-VWUYDA5N.js").then((m) => m.LoginComponent)
  }, false ? { \u0275entryName: "src/app/features/auth/login/login.component.ts" } : {}),
  __spreadValues({
    path: "register",
    loadComponent: () => import("./chunk-CKSYCCPR.js").then((m) => m.RegisterComponent)
  }, false ? { \u0275entryName: "src/app/features/auth/register/register.component.ts" } : {}),
  __spreadValues({
    path: "initialize-admin",
    loadComponent: () => import("./chunk-GEDO4QZ2.js").then((m) => m.InitializeAdminComponent)
  }, false ? { \u0275entryName: "src/app/features/auth/initialize-admin/initialize-admin.component.ts" } : {}),
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-XYKCDUQR.js").then((m) => m.MainLayoutComponent),
    canActivate: [authGuard],
    children: [
      {
        path: "dashboard",
        loadComponent: () => import("./chunk-CZULAT3Z.js").then((m) => m.DashboardComponent)
      },
      {
        path: "patients",
        loadChildren: () => import("./chunk-MFEWHNXC.js").then((m) => m.PATIENT_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }
      },
      {
        path: "doctors",
        loadChildren: () => import("./chunk-62N76AJU.js").then((m) => m.DOCTORS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Receptionist] }
      },
      {
        path: "appointments",
        loadChildren: () => import("./chunk-CEVYVUKA.js").then((m) => m.APPOINTMENTS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist, UserRole.Patient] }
      },
      {
        path: "medical-records",
        loadChildren: () => import("./chunk-BI322YSX.js").then((m) => m.MEDICAL_RECORDS_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor] }
      },
      {
        path: "schedules",
        loadChildren: () => import("./chunk-FLTDDNYX.js").then((m) => m.SCHEDULES_ROUTES),
        canActivate: [roleGuard],
        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }
      }
    ]
  }, false ? { \u0275entryName: "src/app/layouts/main-layout/main-layout.component.ts" } : {}),
  __spreadValues({
    path: "unauthorized",
    loadComponent: () => import("./chunk-Y54BQETF.js").then((m) => m.UnauthorizedComponent)
  }, false ? { \u0275entryName: "src/app/features/auth/unauthorized/unauthorized.component.ts" } : {}),
  __spreadValues({
    path: "**",
    loadComponent: () => import("./chunk-PHDRWZP6.js").then((m) => m.NotFoundComponent)
  }, false ? { \u0275entryName: "src/app/features/auth/not-found/not-found.component.ts" } : {})
];

// src/app/core/interceptors/auth.interceptor.ts
var authInterceptor = (req, next) => {
  const authService = inject(AuthService);
  const token = authService.getToken();
  if (token) {
    const cloned = req.clone({
      headers: req.headers.set("Authorization", `Bearer ${token}`)
    });
    return next(cloned);
  }
  return next(req);
};

// src/main.ts
bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor]))
  ]
}).catch((err) => console.error(err));
//# sourceMappingURL=main.js.map
