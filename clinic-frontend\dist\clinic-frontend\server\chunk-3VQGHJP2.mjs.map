{"version": 3, "sources": ["src/app/features/user-management/user-management.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const USER_MANAGEMENT_ROUTES: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./user-list/user-list.component').then(m => m.UserListComponent)\n  },\n  {\n    path: 'new',\n    loadComponent: () => import('./user-form/user-form.component').then(m => m.UserFormComponent)\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./user-detail/user-detail.component').then(m => m.UserDetailComponent)\n  },\n  {\n    path: ':id/edit',\n    loadComponent: () => import('./user-form/user-form.component').then(m => m.UserFormComponent)\n  }\n];\n"], "mappings": ";;;;;;AAEO,IAAM,yBAAiC;EAC5C;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAiC,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAE9F;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAiC,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAE9F;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAqC,EAAE,KAAK,OAAK,EAAE,mBAAmB;;EAEpG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAiC,EAAE,KAAK,OAAK,EAAE,iBAAiB;;;", "names": []}