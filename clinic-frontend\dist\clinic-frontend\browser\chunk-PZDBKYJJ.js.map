{"version": 3, "sources": ["src/app/features/auth/login/login.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\r\nimport { AuthService } from '../../../core/services/auth.service';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\r\n  template: `\r\n    <div class=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div class=\"max-w-md w-full space-y-8\">\r\n        <!-- Header -->\r\n        <div>\r\n          <div class=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100\">\r\n            <svg class=\"h-8 w-8 text-indigo-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n            </svg>\r\n          </div>\r\n          <h2 class=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\r\n            Sign in to your account\r\n          </h2>\r\n          <p class=\"mt-2 text-center text-sm text-gray-600\">\r\n            Welcome to Clinic Management System\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Error Alert -->\r\n        <div *ngIf=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4\">\r\n          <div class=\"flex\">\r\n            <div class=\"flex-shrink-0\">\r\n              <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n              </svg>\r\n            </div>\r\n            <div class=\"ml-3\">\r\n              <p class=\"text-sm text-red-800\">{{ error }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Login Form -->\r\n        <form class=\"mt-8 space-y-6\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n          <div class=\"space-y-4\">\r\n            <!-- Email Field -->\r\n            <div>\r\n              <label for=\"email\" class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Email address\r\n              </label>\r\n              <input\r\n                id=\"email\"\r\n                name=\"email\"\r\n                type=\"email\"\r\n                autocomplete=\"email\"\r\n                formControlName=\"email\"\r\n                class=\"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\r\n                [class.border-red-300]=\"f['email'].invalid && (f['email'].dirty || f['email'].touched || submitted)\"\r\n                placeholder=\"Enter your email address\"\r\n              />\r\n              <div *ngIf=\"f['email'].invalid && (f['email'].dirty || f['email'].touched || submitted)\" class=\"mt-1 text-sm text-red-600\">\r\n                <span *ngIf=\"f['email'].errors?.['required']\">Email is required</span>\r\n                <span *ngIf=\"f['email'].errors?.['email']\">Please enter a valid email address</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Password Field -->\r\n            <div>\r\n              <label for=\"password\" class=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Password\r\n              </label>\r\n              <input\r\n                id=\"password\"\r\n                name=\"password\"\r\n                type=\"password\"\r\n                autocomplete=\"current-password\"\r\n                formControlName=\"password\"\r\n                class=\"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\r\n                [class.border-red-300]=\"f['password'].invalid && (f['password'].dirty || f['password'].touched || submitted)\"\r\n                placeholder=\"Enter your password\"\r\n              />\r\n              <div *ngIf=\"f['password'].invalid && (f['password'].dirty || f['password'].touched || submitted)\" class=\"mt-1 text-sm text-red-600\">\r\n                Password is required\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <div>\r\n            <button\r\n              type=\"submit\"\r\n              [disabled]=\"loading\"\r\n              class=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\">\r\n              <span class=\"absolute left-0 inset-y-0 flex items-center pl-3\">\r\n                <svg *ngIf=\"!loading\" class=\"h-5 w-5 text-indigo-500 group-hover:text-indigo-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"/>\r\n                </svg>\r\n                <svg *ngIf=\"loading\" class=\"animate-spin h-5 w-5 text-indigo-500\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </span>\r\n              {{ loading ? 'Signing in...' : 'Sign in' }}\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Demo Credentials -->\r\n          <div class=\"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md\">\r\n            <h3 class=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials</h3>\r\n            <div class=\"text-xs text-blue-700 space-y-1\">\r\n              <p><strong>Admin:</strong> admin&#64;clinic.com / Admin123!</p>\r\n              <p><strong>Doctor:</strong> doctor&#64;clinic.com / Doctor123!</p>\r\n              <p><strong>Receptionist:</strong> receptionist&#64;clinic.com / Receptionist123!</p>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  `\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginForm: FormGroup;\r\n  loading = false;\r\n  submitted = false;\r\n  error = '';\r\n  returnUrl = '/dashboard';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {\r\n    // Redirect to home if already logged in\r\n    if (this.authService.isAuthenticated()) {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', Validators.required]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Get return url from route parameters or default to '/'\r\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\r\n  }\r\n\r\n  // Convenience getter for easy access to form fields\r\n  get f() { return this.loginForm.controls; }\r\n\r\n  onSubmit(): void {\r\n    this.submitted = true;\r\n\r\n    // Stop here if form is invalid\r\n    if (this.loginForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    this.authService.login({\r\n      email: this.f['email'].value,\r\n      password: this.f['password'].value\r\n    })\r\n    .pipe(\r\n      finalize(() => {\r\n        this.loading = false;\r\n      })\r\n    )\r\n    .subscribe({\r\n      next: () => {\r\n        this.router.navigate([this.returnUrl]);\r\n      },\r\n      error: error => {\r\n        this.error = error.message || 'Invalid credentials';\r\n      }\r\n    });\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BQ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0E,GAAA,OAAA,EAAA,EACtD,GAAA,OAAA,EAAA;;AAEd,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkB,GAAA,KAAA,EAAA;AACgB,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA,EAAI,EAC3C,EACF;;;;AAF8B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;AAwB9B,IAAA,yBAAA,GAAA,MAAA;AAA8C,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;;;;;AAC/D,IAAA,yBAAA,GAAA,MAAA;AAA2C,IAAA,iBAAA,GAAA,oCAAA;AAAkC,IAAA,uBAAA;;;;;AAF/E,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,uCAAA,GAAA,GAAA,QAAA,EAAA,EAA8C,GAAA,uCAAA,GAAA,GAAA,QAAA,EAAA;AAEhD,IAAA,uBAAA;;;;AAFS,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,EAAA,OAAA,EAAA,UAAA,OAAA,OAAA,OAAA,EAAA,OAAA,EAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,EAAA,OAAA,EAAA,UAAA,OAAA,OAAA,OAAA,EAAA,OAAA,EAAA,OAAA,OAAA,CAAA;;;;;AAmBT,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,wBAAA;AACF,IAAA,uBAAA;;;;;;AAWE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAkG,GAAA,QAAA,EAAA;AAEpG,IAAA,uBAAA;;;AAoBV,IAAO,iBAAP,MAAO,gBAAc;EAQf;EACA;EACA;EACA;EAVV;EACA,UAAU;EACV,YAAY;EACZ,QAAQ;EACR,YAAY;EAEZ,YACU,aACA,OACA,QACA,aAAwB;AAHxB,SAAA,cAAA;AACA,SAAA,QAAA;AACA,SAAA,SAAA;AACA,SAAA,cAAA;AAGR,QAAI,KAAK,YAAY,gBAAe,GAAI;AACtC,WAAK,OAAO,SAAS,CAAC,YAAY,CAAC;IACrC;AAEA,SAAK,YAAY,KAAK,YAAY,MAAM;MACtC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,UAAU,CAAC,IAAI,WAAW,QAAQ;KACnC;EACH;EAEA,WAAQ;AAEN,SAAK,YAAY,KAAK,MAAM,SAAS,YAAY,WAAW,KAAK;EACnE;;EAGA,IAAI,IAAC;AAAK,WAAO,KAAK,UAAU;EAAU;EAE1C,WAAQ;AACN,SAAK,YAAY;AAGjB,QAAI,KAAK,UAAU,SAAS;AAC1B;IACF;AAEA,SAAK,UAAU;AACf,SAAK,QAAQ;AAEb,SAAK,YAAY,MAAM;MACrB,OAAO,KAAK,EAAE,OAAO,EAAE;MACvB,UAAU,KAAK,EAAE,UAAU,EAAE;KAC9B,EACA,KACC,SAAS,MAAK;AACZ,WAAK,UAAU;IACjB,CAAC,CAAC,EAEH,UAAU;MACT,MAAM,MAAK;AACT,aAAK,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC;MACvC;MACA,OAAO,WAAQ;AACb,aAAK,QAAQ,MAAM,WAAW;MAChC;KACD;EACH;;qCA5DW,iBAAc,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,gBAAA,QAAA,gBAAA,kBAAA,cAAA,SAAA,QAAA,WAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,WAAA,GAAA,CAAA,GAAA,WAAA,QAAA,QAAA,QAAA,gBAAA,kBAAA,gBAAA,eAAA,GAAA,CAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,OAAA,OAAA,iBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,+CAAA,GAAA,CAAA,GAAA,QAAA,eAAA,YAAA,kBAAA,eAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,eAAA,GAAA,CAAA,SAAA,kDAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,aAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,OAAA,SAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,SAAA,QAAA,SAAA,QAAA,SAAA,gBAAA,SAAA,mBAAA,SAAA,eAAA,4BAAA,GAAA,mBAAA,YAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,wBAAA,iBAAA,cAAA,sBAAA,yBAAA,2BAAA,cAAA,YAAA,GAAA,CAAA,SAAA,6BAAA,GAAA,MAAA,GAAA,CAAA,OAAA,YAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,YAAA,QAAA,YAAA,QAAA,YAAA,gBAAA,oBAAA,mBAAA,YAAA,eAAA,uBAAA,GAAA,mBAAA,YAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,wBAAA,iBAAA,cAAA,sBAAA,yBAAA,2BAAA,cAAA,YAAA,GAAA,CAAA,QAAA,UAAA,GAAA,SAAA,YAAA,UAAA,QAAA,kBAAA,QAAA,QAAA,UAAA,sBAAA,WAAA,eAAA,cAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,uBAAA,+BAAA,qBAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,UAAA,aAAA,QAAA,gBAAA,MAAA,GAAA,CAAA,SAAA,uDAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,MAAA,GAAA,CAAA,SAAA,wCAAA,QAAA,QAAA,WAAA,aAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,OAAA,cAAA,UAAA,mBAAA,YAAA,GAAA,CAAA,GAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,WAAA,iBAAA,WAAA,GAAA,CAAA,GAAA,aAAA,UAAA,kBAAA,cAAA,KAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,OAAA,OAAA,cAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,mDAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,cAAA,GAAA,CAAA,GAAA,QAAA,WAAA,cAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,OAAA,OAAA,mBAAA,6BAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,8FAAA,GAAA,CAAA,QAAA,QAAA,WAAA,aAAA,GAAA,gBAAA,OAAA,OAAA,iBAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,YAAA,GAAA,CAAA,QAAA,gBAAA,KAAA,mHAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA7GvB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiG,GAAA,OAAA,CAAA,EACxD,GAAA,KAAA,EAEhC,GAAA,OAAA,CAAA;;AAED,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA,EAAM;;AAER,MAAA,yBAAA,GAAA,MAAA,CAAA;AACE,MAAA,iBAAA,GAAA,2BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,GAAA,uCAAA;AACF,MAAA,uBAAA,EAAI;AAIN,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,CAAA;AAcA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAqD,MAAA,qBAAA,YAAA,SAAA,oDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AACzE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,KAAA,EAEhB,IAAA,SAAA,EAAA;AAED,MAAA,iBAAA,IAAA,iBAAA;AACF,MAAA,uBAAA;AACA,MAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA;AAIF,MAAA,uBAAA;AAGA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,MAAA,iBAAA,IAAA,YAAA;AACF,MAAA,uBAAA;AACA,MAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,UAAA,EAAA,EAI0T,IAAA,QAAA,EAAA;AAEzT,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAAwI,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AAO1I,MAAA,uBAAA;AACA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS;AAIX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmE,IAAA,MAAA,EAAA;AACd,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACnE,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6C,IAAA,GAAA,EACxC,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAAU,MAAA,iBAAA,IAAA,+BAAA;AAAgC,MAAA,uBAAA;AAC3D,MAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAAU,MAAA,iBAAA,IAAA,iCAAA;AAAkC,MAAA,uBAAA;AAC9D,MAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AAAU,MAAA,iBAAA,IAAA,6CAAA;AAA8C,MAAA,uBAAA,EAAI,EAChF,EACF,EACD,EACH;;;AAvFE,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAcuB,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,SAAA;AAcrB,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,kBAAA,IAAA,EAAA,OAAA,EAAA,YAAA,IAAA,EAAA,OAAA,EAAA,SAAA,IAAA,EAAA,OAAA,EAAA,WAAA,IAAA,UAAA;AAGI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,EAAA,OAAA,EAAA,YAAA,IAAA,EAAA,OAAA,EAAA,SAAA,IAAA,EAAA,OAAA,EAAA,WAAA,IAAA,UAAA;AAkBJ,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,kBAAA,IAAA,EAAA,UAAA,EAAA,YAAA,IAAA,EAAA,UAAA,EAAA,SAAA,IAAA,EAAA,UAAA,EAAA,WAAA,IAAA,UAAA;AAGI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,EAAA,UAAA,EAAA,YAAA,IAAA,EAAA,UAAA,EAAA,SAAA,IAAA,EAAA,UAAA,EAAA,WAAA,IAAA,UAAA;AAUN,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,OAAA;AAGQ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKR,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,UAAA,kBAAA,WAAA,GAAA;;oBA7FF,cAAY,MAAE,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,iBAAE,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEA+G9C,gBAAc,CAAA;UAlH1B;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,cAAc,qBAAqB,YAAY;MACzD,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6GX;;;;6EACY,gBAAc,EAAA,WAAA,kBAAA,UAAA,kDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}