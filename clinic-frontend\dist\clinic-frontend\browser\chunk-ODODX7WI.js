import {
  UserRole
} from "./chunk-K2W23EYU.js";
import {
  AuthService
} from "./chunk-E4RC7AJA.js";
import {
  PatientService
} from "./chunk-J5PIZTPU.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate2
} from "./chunk-BMSBKD5S.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/dashboard/dashboard.component.ts
function DashboardComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 5)(1, "h2", 6);
    \u0275\u0275text(2, "Admin Quick Actions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 7)(4, "button", 8);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 9);
    \u0275\u0275element(6, "path", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(7, "span");
    \u0275\u0275text(8, "Create New User");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "button", 11);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(10, "svg", 9);
    \u0275\u0275element(11, "path", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(12, "span");
    \u0275\u0275text(13, "Manage Users");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(14, "button", 13);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(15, "svg", 9);
    \u0275\u0275element(16, "path", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(17, "span");
    \u0275\u0275text(18, "Add New Patient");
    \u0275\u0275elementEnd()()()();
  }
}
function DashboardComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14);
    \u0275\u0275element(1, "div", 15);
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_5_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22);
    \u0275\u0275text(1, " No patients found. ");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_5_div_7_tr_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr")(1, "td", 29)(2, "div", 30);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "td", 29)(5, "div", 31);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "td", 29)(8, "div", 31);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const patient_r1 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2("", patient_r1.firstName, " ", patient_r1.lastName, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(patient_r1.email);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(patient_r1.phoneNumber);
  }
}
function DashboardComponent_div_5_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23)(1, "table", 24)(2, "thead", 25)(3, "tr")(4, "th", 26);
    \u0275\u0275text(5, "Name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th", 26);
    \u0275\u0275text(7, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th", 26);
    \u0275\u0275text(9, "Phone");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(10, "tbody", 27);
    \u0275\u0275template(11, DashboardComponent_div_5_div_7_tr_11_Template, 10, 4, "tr", 28);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(11);
    \u0275\u0275property("ngForOf", ctx_r1.recentPatients);
  }
}
function DashboardComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 16)(2, "div", 17)(3, "h2", 18);
    \u0275\u0275text(4, "Recent Patients");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "div", 19);
    \u0275\u0275template(6, DashboardComponent_div_5_div_6_Template, 2, 0, "div", 20)(7, DashboardComponent_div_5_div_7_Template, 12, 1, "div", 21);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", ctx_r1.recentPatients.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.recentPatients.length > 0);
  }
}
var DashboardComponent = class _DashboardComponent {
  patientService;
  authService;
  recentPatients = [];
  loading = false;
  error = "";
  constructor(patientService, authService) {
    this.patientService = patientService;
    this.authService = authService;
  }
  ngOnInit() {
    this.loadRecentPatients();
  }
  loadRecentPatients() {
    this.loading = true;
    this.error = "";
    this.patientService.getAll(1, 5).subscribe({
      next: (patients) => {
        this.recentPatients = patients;
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load recent patients. Please try again.";
        this.loading = false;
        console.error("Error loading recent patients:", error);
      }
    });
  }
  isAdmin() {
    return this.authService.hasRole([UserRole.Admin]);
  }
  static \u0275fac = function DashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardComponent)(\u0275\u0275directiveInject(PatientService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardComponent, selectors: [["app-dashboard"]], decls: 6, vars: 3, consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "text-3xl", "font-bold", "text-gray-900", "mb-6"], ["class", "mb-8", 4, "ngIf"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], [4, "ngIf"], [1, "mb-8"], [1, "text-xl", "font-semibold", "text-gray-800", "mb-4"], [1, "grid", "grid-cols-1", "md:grid-cols-3", "gap-4"], ["routerLink", "/user-management/new", 1, "bg-primary-600", "hover:bg-primary-700", "text-white", "p-4", "rounded-lg", "flex", "items-center", "justify-center", "space-x-2", "transition-colors"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"], ["routerLink", "/user-management", 1, "bg-indigo-600", "hover:bg-indigo-700", "text-white", "p-4", "rounded-lg", "flex", "items-center", "justify-center", "space-x-2", "transition-colors"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"], ["routerLink", "/patients/new", 1, "bg-green-600", "hover:bg-green-700", "text-white", "p-4", "rounded-lg", "flex", "items-center", "justify-center", "space-x-2", "transition-colors"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "bg-white", "rounded-lg", "shadow", "mb-8"], [1, "px-6", "py-4", "border-b", "border-gray-200"], [1, "text-xl", "font-semibold", "text-gray-800"], [1, "p-6"], ["class", "text-center py-6 text-gray-500", 4, "ngIf"], ["class", "overflow-x-auto", 4, "ngIf"], [1, "text-center", "py-6", "text-gray-500"], [1, "overflow-x-auto"], [1, "min-w-full", "divide-y", "divide-gray-200"], [1, "bg-gray-50"], ["scope", "col", 1, "px-6", "py-3", "text-left", "text-xs", "font-medium", "text-gray-500", "uppercase", "tracking-wider"], [1, "bg-white", "divide-y", "divide-gray-200"], [4, "ngFor", "ngForOf"], [1, "px-6", "py-4", "whitespace-nowrap"], [1, "text-sm", "font-medium", "text-gray-900"], [1, "text-sm", "text-gray-900"]], template: function DashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2, "Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275template(3, DashboardComponent_div_3_Template, 19, 0, "div", 2)(4, DashboardComponent_div_4_Template, 2, 0, "div", 3)(5, DashboardComponent_div_5_Template, 8, 2, "div", 4);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.isAdmin());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule, RouterLink], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto px-4 py-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-6">Dashboard</h1>

      <!-- Admin Quick Actions -->
      <div *ngIf="isAdmin()" class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Admin Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            routerLink="/user-management/new"
            class="bg-primary-600 hover:bg-primary-700 text-white p-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            <span>Create New User</span>
          </button>
          <button
            routerLink="/user-management"
            class="bg-indigo-600 hover:bg-indigo-700 text-white p-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <span>Manage Users</span>
          </button>
          <button
            routerLink="/patients/new"
            class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            <span>Add New Patient</span>
          </button>
        </div>
      </div>

      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>

      <div *ngIf="!loading">
        <!-- Recent Patients -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800">Recent Patients</h2>
          </div>
          <div class="p-6">
            <div *ngIf="recentPatients.length === 0" class="text-center py-6 text-gray-500">
              No patients found.
            </div>
            <div *ngIf="recentPatients.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr *ngFor="let patient of recentPatients">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900">{{ patient.firstName }} {{ patient.lastName }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ patient.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{{ patient.phoneNumber }}</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;219558ef63f119a92210704329b58a3cdceaa4fb296db559e672f74512827dc7;F:/clinc/clinic-frontend/src/app/features/dashboard/dashboard.component.ts */\n:host {\n  display: block;\n}\n/*# sourceMappingURL=dashboard.component.css.map */\n"] }]
  }], () => [{ type: PatientService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardComponent, { className: "DashboardComponent", filePath: "src/app/features/dashboard/dashboard.component.ts", lineNumber: 100 });
})();
export {
  DashboardComponent
};
//# sourceMappingURL=chunk-ODODX7WI.js.map
