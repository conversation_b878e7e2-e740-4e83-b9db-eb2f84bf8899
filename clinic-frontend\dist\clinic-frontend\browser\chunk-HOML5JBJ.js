import {
  UserManagementService
} from "./chunk-BOTCETOX.js";
import {
  UserRole
} from "./chunk-K2W23EYU.js";
import "./chunk-7NNESOLN.js";
import {
  ActivatedRoute,
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgIf,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-BMSBKD5S.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/user-management/user-detail/user-detail.component.ts
var _c0 = (a0) => ["/user-management", a0, "edit"];
function UserDetailComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275element(1, "div", 11);
    \u0275\u0275elementEnd();
  }
}
function UserDetailComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function UserDetailComponent_div_10_button_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 34);
    \u0275\u0275listener("click", function UserDetailComponent_div_10_button_18_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deactivateUser());
    });
    \u0275\u0275text(1, " Deactivate ");
    \u0275\u0275elementEnd();
  }
}
function UserDetailComponent_div_10_button_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 35);
    \u0275\u0275listener("click", function UserDetailComponent_div_10_button_19_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.activateUser());
    });
    \u0275\u0275text(1, " Activate ");
    \u0275\u0275elementEnd();
  }
}
function UserDetailComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15)(3, "div", 16)(4, "div", 17)(5, "div", 18);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 19)(8, "h2", 20);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 21)(11, "span", 22);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 23);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(15, "div", 24)(16, "button", 25);
    \u0275\u0275text(17, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275template(18, UserDetailComponent_div_10_button_18_Template, 2, 0, "button", 26)(19, UserDetailComponent_div_10_button_19_Template, 2, 0, "button", 27);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(20, "div", 28)(21, "div", 29)(22, "div")(23, "h3", 30);
    \u0275\u0275text(24, "Personal Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "dl", 31)(26, "div")(27, "dt", 32);
    \u0275\u0275text(28, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "dd", 33);
    \u0275\u0275text(30);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(31, "div")(32, "dt", 32);
    \u0275\u0275text(33, "First Name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(34, "dd", 33);
    \u0275\u0275text(35);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(36, "div")(37, "dt", 32);
    \u0275\u0275text(38, "Last Name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(39, "dd", 33);
    \u0275\u0275text(40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(41, "div")(42, "dt", 32);
    \u0275\u0275text(43, "Role");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "dd", 33);
    \u0275\u0275text(45);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(46, "div")(47, "h3", 30);
    \u0275\u0275text(48, "Account Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "dl", 31)(50, "div")(51, "dt", 32);
    \u0275\u0275text(52, "Status");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(53, "dd", 33)(54, "span", 22);
    \u0275\u0275text(55);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(56, "div")(57, "dt", 32);
    \u0275\u0275text(58, "Created At");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(59, "dd", 33);
    \u0275\u0275text(60);
    \u0275\u0275pipe(61, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(62, "div")(63, "dt", 32);
    \u0275\u0275text(64, "Last Login");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(65, "dd", 33);
    \u0275\u0275text(66);
    \u0275\u0275pipe(67, "date");
    \u0275\u0275elementEnd()()()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate2(" ", ctx_r0.user.firstName.charAt(0), "", ctx_r0.user.lastName.charAt(0), " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" ", ctx_r0.user.firstName, " ", ctx_r0.user.lastName, " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", ctx_r0.getRoleBadgeClass(ctx_r0.user.role));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.user.role, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", ctx_r0.user.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.user.isActive ? "Active" : "Inactive", " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(25, _c0, ctx_r0.user.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.user.isActive);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.user.isActive);
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate(ctx_r0.user.email);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.user.firstName);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.user.lastName);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.user.role);
    \u0275\u0275advance(9);
    \u0275\u0275property("ngClass", ctx_r0.user.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.user.isActive ? "Active" : "Inactive", " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(61, 19, ctx_r0.user.createdAt, "medium"));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", ctx_r0.user.lastLoginDate ? \u0275\u0275pipeBind2(67, 22, ctx_r0.user.lastLoginDate, "medium") : "Never", " ");
  }
}
var UserDetailComponent = class _UserDetailComponent {
  userManagementService;
  route;
  user = null;
  loading = false;
  error = "";
  constructor(userManagementService, route) {
    this.userManagementService = userManagementService;
    this.route = route;
  }
  ngOnInit() {
    const idParam = this.route.snapshot.paramMap.get("id");
    if (idParam) {
      const userId = parseInt(idParam, 10);
      this.loadUser(userId);
    }
  }
  loadUser(id) {
    this.loading = true;
    this.error = "";
    this.userManagementService.getUserById(id).subscribe({
      next: (user) => {
        this.user = user;
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load user details. Please try again.";
        this.loading = false;
        console.error("Error loading user:", error);
      }
    });
  }
  getRoleBadgeClass(role) {
    switch (role) {
      case UserRole.Admin:
        return "bg-purple-100 text-purple-800";
      case UserRole.Doctor:
        return "bg-blue-100 text-blue-800";
      case UserRole.Receptionist:
        return "bg-green-100 text-green-800";
      case UserRole.Patient:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }
  deactivateUser() {
    if (this.user && confirm("Are you sure you want to deactivate this user?")) {
      this.userManagementService.deactivateUser(this.user.id).subscribe({
        next: () => {
          if (this.user) {
            this.user.isActive = false;
          }
        },
        error: (error) => {
          this.error = "Failed to deactivate user. Please try again.";
          console.error("Error deactivating user:", error);
        }
      });
    }
  }
  activateUser() {
    if (this.user && confirm("Are you sure you want to activate this user?")) {
      this.userManagementService.activateUser(this.user.id).subscribe({
        next: () => {
          if (this.user) {
            this.user.isActive = true;
          }
        },
        error: (error) => {
          this.error = "Failed to activate user. Please try again.";
          console.error("Error activating user:", error);
        }
      });
    }
  }
  static \u0275fac = function UserDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserDetailComponent)(\u0275\u0275directiveInject(UserManagementService), \u0275\u0275directiveInject(ActivatedRoute));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserDetailComponent, selectors: [["app-user-detail"]], decls: 11, vars: 3, consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "max-w-3xl", "mx-auto"], [1, "flex", "items-center", "mb-6"], ["routerLink", "/user-management", 1, "mr-4", "text-gray-600", "hover:text-gray-900"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 19l-7-7 7-7"], [1, "text-3xl", "font-bold", "text-gray-900"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], ["class", "bg-white shadow rounded-lg overflow-hidden", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "bg-red-50", "border", "border-red-200", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "bg-white", "shadow", "rounded-lg", "overflow-hidden"], [1, "px-6", "py-4", "border-b", "border-gray-200"], [1, "flex", "justify-between", "items-center"], [1, "flex", "items-center"], [1, "flex-shrink-0", "h-16", "w-16"], [1, "h-16", "w-16", "rounded-full", "bg-primary-500", "flex", "items-center", "justify-center", "text-white", "text-xl", "font-medium"], [1, "ml-4"], [1, "text-2xl", "font-bold", "text-gray-900"], [1, "flex", "items-center", "mt-1"], [1, "inline-flex", "px-2", "py-1", "text-xs", "font-semibold", "rounded-full", 3, "ngClass"], [1, "ml-2", "inline-flex", "px-2", "py-1", "text-xs", "font-semibold", "rounded-full", 3, "ngClass"], [1, "flex", "space-x-3"], [1, "px-4", "py-2", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", 3, "routerLink"], ["class", "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700", 3, "click", 4, "ngIf"], ["class", "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700", 3, "click", 4, "ngIf"], [1, "px-6", "py-4"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], [1, "text-lg", "font-medium", "text-gray-900", "mb-4"], [1, "space-y-3"], [1, "text-sm", "font-medium", "text-gray-500"], [1, "mt-1", "text-sm", "text-gray-900"], [1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-red-600", "hover:bg-red-700", 3, "click"], [1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-green-600", "hover:bg-green-700", 3, "click"]], template: function UserDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "button", 3);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 4);
      \u0275\u0275element(5, "path", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "h1", 6);
      \u0275\u0275text(7, "User Details");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(8, UserDetailComponent_div_8_Template, 2, 0, "div", 7)(9, UserDetailComponent_div_9_Template, 2, 1, "div", 8)(10, UserDetailComponent_div_10_Template, 68, 27, "div", 9);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.user);
    }
  }, dependencies: [CommonModule, NgClass, NgIf, DatePipe, RouterModule, RouterLink], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=user-detail.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserDetailComponent, [{
    type: Component,
    args: [{ selector: "app-user-detail", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-3xl mx-auto">
        <div class="flex items-center mb-6">
          <button 
            routerLink="/user-management"
            class="mr-4 text-gray-600 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-3xl font-bold text-gray-900">User Details</h1>
        </div>

        <div *ngIf="loading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {{ error }}
        </div>

        <div *ngIf="!loading && user" class="bg-white shadow rounded-lg overflow-hidden">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-16 w-16">
                  <div class="h-16 w-16 rounded-full bg-primary-500 flex items-center justify-center text-white text-xl font-medium">
                    {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
                  </div>
                </div>
                <div class="ml-4">
                  <h2 class="text-2xl font-bold text-gray-900">
                    {{ user.firstName }} {{ user.lastName }}
                  </h2>
                  <div class="flex items-center mt-1">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          [ngClass]="getRoleBadgeClass(user.role)">
                      {{ user.role }}
                    </span>
                    <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          [ngClass]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                      {{ user.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex space-x-3">
                <button
                  [routerLink]="['/user-management', user.id, 'edit']"
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Edit
                </button>
                <button
                  *ngIf="user.isActive"
                  (click)="deactivateUser()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  Deactivate
                </button>
                <button
                  *ngIf="!user.isActive"
                  (click)="activateUser()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Activate
                </button>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Personal Information -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.email }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">First Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.firstName }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Last Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.lastName }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Role</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.role }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Account Information -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                            [ngClass]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                        {{ user.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.createdAt | date:'medium' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Last Login</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      {{ user.lastLoginDate ? (user.lastLoginDate | date:'medium') : 'Never' }}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;219558ef63f119a92210704329b58a3cdceaa4fb296db559e672f74512827dc7;F:/clinc/clinic-frontend/src/app/features/user-management/user-detail/user-detail.component.ts */\n:host {\n  display: block;\n}\n/*# sourceMappingURL=user-detail.component.css.map */\n"] }]
  }], () => [{ type: UserManagementService }, { type: ActivatedRoute }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserDetailComponent, { className: "UserDetailComponent", filePath: "src/app/features/user-management/user-detail/user-detail.component.ts", lineNumber: 149 });
})();
export {
  UserDetailComponent
};
//# sourceMappingURL=chunk-HOML5JBJ.js.map
