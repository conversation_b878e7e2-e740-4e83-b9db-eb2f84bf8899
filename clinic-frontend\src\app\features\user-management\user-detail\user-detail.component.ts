import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { UserManagementService } from '../services/user-management.service';
import { UserManagement } from '../models/user-management.model';
import { UserRole } from '../../../core/models/auth.model';

@Component({
  selector: 'app-user-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-3xl mx-auto">
        <div class="flex items-center mb-6">
          <button 
            routerLink="/user-management"
            class="mr-4 text-gray-600 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-3xl font-bold text-gray-900">User Details</h1>
        </div>

        <div *ngIf="loading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {{ error }}
        </div>

        <div *ngIf="!loading && user" class="bg-white shadow rounded-lg overflow-hidden">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-16 w-16">
                  <div class="h-16 w-16 rounded-full bg-primary-500 flex items-center justify-center text-white text-xl font-medium">
                    {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
                  </div>
                </div>
                <div class="ml-4">
                  <h2 class="text-2xl font-bold text-gray-900">
                    {{ user.firstName }} {{ user.lastName }}
                  </h2>
                  <div class="flex items-center mt-1">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          [ngClass]="getRoleBadgeClass(user.role)">
                      {{ user.role }}
                    </span>
                    <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          [ngClass]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                      {{ user.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="flex space-x-3">
                <button
                  [routerLink]="['/user-management', user.id, 'edit']"
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Edit
                </button>
                <button
                  *ngIf="user.isActive"
                  (click)="deactivateUser()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  Deactivate
                </button>
                <button
                  *ngIf="!user.isActive"
                  (click)="activateUser()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                >
                  Activate
                </button>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Personal Information -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.email }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">First Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.firstName }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Last Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.lastName }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Role</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.role }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Account Information -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                            [ngClass]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                        {{ user.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ user.createdAt | date:'medium' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Last Login</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                      {{ user.lastLoginDate ? (user.lastLoginDate | date:'medium') : 'Never' }}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class UserDetailComponent implements OnInit {
  user: UserManagement | null = null;
  loading = false;
  error = '';

  constructor(
    private userManagementService: UserManagementService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    if (idParam) {
      const userId = parseInt(idParam, 10);
      this.loadUser(userId);
    }
  }

  private loadUser(id: number): void {
    this.loading = true;
    this.error = '';

    this.userManagementService.getUserById(id).subscribe({
      next: (user: UserManagement) => {
        this.user = user;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load user details. Please try again.';
        this.loading = false;
        console.error('Error loading user:', error);
      }
    });
  }

  getRoleBadgeClass(role: UserRole): string {
    switch (role) {
      case UserRole.Admin:
        return 'bg-purple-100 text-purple-800';
      case UserRole.Doctor:
        return 'bg-blue-100 text-blue-800';
      case UserRole.Receptionist:
        return 'bg-green-100 text-green-800';
      case UserRole.Patient:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  deactivateUser(): void {
    if (this.user && confirm('Are you sure you want to deactivate this user?')) {
      this.userManagementService.deactivateUser(this.user.id).subscribe({
        next: () => {
          if (this.user) {
            this.user.isActive = false;
          }
        },
        error: (error: any) => {
          this.error = 'Failed to deactivate user. Please try again.';
          console.error('Error deactivating user:', error);
        }
      });
    }
  }

  activateUser(): void {
    if (this.user && confirm('Are you sure you want to activate this user?')) {
      this.userManagementService.activateUser(this.user.id).subscribe({
        next: () => {
          if (this.user) {
            this.user.isActive = true;
          }
        },
        error: (error: any) => {
          this.error = 'Failed to activate user. Please try again.';
          console.error('Error activating user:', error);
        }
      });
    }
  }
}
