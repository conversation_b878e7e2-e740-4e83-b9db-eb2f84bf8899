{"version": 3, "sources": ["src/app/features/doctors/doctor-form/doctor-form.component.ts", "src/app/features/doctors/doctor-form/doctor-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { DoctorService } from '../services/doctor.service';\r\n\r\n@Component({\r\n  selector: 'app-doctor-form',\r\n  standalone: true,\r\n  imports: [CommonModule, ReactiveFormsModule],\r\n  templateUrl: './doctor-form.component.html',\r\n  styleUrls: ['./doctor-form.component.scss']\r\n})\r\nexport class DoctorFormComponent implements OnInit {\r\n  doctorForm: FormGroup;\r\n  isEditMode = false;\r\n  doctorId: string | null = null;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private doctorService: DoctorService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) {\r\n    this.doctorForm = this.fb.group({\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phoneNumber: ['', Validators.required],\r\n      specialization: ['', Validators.required],\r\n      licenseNumber: ['', Validators.required]\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.doctorId = this.route.snapshot.paramMap.get('id');\r\n    if (this.doctorId) {\r\n      this.isEditMode = true;\r\n      this.loadDoctor();\r\n    }\r\n  }\r\n\r\n  private loadDoctor(): void {\r\n    if (this.doctorId) {\r\n      this.doctorService.getDoctor(this.doctorId).subscribe({\r\n        next: (doctor) => {\r\n          this.doctorForm.patchValue(doctor);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading doctor:', error);\r\n          this.router.navigate(['/doctors']);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.doctorForm.valid) {\r\n      const doctorData = this.doctorForm.value;\r\n      \r\n      if (this.isEditMode && this.doctorId) {\r\n        this.doctorService.updateDoctor(this.doctorId, doctorData).subscribe({\r\n          next: () => {\r\n            this.router.navigate(['/doctors']);\r\n          },\r\n          error: (error) => {\r\n            console.error('Error updating doctor:', error);\r\n          }\r\n        });\r\n      } else {\r\n        this.doctorService.createDoctor(doctorData).subscribe({\r\n          next: () => {\r\n            this.router.navigate(['/doctors']);\r\n          },\r\n          error: (error) => {\r\n            console.error('Error creating doctor:', error);\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto px-4 py-8\">\r\n  <h1 class=\"text-2xl font-bold mb-6\">{{ isEditMode ? 'Edit Doctor' : 'Add New Doctor' }}</h1>\r\n\r\n  <form [formGroup]=\"doctorForm\" (ngSubmit)=\"onSubmit()\" class=\"max-w-2xl\">\r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n      <div class=\"form-group\">\r\n        <label for=\"firstName\" class=\"block text-sm font-medium text-gray-700\">First Name</label>\r\n        <input\r\n          type=\"text\"\r\n          id=\"firstName\"\r\n          formControlName=\"firstName\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"lastName\" class=\"block text-sm font-medium text-gray-700\">Last Name</label>\r\n        <input\r\n          type=\"text\"\r\n          id=\"lastName\"\r\n          formControlName=\"lastName\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\r\n        <input\r\n          type=\"email\"\r\n          id=\"email\"\r\n          formControlName=\"email\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"phoneNumber\" class=\"block text-sm font-medium text-gray-700\">Phone Number</label>\r\n        <input\r\n          type=\"tel\"\r\n          id=\"phoneNumber\"\r\n          formControlName=\"phoneNumber\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"specialization\" class=\"block text-sm font-medium text-gray-700\">Specialization</label>\r\n        <input\r\n          type=\"text\"\r\n          id=\"specialization\"\r\n          formControlName=\"specialization\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"licenseNumber\" class=\"block text-sm font-medium text-gray-700\">License Number</label>\r\n        <input\r\n          type=\"text\"\r\n          id=\"licenseNumber\"\r\n          formControlName=\"licenseNumber\"\r\n          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"\r\n        >\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"mt-6 flex justify-end space-x-4\">\r\n      <button\r\n        type=\"button\"\r\n        (click)=\"router.navigate(['/doctors'])\"\r\n        class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\r\n      >\r\n        Cancel\r\n      </button>\r\n      <button\r\n        type=\"submit\"\r\n        [disabled]=\"!doctorForm.valid\"\r\n        class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\r\n      >\r\n        {{ isEditMode ? 'Update' : 'Create' }}\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAO,sBAAP,MAAO,qBAAmB;EAMpB;EACA;EACA;EACD;EART;EACA,aAAa;EACb,WAA0B;EAE1B,YACU,IACA,eACA,OACD,QAAc;AAHb,SAAA,KAAA;AACA,SAAA,gBAAA;AACA,SAAA,QAAA;AACD,SAAA,SAAA;AAEP,SAAK,aAAa,KAAK,GAAG,MAAM;MAC9B,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,UAAU,CAAC,IAAI,WAAW,QAAQ;MAClC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,aAAa,CAAC,IAAI,WAAW,QAAQ;MACrC,gBAAgB,CAAC,IAAI,WAAW,QAAQ;MACxC,eAAe,CAAC,IAAI,WAAW,QAAQ;KACxC;EACH;EAEA,WAAQ;AACN,SAAK,WAAW,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AACrD,QAAI,KAAK,UAAU;AACjB,WAAK,aAAa;AAClB,WAAK,WAAU;IACjB;EACF;EAEQ,aAAU;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,cAAc,UAAU,KAAK,QAAQ,EAAE,UAAU;QACpD,MAAM,CAAC,WAAU;AACf,eAAK,WAAW,WAAW,MAAM;QACnC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yBAAyB,KAAK;AAC5C,eAAK,OAAO,SAAS,CAAC,UAAU,CAAC;QACnC;OACD;IACH;EACF;EAEA,WAAQ;AACN,QAAI,KAAK,WAAW,OAAO;AACzB,YAAM,aAAa,KAAK,WAAW;AAEnC,UAAI,KAAK,cAAc,KAAK,UAAU;AACpC,aAAK,cAAc,aAAa,KAAK,UAAU,UAAU,EAAE,UAAU;UACnE,MAAM,MAAK;AACT,iBAAK,OAAO,SAAS,CAAC,UAAU,CAAC;UACnC;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,0BAA0B,KAAK;UAC/C;SACD;MACH,OAAO;AACL,aAAK,cAAc,aAAa,UAAU,EAAE,UAAU;UACpD,MAAM,MAAK;AACT,iBAAK,OAAO,SAAS,CAAC,UAAU,CAAC;UACnC;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,0BAA0B,KAAK;UAC/C;SACD;MACH;IACF;EACF;;qCAnEW,sBAAmB,4BAAA,WAAA,GAAA,4BAAA,aAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,GAAA,YAAA,aAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,QAAA,eAAA,kBAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,aAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,aAAA,mBAAA,aAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,OAAA,YAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,YAAA,mBAAA,YAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,OAAA,SAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,SAAA,MAAA,SAAA,mBAAA,SAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,OAAA,eAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,OAAA,MAAA,eAAA,mBAAA,eAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,OAAA,kBAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,kBAAA,mBAAA,kBAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,OAAA,iBAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,iBAAA,mBAAA,iBAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,GAAA,QAAA,QAAA,eAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,WAAA,eAAA,iBAAA,oBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,uBAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACbhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,MAAA,CAAA;AACH,MAAA,iBAAA,CAAA;AAAmD,MAAA,uBAAA;AAEvF,MAAA,yBAAA,GAAA,QAAA,CAAA;AAA+B,MAAA,qBAAA,YAAA,SAAA,wDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AACnD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmD,GAAA,OAAA,CAAA,EACzB,GAAA,SAAA,CAAA;AACiD,MAAA,iBAAA,GAAA,YAAA;AAAU,MAAA,uBAAA;AACjF,MAAA,oBAAA,GAAA,SAAA,CAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACgD,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC/E,MAAA,oBAAA,IAAA,SAAA,CAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AAC6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACxE,MAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACmD,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACrF,MAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACsD,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC1F,MAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACqD,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AACzF,MAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6C,IAAA,UAAA,EAAA;AAGzC,MAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,eAAS,IAAA,OAAA,SAAA,CAAiB,UAAU,CAAA;MAAE,CAAA;AAGtC,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAKE,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD;;;AAjF6B,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,aAAA,gBAAA,gBAAA;AAE9B,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,UAAA;AAyEA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,CAAA,IAAA,WAAA,KAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,WAAA,UAAA,GAAA;;oBDtEI,cAAc,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,eAAA,GAAA,QAAA,CAAA,kTAAA,EAAA,CAAA;;;sEAIhC,qBAAmB,CAAA;UAP/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,8TAAA,EAAA,CAAA;;;;6EAIjC,qBAAmB,EAAA,WAAA,uBAAA,UAAA,iEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}