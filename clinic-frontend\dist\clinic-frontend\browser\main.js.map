{"version": 3, "sources": ["src/app/app.component.ts", "src/app/core/guards/auth.guard.ts", "src/app/core/guards/role.guard.ts", "src/app/app.routes.ts", "src/app/core/interceptors/auth.interceptor.ts", "src/main.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { RouterOutlet } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterOutlet],\r\n  template: `\r\n    <nav class=\"bg-gray-800\">\r\n      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div class=\"flex items-center justify-between h-16\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"flex-shrink-0\">\r\n              <span class=\"text-white text-xl font-bold\">{{ title }}</span>\r\n            </div>\r\n            <div class=\"hidden md:block\">\r\n              <div class=\"ml-10 flex items-baseline space-x-4\">\r\n                <a routerLink=\"/dashboard\" routerLinkActive=\"bg-gray-900 text-white\" class=\"text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\">Dashboard</a>\r\n                <a routerLink=\"/patients\" routerLinkActive=\"bg-gray-900 text-white\" class=\"text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium\">Patients</a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n\r\n    <main class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\r\n      <router-outlet></router-outlet>\r\n    </main>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n      min-height: 100vh;\r\n      background-color: #f3f4f6;\r\n    }\r\n  `]\r\n})\r\nexport class AppComponent {\r\n  title = 'Clinic Management System';\r\n}\r\n", "import { inject } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\nexport const authGuard = () => {\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  if (authService.isAuthenticated()) {\r\n    return true;\r\n  }\r\n\r\n  return router.parseUrl('/login');\r\n}; ", "import { inject } from '@angular/core';\r\nimport { Router, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { UserRole } from '../models/auth.model';\r\n\r\nexport const roleGuard = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean | UrlTree => {\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  // First check if the user is authenticated\r\n  if (!authService.isAuthenticated()) {\r\n    return router.createUrlTree(['/login'], { queryParams: { returnUrl: state.url } });\r\n  }\r\n\r\n  // Check if route has data.roles specified\r\n  const roles = route.data['roles'] as Array<UserRole>;\r\n  if (!roles || roles.length === 0) {\r\n    return true; // No specific roles required\r\n  }\r\n\r\n  // Check if user has required role\r\n  if (authService.hasRole(roles)) {\r\n    return true;\r\n  }\r\n\r\n  // If not in role, redirect to unauthorized page\r\n  return router.createUrlTree(['/unauthorized']);\r\n}; ", "import { Routes } from '@angular/router';\r\nimport { authGuard } from './core/guards/auth.guard';\r\nimport { roleGuard } from './core/guards/role.guard';\r\nimport { UserRole } from './core/models/auth.model';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: 'dashboard',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'login',\r\n    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)\r\n  },\r\n  {\r\n    path: 'register',\r\n    loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)\r\n  },\r\n  {\r\n    path: 'initialize-admin',\r\n    loadComponent: () => import('./features/auth/initialize-admin/initialize-admin.component').then(m => m.InitializeAdminComponent)\r\n  },\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./layouts/main-layout/main-layout.component').then(m => m.MainLayoutComponent),\r\n    canActivate: [authGuard],\r\n    children: [\r\n      {\r\n        path: 'dashboard',\r\n        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)\r\n      },\r\n      {\r\n        path: 'patients',\r\n        loadChildren: () => import('./features/patients/patient.routes').then(m => m.PATIENT_ROUTES),\r\n        canActivate: [roleGuard],\r\n        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }\r\n      },\r\n      {\r\n        path: 'doctors',\r\n        loadChildren: () => import('./features/doctors/doctors.routes').then(m => m.DOCTORS_ROUTES),\r\n        canActivate: [roleGuard],\r\n        data: { roles: [UserRole.Admin, UserRole.Receptionist] }\r\n      },\r\n      {\r\n        path: 'appointments',\r\n        loadChildren: () => import('./features/appointments/appointments.routes').then(m => m.APPOINTMENTS_ROUTES),\r\n        canActivate: [roleGuard],\r\n        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist, UserRole.Patient] }\r\n      },\r\n      {\r\n        path: 'medical-records',\r\n        loadChildren: () => import('./features/medical-records/medical-records.routes').then(m => m.MEDICAL_RECORDS_ROUTES),\r\n        canActivate: [roleGuard],\r\n        data: { roles: [UserRole.Admin, UserRole.Doctor] }\r\n      },\r\n      {\r\n        path: 'schedules',\r\n        loadChildren: () => import('./features/schedules/schedules.routes').then(m => m.SCHEDULES_ROUTES),\r\n        canActivate: [roleGuard],\r\n        data: { roles: [UserRole.Admin, UserRole.Doctor, UserRole.Receptionist] }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: 'unauthorized',\r\n    loadComponent: () => import('./features/auth/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent)\r\n  },\r\n  {\r\n    path: '**',\r\n    loadComponent: () => import('./features/auth/not-found/not-found.component').then(m => m.NotFoundComponent)\r\n  }\r\n];\r\n", "import { HttpInterceptorFn } from '@angular/common/http';\r\nimport { inject } from '@angular/core';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\nexport const authInterceptor: HttpInterceptorFn = (req, next) => {\r\n  const authService = inject(AuthService);\r\n  const token = authService.getToken();\r\n\r\n  if (token) {\r\n    const cloned = req.clone({\r\n      headers: req.headers.set('Authorization', `Bearer ${token}`)\r\n    });\r\n    return next(cloned);\r\n  }\r\n\r\n  return next(req);\r\n}; ", "import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\r\nimport { AppComponent } from './app/app.component';\r\nimport { routes } from './app/app.routes';\r\nimport { authInterceptor } from './app/core/interceptors/auth.interceptor';\r\n\r\nbootstrapApplication(AppComponent, {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideHttpClient(withInterceptors([authInterceptor]))\r\n  ]\r\n}).catch(err => console.error(err));\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCM,IAAO,eAAP,MAAO,cAAY;EACvB,QAAQ;;qCADG,eAAY;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,WAAA,QAAA,WAAA,SAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,mBAAA,MAAA,GAAA,CAAA,GAAA,QAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,WAAA,WAAA,GAAA,CAAA,GAAA,UAAA,UAAA,GAAA,CAAA,GAAA,SAAA,QAAA,kBAAA,WAAA,GAAA,CAAA,cAAA,cAAA,oBAAA,0BAAA,GAAA,iBAAA,qBAAA,oBAAA,QAAA,QAAA,cAAA,WAAA,aAAA,GAAA,CAAA,cAAA,aAAA,oBAAA,0BAAA,GAAA,iBAAA,qBAAA,oBAAA,QAAA,QAAA,cAAA,WAAA,aAAA,GAAA,CAAA,GAAA,aAAA,WAAA,QAAA,WAAA,SAAA,CAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA9BrB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyB,GAAA,OAAA,CAAA,EAC6B,GAAA,OAAA,CAAA,EACE,GAAA,OAAA,CAAA,EACnB,GAAA,OAAA,CAAA,EACF,GAAA,QAAA,CAAA;AACkB,MAAA,iBAAA,CAAA;AAAW,MAAA,uBAAA,EAAO;AAE/D,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EACsB,GAAA,KAAA,CAAA;AACwH,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAChL,MAAA,yBAAA,IAAA,KAAA,CAAA;AAAsK,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAI,EAC9K,EACF,EACF,EACF,EACF;AAGR,MAAA,yBAAA,IAAA,QAAA,EAAA;AACE,MAAA,oBAAA,IAAA,eAAA;AACF,MAAA,uBAAA;;;AAfqD,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,KAAA;;oBAP7C,cAAc,YAAY,GAAA,QAAA,CAAA,iJAAA,EAAA,CAAA;;;sEAgCzB,cAAY,CAAA;UAnCxB;uBACW,YAAU,YACR,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAC3B;;;;;;;;;;;;;;;;;;;;;;KAsBT,QAAA,CAAA,4RAAA,EAAA,CAAA;;;;6EASU,cAAY,EAAA,WAAA,gBAAA,UAAA,4BAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;ACnClB,IAAM,YAAY,MAAK;AAC5B,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,SAAS,OAAO,MAAM;AAE5B,MAAI,YAAY,gBAAe,GAAI;AACjC,WAAO;EACT;AAEA,SAAO,OAAO,SAAS,QAAQ;AACjC;;;ACRO,IAAM,YAAY,CAAC,OAA+B,UAAiD;AACxG,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,SAAS,OAAO,MAAM;AAG5B,MAAI,CAAC,YAAY,gBAAe,GAAI;AAClC,WAAO,OAAO,cAAc,CAAC,QAAQ,GAAG,EAAE,aAAa,EAAE,WAAW,MAAM,IAAG,EAAE,CAAE;EACnF;AAGA,QAAM,QAAQ,MAAM,KAAK,OAAO;AAChC,MAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,WAAO;EACT;AAGA,MAAI,YAAY,QAAQ,KAAK,GAAG;AAC9B,WAAO;EACT;AAGA,SAAO,OAAO,cAAc,CAAC,eAAe,CAAC;AAC/C;;;ACtBO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,cAAc;;EAEjG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA6C,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAE1G;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA6D,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEjI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA6C,EAAE,KAAK,OAAK,EAAE,mBAAmB;IAC1G,aAAa,CAAC,SAAS;IACvB,UAAU;MACR;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAA0C,EAAE,KAAK,OAAK,EAAE,kBAAkB;;MAExG;QACE,MAAM;QACN,cAAc,MAAM,OAAO,qBAAoC,EAAE,KAAK,OAAK,EAAE,cAAc;QAC3F,aAAa,CAAC,SAAS;QACvB,MAAM,EAAE,OAAO,CAAC,SAAS,OAAO,SAAS,QAAQ,SAAS,YAAY,EAAC;;MAEzE;QACE,MAAM;QACN,cAAc,MAAM,OAAO,qBAAmC,EAAE,KAAK,OAAK,EAAE,cAAc;QAC1F,aAAa,CAAC,SAAS;QACvB,MAAM,EAAE,OAAO,CAAC,SAAS,OAAO,SAAS,YAAY,EAAC;;MAExD;QACE,MAAM;QACN,cAAc,MAAM,OAAO,qBAA6C,EAAE,KAAK,OAAK,EAAE,mBAAmB;QACzG,aAAa,CAAC,SAAS;QACvB,MAAM,EAAE,OAAO,CAAC,SAAS,OAAO,SAAS,QAAQ,SAAS,cAAc,SAAS,OAAO,EAAC;;MAE3F;QACE,MAAM;QACN,cAAc,MAAM,OAAO,qBAAmD,EAAE,KAAK,OAAK,EAAE,sBAAsB;QAClH,aAAa,CAAC,SAAS;QACvB,MAAM,EAAE,OAAO,CAAC,SAAS,OAAO,SAAS,MAAM,EAAC;;MAElD;QACE,MAAM;QACN,cAAc,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,gBAAgB;QAChG,aAAa,CAAC,SAAS;QACvB,MAAM,EAAE,OAAO,CAAC,SAAS,OAAO,SAAS,QAAQ,SAAS,YAAY,EAAC;;;;EAI7E;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAqD,EAAE,KAAK,OAAK,EAAE,qBAAqB;;EAEtH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,iBAAiB;;;;;AClEvG,IAAM,kBAAqC,CAAC,KAAK,SAAQ;AAC9D,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,QAAQ,YAAY,SAAQ;AAElC,MAAI,OAAO;AACT,UAAM,SAAS,IAAI,MAAM;MACvB,SAAS,IAAI,QAAQ,IAAI,iBAAiB,UAAU,KAAK,EAAE;KAC5D;AACD,WAAO,KAAK,MAAM;EACpB;AAEA,SAAO,KAAK,GAAG;AACjB;;;ACTA,qBAAqB,cAAc;EACjC,WAAW;IACT,cAAc,MAAM;IACpB,kBAAkB,iBAAiB,CAAC,eAAe,CAAC,CAAC;;CAExD,EAAE,MAAM,SAAO,QAAQ,MAAM,GAAG,CAAC;", "names": []}