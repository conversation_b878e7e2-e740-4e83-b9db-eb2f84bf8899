{"version": 3, "sources": ["src/app/features/appointments/services/appointment.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../../../environments/environment';\r\nimport { Appointment } from '../models/appointment.model';\r\nimport { PaginationQuery, PaginatedResponse } from '../../../core/models/api-response.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AppointmentService {\r\n  private http = inject(HttpClient);\r\n  private apiUrl = `${environment.apiUrl}/appointments`;\r\n\r\n  getAppointments(): Observable<Appointment[]> {\r\n    return this.http.get<Appointment[]>(this.apiUrl);\r\n  }\r\n\r\n  getAppointment(id: string): Observable<Appointment> {\r\n    return this.http.get<Appointment>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  createAppointment(appointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Observable<Appointment> {\r\n    return this.http.post<Appointment>(this.apiUrl, appointment);\r\n  }\r\n\r\n  updateAppointment(id: string, appointment: Partial<Appointment>): Observable<Appointment> {\r\n    return this.http.put<Appointment>(`${this.apiUrl}/${id}`, appointment);\r\n  }\r\n\r\n  deleteAppointment(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  getPaginatedAppointments(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<PaginatedResponse<Appointment>> {\r\n    const query: PaginationQuery = {\r\n      pageNumber,\r\n      pageSize,\r\n      searchTerm\r\n    };\r\n    return this.http.get<PaginatedResponse<Appointment>>(`${this.apiUrl}/paginated`, { params: query as any });\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;AAUM,IAAO,qBAAP,MAAO,oBAAkB;EACrB,OAAO,OAAO,UAAU;EACxB,SAAS,GAAG,YAAY,MAAM;EAEtC,kBAAe;AACb,WAAO,KAAK,KAAK,IAAmB,KAAK,MAAM;EACjD;EAEA,eAAe,IAAU;AACvB,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EAC1D;EAEA,kBAAkB,aAAgE;AAChF,WAAO,KAAK,KAAK,KAAkB,KAAK,QAAQ,WAAW;EAC7D;EAEA,kBAAkB,IAAY,aAAiC;AAC7D,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,MAAM,IAAI,EAAE,IAAI,WAAW;EACvE;EAEA,kBAAkB,IAAU;AAC1B,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EACtD;EAEA,yBAAyB,aAAqB,GAAG,WAAmB,IAAI,YAAmB;AACzF,UAAM,QAAyB;MAC7B;MACA;MACA;;AAEF,WAAO,KAAK,KAAK,IAAoC,GAAG,KAAK,MAAM,cAAc,EAAE,QAAQ,MAAY,CAAE;EAC3G;;qCA/BW,qBAAkB;EAAA;4EAAlB,qBAAkB,SAAlB,oBAAkB,WAAA,YAFjB,OAAM,CAAA;;;sEAEP,oBAAkB,CAAA;UAH9B;WAAW;MACV,YAAY;KACb;;;", "names": []}