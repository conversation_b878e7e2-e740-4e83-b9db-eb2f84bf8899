{"version": 3, "sources": ["src/app/features/medical-records/medical-records.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const MEDICAL_RECORDS_ROUTES: Routes = [\r\n  // Comment out missing component routes\r\n  // {\r\n  //   path: 'list',\r\n  //   loadComponent: () => import('./medical-record-list/medical-record-list.component').then(m => m.MedicalRecordListComponent)\r\n  // },\r\n  // {\r\n  //   path: 'new',\r\n  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)\r\n  // },\r\n  // {\r\n  //   path: 'edit/:id',\r\n  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)\r\n  // },\r\n  // {\r\n  //   path: ':id',\r\n  //   loadComponent: () => import('./medical-record-detail/medical-record-detail.component').then(m => m.MedicalRecordDetailComponent)\r\n  // },\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./components/medical-record-list/medical-record-list.component').then(m => m.MedicalRecordListComponent)\r\n  },\r\n  {\r\n    path: 'add',\r\n    loadComponent: () => import('./components/medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    loadComponent: () => import('./components/medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)\r\n  },\r\n  {\r\n    path: ':id',\r\n    loadComponent: () => import('./components/medical-record-detail/medical-record-detail.component').then(m => m.MedicalRecordDetailComponent)\r\n  }\r\n]; "], "mappings": ";;;;;;AAEO,IAAM,yBAAiC;;;;;;;;;;;;;;;;;;EAkB5C;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAgE,EAAE,KAAK,OAAK,EAAE,0BAA0B;;EAEtI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAgE,EAAE,KAAK,OAAK,EAAE,0BAA0B;;EAEtI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAgE,EAAE,KAAK,OAAK,EAAE,0BAA0B;;EAEtI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAoE,EAAE,KAAK,OAAK,EAAE,4BAA4B;;;", "names": []}