{"version": 3, "sources": ["src/app/features/patients/patient.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const PATIENT_ROUTES: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./patient-list/patient-list.component').then(m => m.PatientListComponent)\r\n  },\r\n  {\r\n    path: 'new',\r\n    loadComponent: () => import('./patient-form/patient-form.component').then(m => m.PatientFormComponent)\r\n  },\r\n  {\r\n    path: ':id',\r\n    loadComponent: () => import('./patient-detail/patient-detail.component').then(m => m.PatientDetailComponent)\r\n  },\r\n  {\r\n    path: ':id/edit',\r\n    loadComponent: () => import('./patient-form/patient-form.component').then(m => m.PatientFormComponent)\r\n  }\r\n]; "], "mappings": ";;;;;AAEO,IAAM,iBAAyB;EACpC;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,oBAAoB;;EAEvG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,oBAAoB;;EAEvG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA2C,EAAE,KAAK,OAAK,EAAE,sBAAsB;;EAE7G;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,oBAAoB;;;", "names": []}