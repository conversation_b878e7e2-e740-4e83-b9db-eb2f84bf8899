{"version": 3, "sources": ["src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-medical-record-list',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  template: `\r\n    <div class=\"container mx-auto p-4\">\r\n      <h1 class=\"text-2xl font-bold mb-4\">Medical Records</h1>\r\n      <div class=\"mb-4\">\r\n        <a routerLink=\"add\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">\r\n          Add New Record\r\n        </a>\r\n      </div>\r\n      <div class=\"grid gap-4\">\r\n        <!-- Placeholder for medical records list -->\r\n        <p>Medical records will be displayed here</p>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: []\r\n})\r\nexport class MedicalRecordListComponent implements OnInit {\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {}\r\n} "], "mappings": ";;;;;;;;;;;;;;;;AAwBM,IAAO,6BAAP,MAAO,4BAA0B;EACrC,cAAA;EAAe;EAEf,WAAQ;EAAU;;qCAHP,6BAA0B;EAAA;yEAA1B,6BAA0B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,KAAA,GAAA,CAAA,GAAA,YAAA,aAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,cAAA,OAAA,GAAA,eAAA,cAAA,QAAA,QAAA,WAAA,mBAAA,GAAA,CAAA,GAAA,QAAA,OAAA,CAAA,GAAA,UAAA,SAAA,oCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAfnC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,MAAA,CAAA;AACG,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA;AACnD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,GAAA,KAAA,CAAA;AAEd,MAAA,iBAAA,GAAA,kBAAA;AACF,MAAA,uBAAA,EAAI;AAEN,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,GAAA;AAEnB,MAAA,iBAAA,GAAA,wCAAA;AAAsC,MAAA,uBAAA,EAAI,EACzC;;oBAZA,cAAc,cAAY,UAAA,GAAA,eAAA,EAAA,CAAA;;;sEAiBzB,4BAA0B,CAAA;UApBtC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAC3B;;;;;;;;;;;;;IAaT,CAAA;;;;6EAGU,4BAA0B,EAAA,WAAA,8BAAA,UAAA,oGAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}