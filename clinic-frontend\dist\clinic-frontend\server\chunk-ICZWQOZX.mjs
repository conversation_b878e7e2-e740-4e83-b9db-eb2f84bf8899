import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/patients/patient.routes.ts
var PATIENT_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-JKGA5PKZ.mjs").then((m) => m.PatientListComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-list/patient-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-2WKBYLPV.mjs").then((m) => m.PatientFormComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-6ZNA6IDC.mjs").then((m) => m.PatientDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-detail/patient-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-2WKBYLPV.mjs").then((m) => m.PatientFormComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {})
];
export {
  PATIENT_ROUTES
};
//# sourceMappingURL=chunk-ICZWQOZX.mjs.map
