import './polyfills.server.mjs';
import {
  environment
} from "./chunk-QSZZESH5.mjs";
import {
  HttpClient,
  Injectable,
  catchError,
  setClassMetadata,
  throwError,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-BUZS6RN2.mjs";

// src/app/features/user-management/services/user-management.service.ts
var UserManagementService = class _UserManagementService {
  http;
  apiUrl = `${environment.apiUrl}/usermanagement`;
  constructor(http) {
    this.http = http;
  }
  getAllUsers() {
    return this.http.get(this.apiUrl).pipe(catchError((error) => {
      console.error("Failed to fetch users:", error);
      return throwError(() => error);
    }));
  }
  getUsersByRole(role) {
    return this.http.get(`${this.apiUrl}/by-role/${role}`).pipe(catchError((error) => {
      console.error(`Failed to fetch users by role ${role}:`, error);
      return throwError(() => error);
    }));
  }
  getUserById(id) {
    return this.http.get(`${this.apiUrl}/${id}`).pipe(catchError((error) => {
      console.error(`Failed to fetch user ${id}:`, error);
      return throwError(() => error);
    }));
  }
  createUser(user) {
    return this.http.post(this.apiUrl, user).pipe(catchError((error) => {
      console.error("Failed to create user:", error);
      return throwError(() => error);
    }));
  }
  updateUser(id, user) {
    return this.http.put(`${this.apiUrl}/${id}`, user).pipe(catchError((error) => {
      console.error(`Failed to update user ${id}:`, error);
      return throwError(() => error);
    }));
  }
  deactivateUser(id) {
    return this.http.post(`${this.apiUrl}/${id}/deactivate`, {}).pipe(catchError((error) => {
      console.error(`Failed to deactivate user ${id}:`, error);
      return throwError(() => error);
    }));
  }
  activateUser(id) {
    return this.http.post(`${this.apiUrl}/${id}/activate`, {}).pipe(catchError((error) => {
      console.error(`Failed to activate user ${id}:`, error);
      return throwError(() => error);
    }));
  }
  static \u0275fac = function UserManagementService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserManagementService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _UserManagementService, factory: _UserManagementService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserManagementService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  UserManagementService
};
//# sourceMappingURL=chunk-MSWF3DHB.mjs.map
