import './polyfills.server.mjs';
import {
  AppointmentService
} from "./chunk-COTX6EKK.mjs";
import {
  FormsModule
} from "./chunk-IIIRLQMQ.mjs";
import {
  RouterLink,
  RouterModule
} from "./chunk-YKEX2NSQ.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgForOf,
  NgIf,
  Subject,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵpureFunction3,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate3,
  ɵɵtextInterpolate4
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/appointments/appointment-list/appointment-list.component.ts
var _c0 = (a0) => [a0];
var _c1 = (a0) => [a0, "edit"];
var _c2 = (a0, a1, a2) => ({ "bg-green-100 text-green-800": a0, "bg-yellow-100 text-yellow-800": a1, "bg-red-100 text-red-800": a2 });
var _c3 = () => [];
function AppointmentListComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275element(1, "div", 11);
    \u0275\u0275elementEnd();
  }
}
function AppointmentListComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12)(1, "p", 13);
    \u0275\u0275text(2, "No appointments found.");
    \u0275\u0275elementEnd()();
  }
}
function AppointmentListComponent_div_10_li_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "li")(1, "div", 17)(2, "div", 18)(3, "div", 19)(4, "p", 20);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p", 21);
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "div", 22)(10, "button", 23);
    \u0275\u0275text(11, " View ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "button", 24);
    \u0275\u0275text(13, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "button", 25);
    \u0275\u0275listener("click", function AppointmentListComponent_div_10_li_2_Template_button_click_14_listener() {
      const appointment_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.onDelete(appointment_r2.id));
    });
    \u0275\u0275text(15, " Delete ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(16, "div", 26)(17, "span", 27);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const appointment_r2 = ctx.$implicit;
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate4(" ", appointment_r2.patient == null ? null : appointment_r2.patient.firstName, " ", appointment_r2.patient == null ? null : appointment_r2.patient.lastName, " with Dr. ", appointment_r2.doctor == null ? null : appointment_r2.doctor.firstName, " ", appointment_r2.doctor == null ? null : appointment_r2.doctor.lastName, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate3(" ", \u0275\u0275pipeBind2(8, 11, appointment_r2.date, "mediumDate"), " at ", appointment_r2.startTime, " - ", appointment_r2.endTime, " ");
    \u0275\u0275advance(3);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(14, _c0, appointment_r2.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(16, _c1, appointment_r2.id));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction3(18, _c2, appointment_r2.status === "Completed", appointment_r2.status === "Scheduled", appointment_r2.status === "Cancelled" || appointment_r2.status === "NoShow"));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", appointment_r2.status, " ");
  }
}
function AppointmentListComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "ul", 15);
    \u0275\u0275template(2, AppointmentListComponent_div_10_li_2_Template, 19, 22, "li", 16);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.appointments);
  }
}
function AppointmentListComponent_div_11_button_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 31);
    \u0275\u0275listener("click", function AppointmentListComponent_div_11_button_2_Template_button_click_0_listener() {
      const i_r5 = \u0275\u0275restoreView(_r4).index;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.onPageChange(i_r5 + 1));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r5 = ctx.index;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("bg-indigo-50", ctx_r2.currentPage === i_r5 + 1);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", i_r5 + 1, " ");
  }
}
function AppointmentListComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 28)(1, "nav", 29);
    \u0275\u0275template(2, AppointmentListComponent_div_11_button_2_Template, 2, 3, "button", 30);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(1, _c3).constructor(ctx_r2.Math.ceil(ctx_r2.totalItems / ctx_r2.pageSize)));
  }
}
var AppointmentListComponent = class _AppointmentListComponent {
  appointmentService;
  appointments = [];
  loading = false;
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  searchTerm = "";
  searchTermChanged = new Subject();
  Math = Math;
  constructor(appointmentService) {
    this.appointmentService = appointmentService;
    this.searchTermChanged.pipe(debounceTime(300), distinctUntilChanged()).subscribe((term) => {
      this.searchTerm = term;
      this.currentPage = 1;
      this.loadAppointments();
    });
  }
  ngOnInit() {
    this.loadAppointments();
  }
  loadAppointments() {
    this.loading = true;
    this.appointmentService.getPaginatedAppointments(this.currentPage, this.pageSize, this.searchTerm).subscribe({
      next: (response) => {
        this.appointments = response.items;
        this.totalItems = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading appointments:", error);
        this.loading = false;
      }
    });
  }
  onSearch(searchTerm) {
    this.searchTermChanged.next(searchTerm);
  }
  onPageChange(page) {
    this.currentPage = page;
    this.loadAppointments();
  }
  onDelete(id) {
    if (confirm("Are you sure you want to delete this appointment?")) {
      this.loading = true;
      this.appointmentService.deleteAppointment(id).subscribe({
        next: () => {
          this.loadAppointments();
        },
        error: (error) => {
          console.error("Error deleting appointment:", error);
          this.loading = false;
        }
      });
    }
  }
  static \u0275fac = function AppointmentListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppointmentListComponent)(\u0275\u0275directiveInject(AppointmentService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppointmentListComponent, selectors: [["app-appointment-list"]], decls: 12, vars: 4, consts: [[1, "container", "mx-auto", "px-4", "py-8"], [1, "flex", "justify-between", "items-center", "mb-6"], [1, "text-2xl", "font-bold"], ["routerLink", "new", 1, "px-4", "py-2", "bg-indigo-600", "text-white", "rounded-md", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-indigo-500", "focus:ring-offset-2"], [1, "mb-4"], ["type", "text", "placeholder", "Search appointments...", 1, "w-full", "px-4", "py-2", "border", "border-gray-300", "rounded-md", "focus:outline-none", "focus:ring-2", "focus:ring-indigo-500", "focus:border-indigo-500", 3, "input"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "text-center py-8", 4, "ngIf"], ["class", "bg-white shadow overflow-hidden sm:rounded-md", 4, "ngIf"], ["class", "mt-4 flex justify-center", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-indigo-600"], [1, "text-center", "py-8"], [1, "text-gray-500"], [1, "bg-white", "shadow", "overflow-hidden", "sm:rounded-md"], [1, "divide-y", "divide-gray-200"], [4, "ngFor", "ngForOf"], [1, "px-4", "py-4", "sm:px-6"], [1, "flex", "items-center", "justify-between"], [1, "flex-1", "min-w-0"], [1, "text-sm", "font-medium", "text-indigo-600", "truncate"], [1, "mt-1", "text-sm", "text-gray-500"], [1, "flex", "space-x-2"], [1, "px-3", "py-1", "text-sm", "text-indigo-600", "hover:text-indigo-900", 3, "routerLink"], [1, "px-3", "py-1", "text-sm", "text-gray-600", "hover:text-gray-900", 3, "routerLink"], [1, "px-3", "py-1", "text-sm", "text-red-600", "hover:text-red-900", 3, "click"], [1, "mt-2"], [1, "px-2", "inline-flex", "text-xs", "leading-5", "font-semibold", "rounded-full", 3, "ngClass"], [1, "mt-4", "flex", "justify-center"], ["aria-label", "Pagination", 1, "relative", "z-0", "inline-flex", "rounded-md", "shadow-sm", "-space-x-px"], ["class", "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50", 3, "bg-indigo-50", "click", 4, "ngFor", "ngForOf"], [1, "relative", "inline-flex", "items-center", "px-4", "py-2", "border", "border-gray-300", "bg-white", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", 3, "click"]], template: function AppointmentListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1", 2);
      \u0275\u0275text(3, "Appointments");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "button", 3);
      \u0275\u0275text(5, " New Appointment ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 4)(7, "input", 5);
      \u0275\u0275listener("input", function AppointmentListComponent_Template_input_input_7_listener($event) {
        return ctx.onSearch($event.target.value);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275template(8, AppointmentListComponent_div_8_Template, 2, 0, "div", 6)(9, AppointmentListComponent_div_9_Template, 3, 0, "div", 7)(10, AppointmentListComponent_div_10_Template, 3, 1, "div", 8)(11, AppointmentListComponent_div_11_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.appointments.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.appointments.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.totalItems > 0);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, FormsModule], styles: ["\n\n.animate-spin[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=appointment-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppointmentListComponent, [{
    type: Component,
    args: [{ selector: "app-appointment-list", standalone: true, imports: [CommonModule, RouterModule, FormsModule], template: `<div class="container mx-auto px-4 py-8">\r
  <div class="flex justify-between items-center mb-6">\r
    <h1 class="text-2xl font-bold">Appointments</h1>\r
    <button\r
      routerLink="new"\r
      class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"\r
    >\r
      New Appointment\r
    </button>\r
  </div>\r
\r
  <div class="mb-4">\r
    <input\r
      type="text"\r
      placeholder="Search appointments..."\r
      class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"\r
      (input)="onSearch($any($event.target).value)"\r
    >\r
  </div>\r
\r
  <div *ngIf="loading" class="flex justify-center items-center h-64">\r
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>\r
  </div>\r
\r
  <div *ngIf="!loading && appointments.length === 0" class="text-center py-8">\r
    <p class="text-gray-500">No appointments found.</p>\r
  </div>\r
\r
  <div *ngIf="!loading && appointments.length > 0" class="bg-white shadow overflow-hidden sm:rounded-md">\r
    <ul class="divide-y divide-gray-200">\r
      <li *ngFor="let appointment of appointments">\r
        <div class="px-4 py-4 sm:px-6">\r
          <div class="flex items-center justify-between">\r
            <div class="flex-1 min-w-0">\r
              <p class="text-sm font-medium text-indigo-600 truncate">\r
                {{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }} with Dr. {{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }}\r
              </p>\r
              <p class="mt-1 text-sm text-gray-500">\r
                {{ appointment.date | date:'mediumDate' }} at {{ appointment.startTime }} - {{ appointment.endTime }}\r
              </p>\r
            </div>\r
            <div class="flex space-x-2">\r
              <button\r
                [routerLink]="[appointment.id]"\r
                class="px-3 py-1 text-sm text-indigo-600 hover:text-indigo-900"\r
              >\r
                View\r
              </button>\r
              <button\r
                [routerLink]="[appointment.id, 'edit']"\r
                class="px-3 py-1 text-sm text-gray-600 hover:text-gray-900"\r
              >\r
                Edit\r
              </button>\r
              <button\r
                (click)="onDelete(appointment.id)"\r
                class="px-3 py-1 text-sm text-red-600 hover:text-red-900"\r
              >\r
                Delete\r
              </button>\r
            </div>\r
          </div>\r
          <div class="mt-2">\r
            <span\r
              [ngClass]="{\r
                'bg-green-100 text-green-800': appointment.status === 'Completed',\r
                'bg-yellow-100 text-yellow-800': appointment.status === 'Scheduled',\r
                'bg-red-100 text-red-800': appointment.status === 'Cancelled' || appointment.status === 'NoShow'\r
              }"\r
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"\r
            >\r
              {{ appointment.status }}\r
            </span>\r
          </div>\r
        </div>\r
      </li>\r
    </ul>\r
  </div>\r
\r
  <div *ngIf="!loading && totalItems > 0" class="mt-4 flex justify-center">\r
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">\r
      <button\r
        *ngFor="let page of [].constructor(Math.ceil(totalItems / pageSize)); let i = index"\r
        (click)="onPageChange(i + 1)"\r
        [class.bg-indigo-50]="currentPage === i + 1"\r
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"\r
      >\r
        {{ i + 1 }}\r
      </button>\r
    </nav>\r
  </div>\r
</div> `, styles: ["/* src/app/features/appointments/appointment-list/appointment-list.component.scss */\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=appointment-list.component.css.map */\n"] }]
  }], () => [{ type: AppointmentService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppointmentListComponent, { className: "AppointmentListComponent", filePath: "src/app/features/appointments/appointment-list/appointment-list.component.ts", lineNumber: 17 });
})();
export {
  AppointmentListComponent
};
//# sourceMappingURL=chunk-5EDB7RPH.mjs.map
