import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/appointments/appointments.routes.ts
var APPOINTMENTS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-5EDB7RPH.mjs").then((m) => m.AppointmentListComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-list/appointment-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-TFANXNKP.mjs").then((m) => m.AppointmentFormComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-TFANXNKP.mjs").then((m) => m.AppointmentFormComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-ELQ3CUTA.mjs").then((m) => m.AppointmentDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-detail/appointment-detail.component.ts" } : {})
];
export {
  APPOINTMENTS_ROUTES
};
//# sourceMappingURL=chunk-YKTUUNTW.mjs.map
