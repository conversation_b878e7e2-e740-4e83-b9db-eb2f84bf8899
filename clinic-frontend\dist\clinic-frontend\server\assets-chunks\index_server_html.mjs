export default `<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>ClinicFrontend</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="styles.css"></head>
<body><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script>
  <app-root></app-root>
<link rel="modulepreload" href="chunk-K2W23EYU.js"><link rel="modulepreload" href="chunk-AUAHXWWU.js"><link rel="modulepreload" href="chunk-2NNV54NL.js"><link rel="modulepreload" href="chunk-7NNESOLN.js"><link rel="modulepreload" href="chunk-7FZJUQ36.js"><link rel="modulepreload" href="chunk-Y5RQAIA6.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script></body>
</html>
`;