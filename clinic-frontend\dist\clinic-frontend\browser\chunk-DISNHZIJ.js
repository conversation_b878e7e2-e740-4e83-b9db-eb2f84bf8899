import {
  CommonModule,
  Component,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-BMSBKD5S.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts
var MedicalRecordDetailComponent = class _MedicalRecordDetailComponent {
  constructor() {
  }
  ngOnInit() {
  }
  static \u0275fac = function MedicalRecordDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MedicalRecordDetailComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MedicalRecordDetailComponent, selectors: [["app-medical-record-detail"]], decls: 9, vars: 0, consts: [[1, "container", "mx-auto", "p-4"], [1, "text-2xl", "font-bold", "mb-4"], [1, "grid", "gap-4"], [1, "mt-4"], ["routerLink", "..", 1, "bg-gray-500", "text-white", "px-4", "py-2", "rounded", "hover:bg-gray-600"]], template: function MedicalRecordDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2, "Medical Record Details");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 2)(4, "p");
      \u0275\u0275text(5, "Medical record details will be displayed here");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 3)(7, "a", 4);
      \u0275\u0275text(8, " Back to List ");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [CommonModule, RouterModule, RouterLink], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MedicalRecordDetailComponent, [{
    type: Component,
    args: [{ selector: "app-medical-record-detail", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Record Details</h1>
      <div class="grid gap-4">
        <!-- Placeholder for medical record details -->
        <p>Medical record details will be displayed here</p>
      </div>
      <div class="mt-4">
        <a routerLink=".." class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
          Back to List
        </a>
      </div>
    </div>
  ` }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MedicalRecordDetailComponent, { className: "MedicalRecordDetailComponent", filePath: "src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts", lineNumber: 25 });
})();
export {
  MedicalRecordDetailComponent
};
//# sourceMappingURL=chunk-DISNHZIJ.js.map
