import './polyfills.server.mjs';
import {
  UserManagementService
} from "./chunk-MSWF3DHB.mjs";
import {
  UserRole
} from "./chunk-2CKFJZTV.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgForOf,
  NgIf,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-BUZS6RN2.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/user-management/user-list/user-list.component.ts
var _c0 = (a0) => ["/user-management", a0];
var _c1 = (a0) => ["/user-management", a0, "edit"];
function UserListComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275element(1, "div", 19);
    \u0275\u0275elementEnd();
  }
}
function UserListComponent_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function UserListComponent_div_24_tr_18_button_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 44);
    \u0275\u0275listener("click", function UserListComponent_div_24_tr_18_button_27_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r2);
      const user_r3 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deactivateUser(user_r3.id));
    });
    \u0275\u0275text(1, " Deactivate ");
    \u0275\u0275elementEnd();
  }
}
function UserListComponent_div_24_tr_18_button_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 45);
    \u0275\u0275listener("click", function UserListComponent_div_24_tr_18_button_28_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const user_r3 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.activateUser(user_r3.id));
    });
    \u0275\u0275text(1, " Activate ");
    \u0275\u0275elementEnd();
  }
}
function UserListComponent_div_24_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr", 28)(1, "td", 29)(2, "div", 30)(3, "div", 31)(4, "div", 32);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 33)(7, "div", 34);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(9, "td", 29)(10, "div", 35);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "td", 29)(13, "span", 36);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "td", 29)(16, "span", 36);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "td", 37);
    \u0275\u0275text(19);
    \u0275\u0275pipe(20, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "td", 38)(22, "div", 39)(23, "button", 40);
    \u0275\u0275text(24, " View ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "button", 41);
    \u0275\u0275text(26, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275template(27, UserListComponent_div_24_tr_18_button_27_Template, 2, 0, "button", 42)(28, UserListComponent_div_24_tr_18_button_28_Template, 2, 0, "button", 43);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const user_r3 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate2(" ", user_r3.firstName.charAt(0), "", user_r3.lastName.charAt(0), " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" ", user_r3.firstName, " ", user_r3.lastName, " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(user_r3.email);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", ctx_r0.getRoleBadgeClass(user_r3.role));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", user_r3.role, " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", user_r3.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", user_r3.isActive ? "Active" : "Inactive", " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", user_r3.lastLoginDate ? \u0275\u0275pipeBind2(20, 14, user_r3.lastLoginDate, "short") : "Never", " ");
    \u0275\u0275advance(4);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(17, _c0, user_r3.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(19, _c1, user_r3.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", user_r3.isActive);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !user_r3.isActive);
  }
}
function UserListComponent_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 22)(2, "table", 23)(3, "thead", 24)(4, "tr")(5, "th", 25);
    \u0275\u0275text(6, " User ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "th", 25);
    \u0275\u0275text(8, " Email ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "th", 25);
    \u0275\u0275text(10, " Role ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "th", 25);
    \u0275\u0275text(12, " Status ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "th", 25);
    \u0275\u0275text(14, " Last Login ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "th", 25);
    \u0275\u0275text(16, " Actions ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(17, "tbody", 26);
    \u0275\u0275template(18, UserListComponent_div_24_tr_18_Template, 29, 21, "tr", 27);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(18);
    \u0275\u0275property("ngForOf", ctx_r0.users)("ngForTrackBy", ctx_r0.trackByUserId);
  }
}
function UserListComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 46);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 47);
    \u0275\u0275element(2, "path", 48);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(3, "h3", 49);
    \u0275\u0275text(4, "No users found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p", 50);
    \u0275\u0275text(6, "Get started by creating a new user.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 51)(8, "button", 52);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(9, "svg", 53);
    \u0275\u0275element(10, "path", 5);
    \u0275\u0275elementEnd();
    \u0275\u0275text(11, " Add New User ");
    \u0275\u0275elementEnd()()();
  }
}
var UserListComponent = class _UserListComponent {
  userManagementService;
  users = [];
  loading = false;
  error = "";
  selectedRole = "";
  constructor(userManagementService) {
    this.userManagementService = userManagementService;
  }
  ngOnInit() {
    this.loadUsers();
  }
  loadUsers() {
    this.loading = true;
    this.error = "";
    const request$ = this.selectedRole ? this.userManagementService.getUsersByRole(this.selectedRole) : this.userManagementService.getAllUsers();
    request$.subscribe({
      next: (users) => {
        this.users = users;
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load users. Please try again.";
        this.loading = false;
        console.error("Error loading users:", error);
      }
    });
  }
  onRoleFilterChange(event) {
    this.selectedRole = event.target.value;
    this.loadUsers();
  }
  trackByUserId(index, user) {
    return user.id;
  }
  getRoleBadgeClass(role) {
    switch (role) {
      case UserRole.Admin:
        return "bg-purple-100 text-purple-800";
      case UserRole.Doctor:
        return "bg-blue-100 text-blue-800";
      case UserRole.Receptionist:
        return "bg-green-100 text-green-800";
      case UserRole.Patient:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }
  deactivateUser(id) {
    if (confirm("Are you sure you want to deactivate this user?")) {
      this.userManagementService.deactivateUser(id).subscribe({
        next: () => {
          this.loadUsers();
        },
        error: (error) => {
          this.error = "Failed to deactivate user. Please try again.";
          console.error("Error deactivating user:", error);
        }
      });
    }
  }
  activateUser(id) {
    if (confirm("Are you sure you want to activate this user?")) {
      this.userManagementService.activateUser(id).subscribe({
        next: () => {
          this.loadUsers();
        },
        error: (error) => {
          this.error = "Failed to activate user. Please try again.";
          console.error("Error activating user:", error);
        }
      });
    }
  }
  static \u0275fac = function UserListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserListComponent)(\u0275\u0275directiveInject(UserManagementService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserListComponent, selectors: [["app-user-list"]], decls: 26, vars: 4, consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "flex", "justify-between", "items-center", "mb-6"], [1, "text-3xl", "font-bold", "text-gray-900"], ["routerLink", "/user-management/new", 1, "bg-primary-600", "hover:bg-primary-700", "text-white", "font-medium", "py-2", "px-4", "rounded-md", "flex", "items-center"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", 1, "h-5", "w-5", "mr-2"], ["fill-rule", "evenodd", "d", "M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z", "clip-rule", "evenodd"], [1, "mb-6"], [1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], [1, "block", "w-48", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", 3, "change"], ["value", ""], ["value", "Admin"], ["value", "Doctor"], ["value", "Receptionist"], ["value", "Patient"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], ["class", "bg-white shadow rounded-lg overflow-hidden", 4, "ngIf"], ["class", "text-center py-12", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "bg-red-50", "border", "border-red-200", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "bg-white", "shadow", "rounded-lg", "overflow-hidden"], [1, "overflow-x-auto"], [1, "min-w-full", "divide-y", "divide-gray-200"], [1, "bg-gray-50"], ["scope", "col", 1, "px-6", "py-3", "text-left", "text-xs", "font-medium", "text-gray-500", "uppercase", "tracking-wider"], [1, "bg-white", "divide-y", "divide-gray-200"], ["class", "hover:bg-gray-50", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "hover:bg-gray-50"], [1, "px-6", "py-4", "whitespace-nowrap"], [1, "flex", "items-center"], [1, "flex-shrink-0", "h-10", "w-10"], [1, "h-10", "w-10", "rounded-full", "bg-primary-500", "flex", "items-center", "justify-center", "text-white", "font-medium"], [1, "ml-4"], [1, "text-sm", "font-medium", "text-gray-900"], [1, "text-sm", "text-gray-900"], [1, "inline-flex", "px-2", "py-1", "text-xs", "font-semibold", "rounded-full", 3, "ngClass"], [1, "px-6", "py-4", "whitespace-nowrap", "text-sm", "text-gray-500"], [1, "px-6", "py-4", "whitespace-nowrap", "text-sm", "font-medium"], [1, "flex", "space-x-2"], [1, "text-primary-600", "hover:text-primary-900", 3, "routerLink"], [1, "text-indigo-600", "hover:text-indigo-900", 3, "routerLink"], ["class", "text-red-600 hover:text-red-900", 3, "click", 4, "ngIf"], ["class", "text-green-600 hover:text-green-900", 3, "click", 4, "ngIf"], [1, "text-red-600", "hover:text-red-900", 3, "click"], [1, "text-green-600", "hover:text-green-900", 3, "click"], [1, "text-center", "py-12"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "mx-auto", "h-12", "w-12", "text-gray-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"], [1, "mt-2", "text-sm", "font-medium", "text-gray-900"], [1, "mt-1", "text-sm", "text-gray-500"], [1, "mt-6"], ["routerLink", "/user-management/new", 1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "shadow-sm", "text-sm", "font-medium", "rounded-md", "text-white", "bg-primary-600", "hover:bg-primary-700"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", 1, "-ml-1", "mr-2", "h-5", "w-5"]], template: function UserListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1", 2);
      \u0275\u0275text(3, "User Management");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "button", 3);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(5, "svg", 4);
      \u0275\u0275element(6, "path", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275text(7, " Add New User ");
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(8, "div", 6)(9, "label", 7);
      \u0275\u0275text(10, "Filter by Role:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "select", 8);
      \u0275\u0275listener("change", function UserListComponent_Template_select_change_11_listener($event) {
        return ctx.onRoleFilterChange($event);
      });
      \u0275\u0275elementStart(12, "option", 9);
      \u0275\u0275text(13, "All Roles");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "option", 10);
      \u0275\u0275text(15, "Admin");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "option", 11);
      \u0275\u0275text(17, "Doctor");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "option", 12);
      \u0275\u0275text(19, "Receptionist");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "option", 13);
      \u0275\u0275text(21, "Patient");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(22, UserListComponent_div_22_Template, 2, 0, "div", 14)(23, UserListComponent_div_23_Template, 2, 1, "div", 15)(24, UserListComponent_div_24_Template, 19, 2, "div", 16)(25, UserListComponent_div_25_Template, 12, 0, "div", 17);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(22);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && !ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && !ctx.error && ctx.users.length === 0);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, DatePipe, RouterModule, RouterLink], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=user-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserListComponent, [{
    type: Component,
    args: [{ selector: "app-user-list", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
        <button 
          routerLink="/user-management/new"
          class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Add New User
        </button>
      </div>

      <!-- Filter by Role -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Role:</label>
        <select 
          (change)="onRoleFilterChange($event)"
          class="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">All Roles</option>
          <option value="Admin">Admin</option>
          <option value="Doctor">Doctor</option>
          <option value="Receptionist">Receptionist</option>
          <option value="Patient">Patient</option>
        </select>
      </div>

      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>

      <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
        {{ error }}
      </div>

      <div *ngIf="!loading && !error" class="bg-white shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let user of users; trackBy: trackByUserId" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center text-white font-medium">
                        {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ user.firstName }} {{ user.lastName }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ user.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        [ngClass]="getRoleBadgeClass(user.role)">
                    {{ user.role }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        [ngClass]="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                    {{ user.isActive ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ user.lastLoginDate ? (user.lastLoginDate | date:'short') : 'Never' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button 
                      [routerLink]="['/user-management', user.id]"
                      class="text-primary-600 hover:text-primary-900"
                    >
                      View
                    </button>
                    <button 
                      [routerLink]="['/user-management', user.id, 'edit']"
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Edit
                    </button>
                    <button 
                      *ngIf="user.isActive"
                      (click)="deactivateUser(user.id)"
                      class="text-red-600 hover:text-red-900"
                    >
                      Deactivate
                    </button>
                    <button 
                      *ngIf="!user.isActive"
                      (click)="activateUser(user.id)"
                      class="text-green-600 hover:text-green-900"
                    >
                      Activate
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div *ngIf="!loading && !error && users.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new user.</p>
        <div class="mt-6">
          <button 
            routerLink="/user-management/new"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add New User
          </button>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;219558ef63f119a92210704329b58a3cdceaa4fb296db559e672f74512827dc7;F:/clinc/clinic-frontend/src/app/features/user-management/user-list/user-list.component.ts */\n:host {\n  display: block;\n}\n/*# sourceMappingURL=user-list.component.css.map */\n"] }]
  }], () => [{ type: UserManagementService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserListComponent, { className: "UserListComponent", filePath: "src/app/features/user-management/user-list/user-list.component.ts", lineNumber: 171 });
})();
export {
  UserListComponent
};
//# sourceMappingURL=chunk-XLQEIRSH.mjs.map
