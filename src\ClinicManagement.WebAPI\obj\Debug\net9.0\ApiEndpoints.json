[{"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "Create", "RelativePath": "api/Appointments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "GetById", "RelativePath": "api/Appointments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Appointments.Common.AppointmentDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "Update", "RelativePath": "api/Appointments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "command", "Type": "ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "Cancel", "RelativePath": "api/Appointments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "command", "Type": "ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "CancelAppointment", "RelativePath": "api/Appointments/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AppointmentsController", "Method": "GetByDateRange", "RelativePath": "api/Appointments/date-range", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "doctorId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "patientId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AuthController", "Method": "InitializeAdmin", "RelativePath": "api/Auth/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ClinicManagement.WebAPI.Controllers.InitializeAdminRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ClinicManagement.WebAPI.Controllers.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ClinicManagement.WebAPI.Controllers.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.DoctorsController", "Method": "GetDoctors", "RelativePath": "api/Doctors", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "specialization", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.DoctorsController", "Method": "GetDoctor", "RelativePath": "api/Doctors/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.DoctorsController", "Method": "GetDoctorSchedule", "RelativePath": "api/Doctors/{id}/schedule", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "GetPatients", "RelativePath": "api/frontend/FrontendPatients", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ClinicManagement.Application.Features.Patients.Common.PatientDto, ClinicManagement.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "Create", "RelativePath": "api/frontend/FrontendPatients", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "GetById", "RelativePath": "api/frontend/FrontendPatients/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Patients.Common.PatientDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "Update", "RelativePath": "api/frontend/FrontendPatients/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "command", "Type": "ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "Delete", "RelativePath": "api/frontend/FrontendPatients/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.FrontendPatientsController", "Method": "GetPatientDetails", "RelativePath": "api/frontend/FrontendPatients/{id}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.PatientDetailDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.MedicalRecordsController", "Method": "Create", "RelativePath": "api/MedicalRecords", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.MedicalRecordsController", "Method": "GetById", "RelativePath": "api/MedicalRecords/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.MedicalRecords.Common.MedicalRecordDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.MedicalRecordsController", "Method": "GetByPatient", "RelativePath": "api/MedicalRecords/patient/{patientId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "patientId", "Type": "System.Int32", "IsRequired": true}, {"Name": "skip", "Type": "System.Int32", "IsRequired": false}, {"Name": "take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "GetPatients", "RelativePath": "api/Patients", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Take", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ClinicManagement.Application.Features.Patients.Common.PatientDto, ClinicManagement.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "Create", "RelativePath": "api/Patients", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "GetById", "RelativePath": "api/Patients/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Patients.Common.PatientDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "Update", "RelativePath": "api/Patients/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "command", "Type": "ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "Delete", "RelativePath": "api/Patients/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.PatientsController", "Method": "GetPatientDetails", "RelativePath": "api/Patients/{id}/details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.PatientDetailDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.SchedulesController", "Method": "Create", "RelativePath": "api/Schedules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.SchedulesController", "Method": "GetById", "RelativePath": "api/Schedules/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.Application.Features.Schedules.Common.ScheduleDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.SchedulesController", "Method": "GetByDoctor", "RelativePath": "api/Schedules/doctor/{doctorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "doctorId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "GetAllUsers", "RelativePath": "api/UserManagement", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ClinicManagement.WebAPI.Controllers.UserManagementDto, ClinicManagement.WebAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "CreateUser", "RelativePath": "api/UserManagement", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ClinicManagement.WebAPI.Controllers.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.WebAPI.Controllers.UserManagementDto", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "GetUser", "RelativePath": "api/UserManagement/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ClinicManagement.WebAPI.Controllers.UserManagementDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "UpdateUser", "RelativePath": "api/UserManagement/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "ClinicManagement.WebAPI.Controllers.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "ActivateUser", "RelativePath": "api/UserManagement/{id}/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "DeactivateUser", "RelativePath": "api/UserManagement/{id}/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}, {"ContainingType": "ClinicManagement.WebAPI.Controllers.UserManagementController", "Method": "GetUsersByRole", "RelativePath": "api/UserManagement/by-role/{role}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ClinicManagement.WebAPI.Controllers.UserManagementDto, ClinicManagement.WebAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 403}]}]