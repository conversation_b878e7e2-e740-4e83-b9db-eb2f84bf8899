import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { UserManagement, CreateUserRequest, UpdateUserRequest } from '../models/user-management.model';
import { UserRole } from '../../../core/models/auth.model';
import { environment } from '../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserManagementService {
  private apiUrl = `${environment.apiUrl}/usermanagement`;

  constructor(private http: HttpClient) {}

  getAllUsers(): Observable<UserManagement[]> {
    return this.http.get<UserManagement[]>(this.apiUrl).pipe(
      catchError((error) => {
        console.error('Failed to fetch users:', error);
        return throwError(() => error);
      })
    );
  }

  getUsersByRole(role: UserRole): Observable<UserManagement[]> {
    return this.http.get<UserManagement[]>(`${this.apiUrl}/by-role/${role}`).pipe(
      catchError((error) => {
        console.error(`Failed to fetch users by role ${role}:`, error);
        return throwError(() => error);
      })
    );
  }

  getUserById(id: number): Observable<UserManagement> {
    return this.http.get<UserManagement>(`${this.apiUrl}/${id}`).pipe(
      catchError((error) => {
        console.error(`Failed to fetch user ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  createUser(user: CreateUserRequest): Observable<UserManagement> {
    return this.http.post<UserManagement>(this.apiUrl, user).pipe(
      catchError((error) => {
        console.error('Failed to create user:', error);
        return throwError(() => error);
      })
    );
  }

  updateUser(id: number, user: UpdateUserRequest): Observable<any> {
    return this.http.put(`${this.apiUrl}/${id}`, user).pipe(
      catchError((error) => {
        console.error(`Failed to update user ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  deactivateUser(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/deactivate`, {}).pipe(
      catchError((error) => {
        console.error(`Failed to deactivate user ${id}:`, error);
        return throwError(() => error);
      })
    );
  }

  activateUser(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/activate`, {}).pipe(
      catchError((error) => {
        console.error(`Failed to activate user ${id}:`, error);
        return throwError(() => error);
      })
    );
  }
}
