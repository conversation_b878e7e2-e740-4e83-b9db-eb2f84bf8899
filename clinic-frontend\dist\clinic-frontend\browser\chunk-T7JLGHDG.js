import {
  environment
} from "./chunk-7NNESOLN.js";
import {
  HttpClient,
  Injectable,
  inject,
  setClassMetadata,
  ɵɵdefineInjectable
} from "./chunk-7FZJUQ36.js";

// src/app/features/doctors/services/doctor.service.ts
var DoctorService = class _DoctorService {
  http = inject(HttpClient);
  apiUrl = `${environment.apiUrl}/doctors`;
  getDoctors() {
    return this.http.get(this.apiUrl);
  }
  getDoctor(id) {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  createDoctor(doctor) {
    return this.http.post(this.apiUrl, doctor);
  }
  updateDoctor(id, doctor) {
    return this.http.put(`${this.apiUrl}/${id}`, doctor);
  }
  deleteDoctor(id) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
  getPaginatedDoctors(pageNumber = 1, pageSize = 10, searchTerm) {
    const query = {
      pageNumber,
      pageSize,
      searchTerm
    };
    return this.http.get(`${this.apiUrl}/paginated`, { params: query });
  }
  // Alias for deleteDoctor to maintain compatibility
  delete(id) {
    return this.deleteDoctor(id);
  }
  static \u0275fac = function DoctorService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DoctorService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _DoctorService, factory: _DoctorService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DoctorService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();

export {
  DoctorService
};
//# sourceMappingURL=chunk-T7JLGHDG.js.map
