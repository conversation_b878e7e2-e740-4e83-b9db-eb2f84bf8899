{"version": 3, "sources": ["src/app/core/models/patient.model.ts", "src/app/features/patients/patient-form/patient-form.component.ts"], "sourcesContent": ["export enum Gender {\r\n  Male = 'Male',\r\n  Female = 'Female',\r\n  Other = 'Other',\r\n  PreferNotToSay = 'PreferNotToSay'\r\n}\r\n\r\nexport interface PersonName {\r\n  firstName: string;\r\n  lastName: string;\r\n  middleName?: string;\r\n  fullName: string;\r\n  displayName: string;\r\n}\r\n\r\nexport interface Email {\r\n  value: string;\r\n}\r\n\r\nexport interface PhoneNumber {\r\n  value: string;\r\n  formattedValue: string;\r\n}\r\n\r\nexport interface Address {\r\n  street: string;\r\n  city: string;\r\n  state: string;\r\n  postalCode: string;\r\n  country: string;\r\n  fullAddress: string;\r\n}\r\n\r\nexport interface Patient {\r\n  id: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  middleName?: string;\r\n  fullName: string;\r\n  dateOfBirth: string;\r\n  age: number;\r\n  gender: Gender;\r\n  phoneNumber: string;  // Changed from 'phone' to match backend\r\n  email: string;\r\n  street: string;       // Separate address fields to match backend\r\n  city: string;\r\n  state: string;\r\n  postalCode: string;\r\n  country: string;\r\n  registrationDate: string;\r\n  medicalHistory: string;\r\n  emergencyContactName?: string;\r\n  emergencyContactPhone?: string;\r\n  insuranceProvider?: string;\r\n  insurancePolicyNumber?: string;\r\n  allergies?: string;\r\n  createdAt: string;\r\n  createdBy: string;\r\n  lastModifiedAt?: string;\r\n  lastModifiedBy?: string;\r\n}\r\n\r\nexport interface CreatePatientRequest {\r\n  firstName: string;\r\n  lastName: string;\r\n  middleName?: string;\r\n  dateOfBirth: string;\r\n  gender: Gender;\r\n  phoneNumber: string;\r\n  email: string;\r\n  street: string;\r\n  city: string;\r\n  state: string;\r\n  postalCode: string;\r\n  country?: string;\r\n  medicalHistory?: string;\r\n  emergencyContactName?: string;\r\n  emergencyContactPhone?: string;\r\n  insuranceProvider?: string;\r\n  insurancePolicyNumber?: string;\r\n  allergies?: string;\r\n}\r\n\r\nexport interface UpdatePatientRequest {\r\n  id: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  middleName?: string;\r\n  dateOfBirth: string;\r\n  gender: Gender;\r\n  phoneNumber: string;\r\n  email: string;\r\n  street: string;\r\n  city: string;\r\n  state: string;\r\n  postalCode: string;\r\n  country?: string;\r\n  medicalHistory?: string;\r\n  emergencyContactName?: string;\r\n  emergencyContactPhone?: string;\r\n  insuranceProvider?: string;\r\n  insurancePolicyNumber?: string;\r\n  allergies?: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  items: T[];\r\n  total: number;\r\n  pageSize: number;\r\n  currentPage: number;\r\n}", "import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\r\nimport { PatientService } from '../../../core/services/patient.service';\r\nimport { Gender, Patient, CreatePatientRequest, UpdatePatientRequest } from '../../../core/models/patient.model';\r\n\r\n@Component({\r\n  selector: 'app-patient-form',\r\n  standalone: true,\r\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\r\n  template: `\r\n    <div class=\"min-h-screen bg-gray-50\">\r\n      <!-- Header Section -->\r\n      <div class=\"bg-white shadow-sm border-b border-gray-200\">\r\n        <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div class=\"flex justify-between items-center py-6\">\r\n            <div>\r\n              <h1 class=\"text-3xl font-bold text-gray-900\">\r\n                {{ isEditMode ? 'Edit Patient' : 'Add New Patient' }}\r\n              </h1>\r\n              <p class=\"mt-1 text-sm text-gray-500\">\r\n                {{ isEditMode ? 'Update patient information' : 'Enter patient details to create a new record' }}\r\n              </p>\r\n            </div>\r\n            <button\r\n              [routerLink]=\"['/patients']\"\r\n              class=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200\">\r\n              <svg class=\"-ml-1 mr-2 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"/>\r\n              </svg>\r\n              Back to Patients\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Main Content -->\r\n      <div class=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <!-- Loading State -->\r\n        <div *ngIf=\"loading\" class=\"bg-white rounded-lg shadow-sm border border-gray-200\">\r\n          <div class=\"flex justify-center items-center py-12\">\r\n            <div class=\"flex flex-col items-center\">\r\n              <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\r\n              <p class=\"mt-4 text-sm text-gray-500\">{{ isEditMode ? 'Loading patient data...' : 'Saving patient...' }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Error State -->\r\n        <div *ngIf=\"error && !loading\" class=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n          <div class=\"flex\">\r\n            <div class=\"flex-shrink-0\">\r\n              <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n              </svg>\r\n            </div>\r\n            <div class=\"ml-3\">\r\n              <p class=\"text-sm text-red-800\">{{ error }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Form -->\r\n        <div *ngIf=\"!loading\" class=\"bg-white rounded-lg shadow-sm border border-gray-200\">\r\n          <form [formGroup]=\"patientForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-6 p-6\">\r\n            <!-- Personal Information Section -->\r\n            <div>\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Personal Information</h3>\r\n              <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\r\n                <!-- First Name -->\r\n                <div>\r\n                  <label for=\"firstName\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    First Name <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"firstName\"\r\n                    type=\"text\"\r\n                    formControlName=\"firstName\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched\"\r\n                    placeholder=\"Enter first name\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    First name is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Last Name -->\r\n                <div>\r\n                  <label for=\"lastName\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Last Name <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"lastName\"\r\n                    type=\"text\"\r\n                    formControlName=\"lastName\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched\"\r\n                    placeholder=\"Enter last name\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Last name is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Middle Name -->\r\n                <div>\r\n                  <label for=\"middleName\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Middle Name\r\n                  </label>\r\n                  <input\r\n                    id=\"middleName\"\r\n                    type=\"text\"\r\n                    formControlName=\"middleName\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter middle name (optional)\"\r\n                  />\r\n                </div>\r\n\r\n                <!-- Date of Birth -->\r\n                <div>\r\n                  <label for=\"dateOfBirth\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Date of Birth <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"dateOfBirth\"\r\n                    type=\"date\"\r\n                    formControlName=\"dateOfBirth\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Date of birth is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Gender -->\r\n                <div>\r\n                  <label for=\"gender\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Gender <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <select\r\n                    id=\"gender\"\r\n                    formControlName=\"gender\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched\"\r\n                  >\r\n                    <option value=\"\">Select gender</option>\r\n                    <option [value]=\"Gender.Male\">Male</option>\r\n                    <option [value]=\"Gender.Female\">Female</option>\r\n                    <option [value]=\"Gender.Other\">Other</option>\r\n                    <option [value]=\"Gender.PreferNotToSay\">Prefer not to say</option>\r\n                  </select>\r\n                  <div *ngIf=\"patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Gender is required\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Contact Information Section -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Contact Information</h3>\r\n              <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n                <!-- Email -->\r\n                <div>\r\n                  <label for=\"email\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Email Address <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"email\"\r\n                    type=\"email\"\r\n                    formControlName=\"email\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('email')?.invalid && patientForm.get('email')?.touched\"\r\n                    placeholder=\"Enter email address\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('email')?.invalid && patientForm.get('email')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    <span *ngIf=\"patientForm.get('email')?.errors?.['required']\">Email is required</span>\r\n                    <span *ngIf=\"patientForm.get('email')?.errors?.['email']\">Please enter a valid email address</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Phone Number -->\r\n                <div>\r\n                  <label for=\"phoneNumber\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Phone Number <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"phoneNumber\"\r\n                    type=\"tel\"\r\n                    formControlName=\"phoneNumber\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched\"\r\n                    placeholder=\"Enter phone number\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Phone number is required\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Address Information Section -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Address Information</h3>\r\n              <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\r\n                <!-- Street -->\r\n                <div class=\"sm:col-span-2\">\r\n                  <label for=\"street\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Street Address <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"street\"\r\n                    type=\"text\"\r\n                    formControlName=\"street\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('street')?.invalid && patientForm.get('street')?.touched\"\r\n                    placeholder=\"Enter street address\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('street')?.invalid && patientForm.get('street')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Street address is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- City -->\r\n                <div>\r\n                  <label for=\"city\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    City <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"city\"\r\n                    type=\"text\"\r\n                    formControlName=\"city\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('city')?.invalid && patientForm.get('city')?.touched\"\r\n                    placeholder=\"Enter city\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('city')?.invalid && patientForm.get('city')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    City is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- State -->\r\n                <div>\r\n                  <label for=\"state\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    State <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"state\"\r\n                    type=\"text\"\r\n                    formControlName=\"state\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('state')?.invalid && patientForm.get('state')?.touched\"\r\n                    placeholder=\"Enter state\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('state')?.invalid && patientForm.get('state')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    State is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Postal Code -->\r\n                <div>\r\n                  <label for=\"postalCode\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Postal Code <span class=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <input\r\n                    id=\"postalCode\"\r\n                    type=\"text\"\r\n                    formControlName=\"postalCode\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    [class.border-red-300]=\"patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched\"\r\n                    placeholder=\"Enter postal code\"\r\n                  />\r\n                  <div *ngIf=\"patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched\" class=\"mt-1 text-sm text-red-600\">\r\n                    Postal code is required\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Country -->\r\n                <div>\r\n                  <label for=\"country\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Country\r\n                  </label>\r\n                  <input\r\n                    id=\"country\"\r\n                    type=\"text\"\r\n                    formControlName=\"country\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter country\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Medical Information Section -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Medical Information</h3>\r\n              <div class=\"grid grid-cols-1 gap-6\">\r\n                <!-- Medical History -->\r\n                <div>\r\n                  <label for=\"medicalHistory\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Medical History\r\n                  </label>\r\n                  <textarea\r\n                    id=\"medicalHistory\"\r\n                    formControlName=\"medicalHistory\"\r\n                    rows=\"3\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter medical history (optional)\"\r\n                  ></textarea>\r\n                </div>\r\n\r\n                <!-- Allergies -->\r\n                <div>\r\n                  <label for=\"allergies\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Allergies\r\n                  </label>\r\n                  <textarea\r\n                    id=\"allergies\"\r\n                    formControlName=\"allergies\"\r\n                    rows=\"2\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter known allergies (optional)\"\r\n                  ></textarea>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Emergency Contact Section -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Emergency Contact</h3>\r\n              <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n                <!-- Emergency Contact Name -->\r\n                <div>\r\n                  <label for=\"emergencyContactName\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Emergency Contact Name\r\n                  </label>\r\n                  <input\r\n                    id=\"emergencyContactName\"\r\n                    type=\"text\"\r\n                    formControlName=\"emergencyContactName\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter emergency contact name\"\r\n                  />\r\n                </div>\r\n\r\n                <!-- Emergency Contact Phone -->\r\n                <div>\r\n                  <label for=\"emergencyContactPhone\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Emergency Contact Phone\r\n                  </label>\r\n                  <input\r\n                    id=\"emergencyContactPhone\"\r\n                    type=\"tel\"\r\n                    formControlName=\"emergencyContactPhone\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter emergency contact phone\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Insurance Information Section -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Insurance Information</h3>\r\n              <div class=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n                <!-- Insurance Provider -->\r\n                <div>\r\n                  <label for=\"insuranceProvider\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Insurance Provider\r\n                  </label>\r\n                  <input\r\n                    id=\"insuranceProvider\"\r\n                    type=\"text\"\r\n                    formControlName=\"insuranceProvider\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter insurance provider\"\r\n                  />\r\n                </div>\r\n\r\n                <!-- Insurance Policy Number -->\r\n                <div>\r\n                  <label for=\"insurancePolicyNumber\" class=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Policy Number\r\n                  </label>\r\n                  <input\r\n                    id=\"insurancePolicyNumber\"\r\n                    type=\"text\"\r\n                    formControlName=\"insurancePolicyNumber\"\r\n                    class=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                    placeholder=\"Enter policy number\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Form Actions -->\r\n            <div class=\"border-t border-gray-200 pt-6\">\r\n              <div class=\"flex justify-end space-x-3\">\r\n                <button\r\n                  type=\"button\"\r\n                  [routerLink]=\"['/patients']\"\r\n                  class=\"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200\">\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  [disabled]=\"patientForm.invalid || loading\"\r\n                  class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\">\r\n                  <svg *ngIf=\"loading\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\r\n                    <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  {{ isEditMode ? 'Update Patient' : 'Create Patient' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `\r\n})\r\nexport class PatientFormComponent implements OnInit {\r\n  patientForm: FormGroup;\r\n  isEditMode = false;\r\n  patientId: number | null = null;\r\n  loading = false;\r\n  error: string | null = null;\r\n  Gender = Gender; // Make Gender enum available in template\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private patientService: PatientService,\r\n    private route: ActivatedRoute,\r\n    public router: Router // Make router public for template access\r\n  ) {\r\n    this.patientForm = this.fb.group({\r\n      firstName: ['', Validators.required],\r\n      lastName: ['', Validators.required],\r\n      middleName: [''],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      phoneNumber: ['', Validators.required],\r\n      dateOfBirth: ['', Validators.required],\r\n      gender: ['', Validators.required],\r\n      street: ['', Validators.required],\r\n      city: ['', Validators.required],\r\n      state: ['', Validators.required],\r\n      postalCode: ['', Validators.required],\r\n      country: ['USA'],\r\n      medicalHistory: [''],\r\n      allergies: [''],\r\n      emergencyContactName: [''],\r\n      emergencyContactPhone: [''],\r\n      insuranceProvider: [''],\r\n      insurancePolicyNumber: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const idParam = this.route.snapshot.paramMap.get('id');\r\n    if (idParam) {\r\n      this.patientId = parseInt(idParam, 10);\r\n      this.isEditMode = true;\r\n      this.loadPatient();\r\n    }\r\n  }\r\n\r\n  private loadPatient(): void {\r\n    if (!this.patientId) return;\r\n\r\n    this.loading = true;\r\n    this.error = null;\r\n\r\n    this.patientService.getById(this.patientId).subscribe({\r\n      next: (patient: Patient) => {\r\n        // Convert date to proper format for date input\r\n        const dateOfBirth = patient.dateOfBirth ? new Date(patient.dateOfBirth).toISOString().split('T')[0] : '';\r\n\r\n        this.patientForm.patchValue({\r\n          firstName: patient.firstName,\r\n          lastName: patient.lastName,\r\n          middleName: patient.middleName || '',\r\n          email: patient.email,\r\n          phoneNumber: patient.phoneNumber,\r\n          dateOfBirth: dateOfBirth,\r\n          gender: patient.gender,\r\n          street: patient.street,\r\n          city: patient.city,\r\n          state: patient.state,\r\n          postalCode: patient.postalCode,\r\n          country: patient.country || 'USA',\r\n          medicalHistory: patient.medicalHistory || '',\r\n          allergies: patient.allergies || '',\r\n          emergencyContactName: patient.emergencyContactName || '',\r\n          emergencyContactPhone: patient.emergencyContactPhone || '',\r\n          insuranceProvider: patient.insuranceProvider || '',\r\n          insurancePolicyNumber: patient.insurancePolicyNumber || ''\r\n        });\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.error = 'Failed to load patient data. Please try again.';\r\n        this.loading = false;\r\n        console.error('Error loading patient:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.patientForm.valid) {\r\n      this.loading = true;\r\n      this.error = null;\r\n\r\n      const formData = this.patientForm.value;\r\n\r\n      if (this.isEditMode && this.patientId) {\r\n        // Update existing patient\r\n        const updateData: UpdatePatientRequest = {\r\n          id: this.patientId,\r\n          ...formData\r\n        };\r\n\r\n        this.patientService.update(this.patientId, updateData).subscribe({\r\n          next: () => {\r\n            this.loading = false;\r\n            this.router.navigate(['/patients']);\r\n          },\r\n          error: (error) => {\r\n            this.error = 'Failed to update patient. Please try again.';\r\n            this.loading = false;\r\n            console.error('Error updating patient:', error);\r\n          }\r\n        });\r\n      } else {\r\n        // Create new patient\r\n        const createData: CreatePatientRequest = formData;\r\n\r\n        this.patientService.create(createData).subscribe({\r\n          next: () => {\r\n            this.loading = false;\r\n            this.router.navigate(['/patients']);\r\n          },\r\n          error: (error) => {\r\n            this.error = 'Failed to create patient. Please try again.';\r\n            this.loading = false;\r\n            console.error('Error creating patient:', error);\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      // Mark all fields as touched to show validation errors\r\n      Object.keys(this.patientForm.controls).forEach(key => {\r\n        this.patientForm.get(key)?.markAsTouched();\r\n      });\r\n    }\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAY;CAAZ,SAAYA,SAAM;AAChB,EAAAA,QAAA,MAAA,IAAA;AACA,EAAAA,QAAA,QAAA,IAAA;AACA,EAAAA,QAAA,OAAA,IAAA;AACA,EAAAA,QAAA,gBAAA,IAAA;AACF,GALY,WAAA,SAAM,CAAA,EAAA;;;;;;ACwCV,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkF,GAAA,OAAA,EAAA,EAC5B,GAAA,OAAA,EAAA;AAEhD,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAsC,IAAA,iBAAA,CAAA;AAAkE,IAAA,uBAAA,EAAI,EACxG,EACF;;;;AAFoC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,4BAAA,mBAAA;;;;;AAM5C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2F,GAAA,OAAA,EAAA,EACvE,GAAA,OAAA,EAAA;;AAEd,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkB,GAAA,KAAA,EAAA;AACgB,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA,EAAI,EAC3C,EACF;;;;AAF8B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;AAyB5B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA;;;;;AAgBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;AA6BA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;AAoBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,sBAAA;AACF,IAAA,uBAAA;;;;;AAuBE,IAAA,yBAAA,GAAA,MAAA;AAA6D,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;;;;;AAC9E,IAAA,yBAAA,GAAA,MAAA;AAA0D,IAAA,iBAAA,GAAA,oCAAA;AAAkC,IAAA,uBAAA;;;;;AAF9F,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,oDAAA,GAAA,GAAA,QAAA,EAAA,EAA6D,GAAA,oDAAA,GAAA,GAAA,QAAA,EAAA;AAE/D,IAAA,uBAAA;;;;;;AAFS,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,OAAA,CAAA;;;;;AAiBT,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,4BAAA;AACF,IAAA,uBAAA;;;;;AAsBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,8BAAA;AACF,IAAA,uBAAA;;;;;AAgBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;;;;;AAgBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,qBAAA;AACF,IAAA,uBAAA;;;;;AAgBA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;;;;AAsIA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAkG,GAAA,QAAA,EAAA;AAEpG,IAAA,uBAAA;;;;;;AA9VV,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmF,GAAA,QAAA,EAAA;AACjD,IAAA,qBAAA,YAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,SAAA,CAAU;IAAA,CAAA;AAEpD,IAAA,yBAAA,GAAA,KAAA,EAAK,GAAA,MAAA,EAAA;AACgD,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AACvE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkE,GAAA,KAAA,EAE3D,GAAA,SAAA,EAAA;AAED,IAAA,iBAAA,GAAA,cAAA;AAAW,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAEhD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,aAAA;AAAU,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAE/C,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,eAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,iBAAA;AAAc,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAEnD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,UAAA;AAAO,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAE5C,IAAA,yBAAA,IAAA,UAAA,EAAA,EAKC,IAAA,UAAA,EAAA;AACkB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAClC,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACtC,IAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACpC,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAwC,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA,EAAS;AAEpE,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2C,IAAA,MAAA,EAAA;AACU,IAAA,iBAAA,IAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAmD,IAAA,KAAA,EAE5C,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,iBAAA;AAAc,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAEnD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAIF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,gBAAA;AAAa,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAElD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2C,IAAA,MAAA,EAAA;AACU,IAAA,iBAAA,IAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAkE,IAAA,OAAA,EAAA,EAErC,IAAA,SAAA,EAAA;AAEvB,IAAA,iBAAA,IAAA,kBAAA;AAAe,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAEpD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,QAAA;AAAK,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAE1C,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,SAAA;AAAM,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAE3C,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,IAAA,eAAA;AAAY,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAEjD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA2C,KAAA,MAAA,EAAA;AACU,IAAA,iBAAA,KAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtE,IAAA,yBAAA,KAAA,OAAA,EAAA,EAAoC,KAAA,KAAA,EAE7B,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,mBAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,YAAA,EAAA;AAOF,IAAA,uBAAA;AAGA,IAAA,yBAAA,KAAA,KAAA,EAAK,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,aAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,YAAA,EAAA;AAOF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA2C,KAAA,MAAA,EAAA;AACU,IAAA,iBAAA,KAAA,mBAAA;AAAiB,IAAA,uBAAA;AACpE,IAAA,yBAAA,KAAA,OAAA,EAAA,EAAmD,KAAA,KAAA,EAE5C,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,0BAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,SAAA,EAAA;AAOF,IAAA,uBAAA;AAGA,IAAA,yBAAA,KAAA,KAAA,EAAK,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,2BAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA2C,KAAA,MAAA,EAAA;AACU,IAAA,iBAAA,KAAA,uBAAA;AAAqB,IAAA,uBAAA;AACxE,IAAA,yBAAA,KAAA,OAAA,EAAA,EAAmD,KAAA,KAAA,EAE5C,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,sBAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,SAAA,EAAA;AAOF,IAAA,uBAAA;AAGA,IAAA,yBAAA,KAAA,KAAA,EAAK,KAAA,SAAA,EAAA;AAED,IAAA,iBAAA,KAAA,iBAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,KAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA2C,KAAA,OAAA,EAAA,EACD,KAAA,UAAA,EAAA;AAKpC,IAAA,iBAAA,KAAA,UAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,KAAA,UAAA,EAAA;AAIE,IAAA,qBAAA,KAAA,mDAAA,GAAA,GAAA,OAAA,EAAA;AAIA,IAAA,iBAAA,GAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACF,EACD;;;;;;;;;;;;;;;;;;;;;;;;AAlWD,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA;AAeI,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,oBAAA,UAAA,OAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAeJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AA6BJ,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,oBAAA,UAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAEI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAcJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,UAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAGQ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,OAAA,IAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,OAAA,MAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,OAAA,KAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,OAAA,cAAA;AAEJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAqBJ,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAgBJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,aAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAqBJ,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAeJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,MAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,MAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,MAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,MAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAeJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAeJ,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,oBAAA,WAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAgIN,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,WAAA,OAAA,OAAA;AAEM,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA;AAIN,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,mBAAA,kBAAA,GAAA;;;AAUZ,IAAO,uBAAP,MAAO,sBAAoB;EASrB;EACA;EACA;EACD;EAXT;EACA,aAAa;EACb,YAA2B;EAC3B,UAAU;EACV,QAAuB;EACvB,SAAS;;EAET,YACU,IACA,gBACA,OACD;AAHC,SAAA,KAAA;AACA,SAAA,iBAAA;AACA,SAAA,QAAA;AACD,SAAA,SAAA;AAEP,SAAK,cAAc,KAAK,GAAG,MAAM;MAC/B,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,UAAU,CAAC,IAAI,WAAW,QAAQ;MAClC,YAAY,CAAC,EAAE;MACf,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,aAAa,CAAC,IAAI,WAAW,QAAQ;MACrC,aAAa,CAAC,IAAI,WAAW,QAAQ;MACrC,QAAQ,CAAC,IAAI,WAAW,QAAQ;MAChC,QAAQ,CAAC,IAAI,WAAW,QAAQ;MAChC,MAAM,CAAC,IAAI,WAAW,QAAQ;MAC9B,OAAO,CAAC,IAAI,WAAW,QAAQ;MAC/B,YAAY,CAAC,IAAI,WAAW,QAAQ;MACpC,SAAS,CAAC,KAAK;MACf,gBAAgB,CAAC,EAAE;MACnB,WAAW,CAAC,EAAE;MACd,sBAAsB,CAAC,EAAE;MACzB,uBAAuB,CAAC,EAAE;MAC1B,mBAAmB,CAAC,EAAE;MACtB,uBAAuB,CAAC,EAAE;KAC3B;EACH;EAEA,WAAQ;AACN,UAAM,UAAU,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AACrD,QAAI,SAAS;AACX,WAAK,YAAY,SAAS,SAAS,EAAE;AACrC,WAAK,aAAa;AAClB,WAAK,YAAW;IAClB;EACF;EAEQ,cAAW;AACjB,QAAI,CAAC,KAAK;AAAW;AAErB,SAAK,UAAU;AACf,SAAK,QAAQ;AAEb,SAAK,eAAe,QAAQ,KAAK,SAAS,EAAE,UAAU;MACpD,MAAM,CAAC,YAAoB;AAEzB,cAAM,cAAc,QAAQ,cAAc,IAAI,KAAK,QAAQ,WAAW,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,IAAI;AAEtG,aAAK,YAAY,WAAW;UAC1B,WAAW,QAAQ;UACnB,UAAU,QAAQ;UAClB,YAAY,QAAQ,cAAc;UAClC,OAAO,QAAQ;UACf,aAAa,QAAQ;UACrB;UACA,QAAQ,QAAQ;UAChB,QAAQ,QAAQ;UAChB,MAAM,QAAQ;UACd,OAAO,QAAQ;UACf,YAAY,QAAQ;UACpB,SAAS,QAAQ,WAAW;UAC5B,gBAAgB,QAAQ,kBAAkB;UAC1C,WAAW,QAAQ,aAAa;UAChC,sBAAsB,QAAQ,wBAAwB;UACtD,uBAAuB,QAAQ,yBAAyB;UACxD,mBAAmB,QAAQ,qBAAqB;UAChD,uBAAuB,QAAQ,yBAAyB;SACzD;AACD,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,gBAAQ,MAAM,0BAA0B,KAAK;MAC/C;KACD;EACH;EAEA,WAAQ;AACN,QAAI,KAAK,YAAY,OAAO;AAC1B,WAAK,UAAU;AACf,WAAK,QAAQ;AAEb,YAAM,WAAW,KAAK,YAAY;AAElC,UAAI,KAAK,cAAc,KAAK,WAAW;AAErC,cAAM,aAAmC;UACvC,IAAI,KAAK;WACN;AAGL,aAAK,eAAe,OAAO,KAAK,WAAW,UAAU,EAAE,UAAU;UAC/D,MAAM,MAAK;AACT,iBAAK,UAAU;AACf,iBAAK,OAAO,SAAS,CAAC,WAAW,CAAC;UACpC;UACA,OAAO,CAAC,UAAS;AACf,iBAAK,QAAQ;AACb,iBAAK,UAAU;AACf,oBAAQ,MAAM,2BAA2B,KAAK;UAChD;SACD;MACH,OAAO;AAEL,cAAM,aAAmC;AAEzC,aAAK,eAAe,OAAO,UAAU,EAAE,UAAU;UAC/C,MAAM,MAAK;AACT,iBAAK,UAAU;AACf,iBAAK,OAAO,SAAS,CAAC,WAAW,CAAC;UACpC;UACA,OAAO,CAAC,UAAS;AACf,iBAAK,QAAQ;AACb,iBAAK,UAAU;AACf,oBAAQ,MAAM,2BAA2B,KAAK;UAChD;SACD;MACH;IACF,OAAO;AAEL,aAAO,KAAK,KAAK,YAAY,QAAQ,EAAE,QAAQ,SAAM;AACnD,aAAK,YAAY,IAAI,GAAG,GAAG,cAAa;MAC1C,CAAC;IACH;EACF;;qCArIW,uBAAoB,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,gBAAA,YAAA,GAAA,CAAA,GAAA,YAAA,aAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,WAAA,QAAA,WAAA,SAAA,GAAA,CAAA,GAAA,QAAA,mBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,YAAA,aAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,eAAA,GAAA,CAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,mBAAA,WAAA,eAAA,cAAA,iBAAA,YAAA,oBAAA,sBAAA,gBAAA,uBAAA,yBAAA,qBAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,SAAA,QAAA,OAAA,KAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,6BAAA,GAAA,CAAA,GAAA,aAAA,WAAA,QAAA,WAAA,WAAA,MAAA,GAAA,CAAA,SAAA,wDAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uDAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,cAAA,aAAA,UAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,OAAA,GAAA,CAAA,GAAA,QAAA,YAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,mBAAA,GAAA,CAAA,GAAA,QAAA,WAAA,eAAA,GAAA,CAAA,GAAA,aAAA,UAAA,kBAAA,cAAA,OAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,QAAA,UAAA,gBAAA,WAAA,aAAA,GAAA,OAAA,OAAA,cAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,mDAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,cAAA,GAAA,CAAA,GAAA,aAAA,OAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,QAAA,eAAA,SAAA,kBAAA,gBAAA,GAAA,CAAA,OAAA,aAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,MAAA,aAAA,QAAA,QAAA,mBAAA,aAAA,eAAA,oBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,SAAA,6BAAA,GAAA,MAAA,GAAA,CAAA,OAAA,YAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,YAAA,QAAA,QAAA,mBAAA,YAAA,eAAA,mBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,cAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,cAAA,QAAA,QAAA,mBAAA,cAAA,eAAA,gCAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,eAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,eAAA,QAAA,QAAA,mBAAA,eAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,UAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,UAAA,mBAAA,UAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,mBAAA,MAAA,GAAA,CAAA,GAAA,QAAA,eAAA,SAAA,gBAAA,GAAA,CAAA,OAAA,SAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,SAAA,QAAA,SAAA,mBAAA,SAAA,eAAA,uBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,eAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,eAAA,QAAA,OAAA,mBAAA,eAAA,eAAA,sBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,GAAA,QAAA,eAAA,SAAA,kBAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,UAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,UAAA,QAAA,QAAA,mBAAA,UAAA,eAAA,wBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,QAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,QAAA,QAAA,QAAA,mBAAA,QAAA,eAAA,cAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,SAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,SAAA,QAAA,QAAA,mBAAA,SAAA,eAAA,eAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,cAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,cAAA,QAAA,QAAA,mBAAA,cAAA,eAAA,qBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,WAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,WAAA,QAAA,QAAA,mBAAA,WAAA,eAAA,iBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,GAAA,QAAA,eAAA,OAAA,GAAA,CAAA,OAAA,kBAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,kBAAA,mBAAA,kBAAA,QAAA,KAAA,eAAA,oCAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,aAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,aAAA,mBAAA,aAAA,QAAA,KAAA,eAAA,oCAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,wBAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,wBAAA,QAAA,QAAA,mBAAA,wBAAA,eAAA,gCAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,yBAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,yBAAA,QAAA,OAAA,mBAAA,yBAAA,eAAA,iCAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,qBAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,qBAAA,QAAA,QAAA,mBAAA,qBAAA,eAAA,4BAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,OAAA,yBAAA,GAAA,SAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,MAAA,yBAAA,QAAA,QAAA,mBAAA,yBAAA,eAAA,uBAAA,GAAA,SAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,aAAA,wBAAA,sBAAA,yBAAA,yBAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,mBAAA,WAAA,eAAA,cAAA,iBAAA,YAAA,oBAAA,sBAAA,gBAAA,uBAAA,yBAAA,qBAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,QAAA,UAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,sBAAA,WAAA,eAAA,cAAA,aAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,uBAAA,+BAAA,qBAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,SAAA,8CAAA,QAAA,QAAA,WAAA,aAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,WAAA,cAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,QAAA,WAAA,aAAA,GAAA,gBAAA,SAAA,QAAA,OAAA,OAAA,YAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,YAAA,GAAA,CAAA,QAAA,gBAAA,KAAA,mHAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA7Z7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAqC,GAAA,OAAA,CAAA,EAEsB,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EACE,GAAA,KAAA,EAC7C,GAAA,MAAA,CAAA;AAED,MAAA,iBAAA,CAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,CAAA;AACF,MAAA,uBAAA,EAAI;AAEN,MAAA,yBAAA,GAAA,UAAA,CAAA;;AAGE,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,oBAAA,IAAA,QAAA,CAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,oBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;;AAIR,MAAA,yBAAA,IAAA,OAAA,CAAA;AAEE,MAAA,qBAAA,IAAA,sCAAA,GAAA,GAAA,OAAA,EAAA,EAAkF,IAAA,sCAAA,GAAA,GAAA,OAAA,EAAA,EAUS,IAAA,sCAAA,KAAA,IAAA,OAAA,EAAA;AAmX7F,MAAA,uBAAA,EAAM;;;AAlZI,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,iBAAA,mBAAA,GAAA;AAGA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,+BAAA,gDAAA,GAAA;AAIF,MAAA,oBAAA;AAAA,MAAA,qBAAA,cAAA,0BAAA,GAAA,GAAA,CAAA;AAcA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAUA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA,CAAA,IAAA,OAAA;AAcA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;;oBAtDF,cAAY,MAAE,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,oBAAA,iBAAE,cAAY,UAAA,GAAA,eAAA,EAAA,CAAA;;;sEA+Z9C,sBAAoB,CAAA;UAlahC;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,cAAc,qBAAqB,YAAY;MACzD,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6ZX;;;;6EACY,sBAAoB,EAAA,WAAA,wBAAA,UAAA,oEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": ["Gender"]}