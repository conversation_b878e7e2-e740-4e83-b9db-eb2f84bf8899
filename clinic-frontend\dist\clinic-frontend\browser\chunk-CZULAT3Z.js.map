{"version": 3, "sources": ["src/app/features/dashboard/dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { PatientService } from '../../core/services/patient.service';\r\nimport { Patient } from '../../core/models/patient.model';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  template: `\r\n    <div class=\"container mx-auto px-4 py-6\">\r\n      <h1 class=\"text-3xl font-bold text-gray-900 mb-6\">Dashboard</h1>\r\n      \r\n      <div *ngIf=\"loading\" class=\"flex justify-center items-center h-64\">\r\n        <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\r\n      </div>\r\n      \r\n      <div *ngIf=\"!loading\">\r\n        <!-- Recent Patients -->\r\n        <div class=\"bg-white rounded-lg shadow mb-8\">\r\n          <div class=\"px-6 py-4 border-b border-gray-200\">\r\n            <h2 class=\"text-xl font-semibold text-gray-800\">Recent Patients</h2>\r\n          </div>\r\n          <div class=\"p-6\">\r\n            <div *ngIf=\"recentPatients.length === 0\" class=\"text-center py-6 text-gray-500\">\r\n              No patients found.\r\n            </div>\r\n            <div *ngIf=\"recentPatients.length > 0\" class=\"overflow-x-auto\">\r\n              <table class=\"min-w-full divide-y divide-gray-200\">\r\n                <thead class=\"bg-gray-50\">\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\r\n                    <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Email</th>\r\n                    <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Phone</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody class=\"bg-white divide-y divide-gray-200\">\r\n                  <tr *ngFor=\"let patient of recentPatients\">\r\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div class=\"text-sm font-medium text-gray-900\">{{ patient.firstName }} {{ patient.lastName }}</div>\r\n                    </td>\r\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div class=\"text-sm text-gray-900\">{{ patient.email }}</div>\r\n                    </td>\r\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div class=\"text-sm text-gray-900\">{{ patient.phoneNumber }}</div>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n    }\r\n  `]\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  recentPatients: Patient[] = [];\r\n  loading = false;\r\n  error = '';\r\n\r\n  constructor(private patientService: PatientService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadRecentPatients();\r\n  }\r\n\r\n  loadRecentPatients(): void {\r\n    this.loading = true;\r\n    this.error = '';\r\n    this.patientService.getAll(1, 5).subscribe({\r\n      next: (patients: Patient[]) => {\r\n        this.recentPatients = patients;\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        this.error = 'Failed to load recent patients. Please try again.';\r\n        this.loading = false;\r\n        console.error('Error loading recent patients:', error);\r\n      }\r\n    });\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,OAAA,CAAA;AACF,IAAA,uBAAA;;;;;AASM,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,sBAAA;AACF,IAAA,uBAAA;;;;;AAWM,IAAA,yBAAA,GAAA,IAAA,EAA2C,GAAA,MAAA,EAAA,EACD,GAAA,OAAA,EAAA;AACS,IAAA,iBAAA,CAAA;AAA8C,IAAA,uBAAA,EAAM;AAErG,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAwC,GAAA,OAAA,EAAA;AACH,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA,EAAM;AAE9D,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAwC,GAAA,OAAA,EAAA;AACH,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA,EAAM,EAC/D;;;;AAP4C,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,WAAA,WAAA,KAAA,WAAA,UAAA,EAAA;AAGZ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,KAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,WAAA;;;;;AAlB7C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+D,GAAA,SAAA,EAAA,EACV,GAAA,SAAA,EAAA,EACvB,GAAA,IAAA,EACpB,GAAA,MAAA,EAAA;AACqG,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;AAC3G,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AAC5G,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA,EAAK,EAC9G;AAEP,IAAA,yBAAA,IAAA,SAAA,EAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,IAAA,GAAA,MAAA,EAAA;AAWF,IAAA,uBAAA,EAAQ,EACF;;;;AAZoB,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA;;;;;AApBpC,IAAA,yBAAA,GAAA,KAAA,EAAsB,GAAA,OAAA,CAAA,EAEyB,GAAA,OAAA,CAAA,EACK,GAAA,MAAA,CAAA;AACE,IAAA,iBAAA,GAAA,iBAAA;AAAe,IAAA,uBAAA,EAAK;AAEtE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAgF,GAAA,yCAAA,IAAA,GAAA,OAAA,EAAA;AA2BlF,IAAA,uBAAA,EAAM,EACF;;;;AA5BI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,WAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,SAAA,CAAA;;;AAmCZ,IAAO,qBAAP,MAAO,oBAAkB;EAKT;EAJpB,iBAA4B,CAAA;EAC5B,UAAU;EACV,QAAQ;EAER,YAAoB,gBAA8B;AAA9B,SAAA,iBAAA;EAAiC;EAErD,WAAQ;AACN,SAAK,mBAAkB;EACzB;EAEA,qBAAkB;AAChB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,eAAe,OAAO,GAAG,CAAC,EAAE,UAAU;MACzC,MAAM,CAAC,aAAuB;AAC5B,aAAK,iBAAiB;AACtB,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAc;AACpB,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,gBAAQ,MAAM,kCAAkC,KAAK;MACvD;KACD;EACH;;qCAzBW,qBAAkB,4BAAA,cAAA,CAAA;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,GAAA,YAAA,aAAA,iBAAA,MAAA,GAAA,CAAA,SAAA,yCAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,cAAA,oBAAA,GAAA,CAAA,GAAA,YAAA,cAAA,UAAA,MAAA,GAAA,CAAA,GAAA,QAAA,QAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,WAAA,iBAAA,eAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,SAAA,kCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,QAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,QAAA,QAAA,aAAA,WAAA,eAAA,iBAAA,aAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,QAAA,QAAA,mBAAA,GAAA,CAAA,GAAA,WAAA,eAAA,eAAA,GAAA,CAAA,GAAA,WAAA,eAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApD3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,MAAA,CAAA;AACW,MAAA,iBAAA,GAAA,WAAA;AAAS,MAAA,uBAAA;AAE3D,MAAA,qBAAA,GAAA,mCAAA,GAAA,GAAA,OAAA,CAAA,EAAmE,GAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAyCrE,MAAA,uBAAA;;;AAzCQ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;;oBATA,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,mGAAA,EAAA,CAAA;;;sEAsDX,oBAAkB,CAAA;UAzD9B;uBACW,iBAAe,YACb,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8CT,QAAA,CAAA,uQAAA,EAAA,CAAA;;;;6EAOU,oBAAkB,EAAA,WAAA,sBAAA,UAAA,qDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}