import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/doctors/doctors.routes.ts
var DOCTORS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-EFBTM3HA.mjs").then((m) => m.DoctorListComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-list/doctor-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-6QRF3L74.mjs").then((m) => m.DoctorFormComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-6QRF3L74.mjs").then((m) => m.DoctorFormComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-YHP53BOY.mjs").then((m) => m.DoctorDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-detail/doctor-detail.component.ts" } : {})
];
export {
  DOCTORS_ROUTES
};
//# sourceMappingURL=chunk-OBGHYTYH.mjs.map
