import {
  environment
} from "./chunk-7NNESOLN.js";
import {
  HttpClient,
  Injectable,
  inject,
  setClassMetadata,
  ɵɵdefineInjectable
} from "./chunk-7FZJUQ36.js";

// src/app/features/appointments/services/appointment.service.ts
var AppointmentService = class _AppointmentService {
  http = inject(HttpClient);
  apiUrl = `${environment.apiUrl}/appointments`;
  getAppointments() {
    return this.http.get(this.apiUrl);
  }
  getAppointment(id) {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  createAppointment(appointment) {
    return this.http.post(this.apiUrl, appointment);
  }
  updateAppointment(id, appointment) {
    return this.http.put(`${this.apiUrl}/${id}`, appointment);
  }
  deleteAppointment(id) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
  getPaginatedAppointments(pageNumber = 1, pageSize = 10, searchTerm) {
    const query = {
      pageNumber,
      pageSize,
      searchTerm
    };
    return this.http.get(`${this.apiUrl}/paginated`, { params: query });
  }
  static \u0275fac = function AppointmentService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppointmentService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AppointmentService, factory: _AppointmentService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppointmentService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();

export {
  AppointmentService
};
//# sourceMappingURL=chunk-7S6VBFUQ.js.map
