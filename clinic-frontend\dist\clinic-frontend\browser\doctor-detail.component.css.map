{"version": 3, "sources": ["src/app/features/doctors/doctor-detail/doctor-detail.component.scss"], "sourcesContent": [":host {\r\n  display: block;\r\n  min-height: calc(100vh - 64px);\r\n  background-color: #f9fafb;\r\n}\r\n\r\n.animate-spin {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n} "], "mappings": ";AAAA;AACE,WAAA;AACA,cAAA,KAAA,MAAA,EAAA;AACA,oBAAA;;AAGF,CAAA;AACE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;", "names": []}