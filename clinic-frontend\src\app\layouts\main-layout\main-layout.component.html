<div class="min-h-screen bg-gray-50">
  <!-- Mobile menu button -->
  <div class="fixed top-0 left-0 right-0 z-50 bg-white shadow-md">
    <div class="flex items-center justify-between px-4 py-3">
      <button 
        (click)="toggleSidebar()" 
        class="text-gray-600 focus:outline-none focus:text-primary-600 md:hidden"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>
      
      <div class="flex items-center">
        <img src="assets/images/logo.png" alt="Clinic Logo" class="h-8 w-auto mr-2">
        <span class="text-xl font-semibold text-primary-700">Clinic Management</span>
      </div>
      
      <!-- User dropdown -->
      <div class="relative" *ngIf="currentUser$ | async as user">
        <div class="flex items-center space-x-4">
          <span class="hidden md:block text-sm text-gray-700">{{ user.firstName }} {{ user.lastName }}</span>
          <button class="flex items-center focus:outline-none">
            <div class="w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center text-white">
              {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
            </div>
          </button>
        </div>
        
        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
          <button (click)="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            Sign out
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="flex pt-16">
    <aside 
      class="fixed inset-y-0 left-0 z-40 w-64 mt-16 transform bg-white border-r border-gray-200 transition duration-300 ease-in-out"
      [ngClass]="{'translate-x-0': isSidebarOpen, '-translate-x-full': !isSidebarOpen, 'md:translate-x-0': true}"
    >
      <nav class="flex flex-col h-full py-4">
        <div class="flex-1 space-y-1 px-2">
          <a routerLink="/dashboard" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
          </a>
          
          <a routerLink="/patients" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Patients
          </a>
          
          <a routerLink="/doctors" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Doctors
          </a>
          
          <a routerLink="/appointments" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Appointments
          </a>
          
          <a routerLink="/medical-records" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Medical Records
          </a>
          
          <a routerLink="/schedules" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Schedules
          </a>

          <!-- Admin Only - User Management -->
          <a *ngIf="isAdmin()" routerLink="/user-management" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            User Management
          </a>
        </div>
        
        <div class="px-4 mt-6">
          <button (click)="logout()" class="w-full flex items-center px-2 py-2 text-base font-medium text-red-600 rounded-md hover:bg-red-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Sign out
          </button>
        </div>
      </nav>
    </aside>

    <!-- Main content -->
    <main class="flex-1 md:ml-64 p-6 pt-20">
      <router-outlet></router-outlet>
    </main>
  </div>
</div> 