{"version": 3, "sources": ["src/app/features/patients/patient-detail/patient-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { PatientService } from '../../../core/services/patient.service';\r\nimport { Patient } from '../../../core/models/patient.model';\r\n\r\n@Component({\r\n  selector: 'app-patient-detail',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  template: `\r\n    <div class=\"container mx-auto px-4 py-6\">\r\n      <div *ngIf=\"loading\" class=\"flex justify-center items-center h-64\">\r\n        <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\r\n      </div>\r\n\r\n      <div *ngIf=\"!loading && patient\" class=\"max-w-3xl mx-auto\">\r\n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\r\n          <!-- Header -->\r\n          <div class=\"px-6 py-4 border-b border-gray-200\">\r\n            <div class=\"flex justify-between items-center\">\r\n              <h1 class=\"text-2xl font-bold text-gray-900\">\r\n                {{ patient.firstName }} {{ patient.lastName }}\r\n              </h1>\r\n              <div class=\"flex space-x-3\">\r\n                <button\r\n                  [routerLink]=\"['/patients', patient.id, 'edit']\"\r\n                  class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\r\n                >\r\n                  Edit\r\n                </button>\r\n                <button\r\n                  (click)=\"onDelete()\"\r\n                  class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\"\r\n                >\r\n                  Delete\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Content -->\r\n          <div class=\"px-6 py-4\">\r\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <!-- Personal Information -->\r\n              <div>\r\n                <h2 class=\"text-lg font-medium text-gray-900 mb-4\">Personal Information</h2>\r\n                <dl class=\"space-y-3\">\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Email</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.email }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Phone</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.phoneNumber }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Date of Birth</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.dateOfBirth | date }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Gender</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.gender }}</dd>\r\n                  </div>\r\n                </dl>\r\n              </div>\r\n\r\n              <!-- Address Information -->\r\n              <div>\r\n                <h2 class=\"text-lg font-medium text-gray-900 mb-4\">Address Information</h2>\r\n                <dl class=\"space-y-3\">\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Street</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.street }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">City</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.city }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">State</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.state }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Postal Code</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.postalCode }}</dd>\r\n                  </div>\r\n                  <div>\r\n                    <dt class=\"text-sm font-medium text-gray-500\">Country</dt>\r\n                    <dd class=\"mt-1 text-sm text-gray-900\">{{ patient.country }}</dd>\r\n                  </div>\r\n                </dl>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Error Message -->\r\n      <div *ngIf=\"error\" class=\"mt-4 text-red-600 text-center\">\r\n        {{ error }}\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n    }\r\n  `]\r\n})\r\nexport class PatientDetailComponent implements OnInit {\r\n  patient: Patient | null = null;\r\n  loading = false;\r\n  error = '';\r\n\r\n  constructor(private patientService: PatientService, private route: ActivatedRoute) {}\r\n\r\n  ngOnInit(): void {\r\n    // Implementation will be added when we have route parameters\r\n  }\r\n\r\n  onDelete(): void {\r\n    if (this.patient && confirm('Are you sure you want to delete this patient?')) {\r\n      this.patientService.deletePatient(this.patient.id).subscribe({\r\n        next: () => {\r\n          // Navigation will be added\r\n        },\r\n        error: (error: any) => {\r\n          this.error = 'Failed to delete patient. Please try again.';\r\n          console.error('Error deleting patient:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaM,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,OAAA,CAAA;AACF,IAAA,uBAAA;;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA2D,GAAA,OAAA,CAAA,EACD,GAAA,OAAA,CAAA,EAEN,GAAA,OAAA,CAAA,EACC,GAAA,MAAA,EAAA;AAE3C,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,UAAA,EAAA;AAKxB,IAAA,iBAAA,GAAA,QAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AAGnB,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACF;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,OAAA,EAAA,EAC8B,IAAA,KAAA,EAE5C,IAAA,MAAA,EAAA;AACgD,IAAA,iBAAA,IAAA,sBAAA;AAAoB,IAAA,uBAAA;AACvE,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAsB,IAAA,KAAA,EACf,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA,EAAK;AAEjE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAyB,IAAA,uBAAA,EAAK;AAEvE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;;AAAgC,IAAA,uBAAA,EAAK;AAE9E,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACpD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAoB,IAAA,uBAAA,EAAK,EAC5D,EACH;AAIP,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AACgD,IAAA,iBAAA,IAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtE,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAsB,IAAA,KAAA,EACf,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACpD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAoB,IAAA,uBAAA,EAAK;AAElE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAClD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAK;AAEhE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA,EAAK;AAEjE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACzD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAwB,IAAA,uBAAA,EAAK;AAEtE,IAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,MAAA,EAAA;AAC2C,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACrD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuC,IAAA,iBAAA,EAAA;AAAqB,IAAA,uBAAA,EAAK,EAC7D,EACH,EACD,EACF,EACF,EACF;;;;AAzEE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,QAAA,WAAA,KAAA,OAAA,QAAA,UAAA,GAAA;AAIE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,IAAA,KAAA,OAAA,QAAA,EAAA,CAAA;AAwByC,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,KAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,WAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,IAAA,OAAA,QAAA,WAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,MAAA;AAWA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,MAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,IAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,KAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,UAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,OAAA;;;;;AAUrD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,OAAA,GAAA;;;AAUF,IAAO,yBAAP,MAAO,wBAAsB;EAKb;EAAwC;EAJ5D,UAA0B;EAC1B,UAAU;EACV,QAAQ;EAER,YAAoB,gBAAwC,OAAqB;AAA7D,SAAA,iBAAA;AAAwC,SAAA,QAAA;EAAwB;EAEpF,WAAQ;EAER;EAEA,WAAQ;AACN,QAAI,KAAK,WAAW,QAAQ,+CAA+C,GAAG;AAC5E,WAAK,eAAe,cAAc,KAAK,QAAQ,EAAE,EAAE,UAAU;QAC3D,MAAM,MAAK;QAEX;QACA,OAAO,CAAC,UAAc;AACpB,eAAK,QAAQ;AACb,kBAAQ,MAAM,2BAA2B,KAAK;QAChD;OACD;IACH;EACF;;qCAvBW,yBAAsB,4BAAA,cAAA,GAAA,4BAAA,cAAA,CAAA;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,SAAA,yCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iCAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,cAAA,oBAAA,GAAA,CAAA,GAAA,aAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,cAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,QAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,mBAAA,cAAA,GAAA,CAAA,GAAA,YAAA,aAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,GAAA,CAAA,GAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,WAAA,eAAA,iBAAA,oBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,cAAA,oBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,QAAA,MAAA,GAAA,CAAA,GAAA,QAAA,eAAA,kBAAA,OAAA,GAAA,CAAA,GAAA,WAAA,eAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,eAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,aAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAnG/B,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,uCAAA,GAAA,GAAA,OAAA,CAAA,EAAmE,GAAA,uCAAA,IAAA,IAAA,OAAA,CAAA,EAIR,GAAA,uCAAA,GAAA,GAAA,OAAA,CAAA;AAsF7D,MAAA,uBAAA;;;AA1FQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,OAAA;AAmFA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;;oBA1FA,cAAY,MAAA,UAAE,cAAY,UAAA,GAAA,QAAA,CAAA,wGAAA,EAAA,CAAA;;;sEAqGzB,wBAAsB,CAAA;UAxGlC;uBACW,sBAAoB,YAClB,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6FT,QAAA,CAAA,+RAAA,EAAA,CAAA;;;;6EAOU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,wEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}