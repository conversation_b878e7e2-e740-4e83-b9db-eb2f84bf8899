export enum Gender {
  Male = 'Male',
  Female = 'Female',
  Other = 'Other',
  PreferNotToSay = 'PreferNotToSay'
}

export interface PersonName {
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  displayName: string;
}

export interface Email {
  value: string;
}

export interface PhoneNumber {
  value: string;
  formattedValue: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  fullAddress: string;
}

export interface Patient {
  id: number;
  firstName: string;
  lastName: string;
  middleName?: string;
  fullName: string;
  dateOfBirth: string;
  age: number;
  gender: Gender;
  phoneNumber: string;  // Changed from 'phone' to match backend
  email: string;
  street: string;       // Separate address fields to match backend
  city: string;
  state: string;
  postalCode: string;
  country: string;
  registrationDate: string;
  medicalHistory: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  allergies?: string;
  createdAt: string;
  createdBy: string;
  lastModifiedAt?: string;
  lastModifiedBy?: string;
}

export interface CreatePatientRequest {
  firstName: string;
  lastName: string;
  middleName?: string;
  dateOfBirth: string;
  gender: Gender;
  phoneNumber: string;
  email: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country?: string;
  medicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  allergies?: string;
}

export interface UpdatePatientRequest {
  id: number;
  firstName: string;
  lastName: string;
  middleName?: string;
  dateOfBirth: string;
  gender: Gender;
  phoneNumber: string;
  email: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country?: string;
  medicalHistory?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  allergies?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  pageSize: number;
  currentPage: number;
}