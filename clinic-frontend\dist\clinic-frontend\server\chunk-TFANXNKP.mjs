import './polyfills.server.mjs';
import {
  AppointmentService
} from "./chunk-COTX6EKK.mjs";
import {
  DoctorService
} from "./chunk-E6PL2YA6.mjs";
import {
  Default<PERSON>al<PERSON>Accessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-IIIRLQMQ.mjs";
import {
  ActivatedRoute,
  Router
} from "./chunk-YKEX2NSQ.mjs";
import {
  PatientService
} from "./chunk-4ZO7KR3M.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  KeyValuePipe,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtextInterpolate3
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/appointments/models/appointment.model.ts
var AppointmentStatus;
(function(AppointmentStatus2) {
  AppointmentStatus2["Scheduled"] = "Scheduled";
  AppointmentStatus2["Completed"] = "Completed";
  AppointmentStatus2["Cancelled"] = "Cancelled";
  AppointmentStatus2["NoShow"] = "NoShow";
})(AppointmentStatus || (AppointmentStatus = {}));

// src/app/features/appointments/appointment-form/appointment-form.component.ts
function AppointmentFormComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function AppointmentFormComponent_option_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 22);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const patient_r2 = ctx.$implicit;
    \u0275\u0275property("value", patient_r2.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", patient_r2.firstName, " ", patient_r2.lastName, " ");
  }
}
function AppointmentFormComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select a patient ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_option_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 22);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doctor_r3 = ctx.$implicit;
    \u0275\u0275property("value", doctor_r3.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate3(" Dr. ", doctor_r3.firstName, " ", doctor_r3.lastName, " (", doctor_r3.specialization, ") ");
  }
}
function AppointmentFormComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select a doctor ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select a date ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select a start time ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select an end time ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_option_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 22);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const status_r4 = ctx.$implicit;
    \u0275\u0275property("value", status_r4.value);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", status_r4.value, " ");
  }
}
function AppointmentFormComponent_div_43_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1, " Please select a status ");
    \u0275\u0275elementEnd();
  }
}
function AppointmentFormComponent_span_52_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 24);
    \u0275\u0275text(1, "\u27F3");
    \u0275\u0275elementEnd();
  }
}
var AppointmentFormComponent = class _AppointmentFormComponent {
  fb;
  appointmentService;
  doctorService;
  patientService;
  route;
  router;
  appointmentForm;
  isEditMode = false;
  appointmentId = null;
  loading = false;
  error = null;
  doctors = [];
  patients = [];
  AppointmentStatus = AppointmentStatus;
  constructor(fb, appointmentService, doctorService, patientService, route, router) {
    this.fb = fb;
    this.appointmentService = appointmentService;
    this.doctorService = doctorService;
    this.patientService = patientService;
    this.route = route;
    this.router = router;
    this.appointmentForm = this.fb.group({
      patientId: ["", Validators.required],
      doctorId: ["", Validators.required],
      date: ["", Validators.required],
      startTime: ["", Validators.required],
      endTime: ["", Validators.required],
      status: [AppointmentStatus.Scheduled, Validators.required],
      notes: [""]
    });
  }
  ngOnInit() {
    this.loadDoctors();
    this.loadPatients();
    this.appointmentId = this.route.snapshot.paramMap.get("id");
    if (this.appointmentId) {
      this.isEditMode = true;
      this.loadAppointment();
    }
  }
  loadDoctors() {
    this.doctorService.getDoctors().subscribe({
      next: (doctors) => {
        this.doctors = doctors;
      },
      error: (error) => {
        console.error("Error loading doctors:", error);
        this.error = "Failed to load doctors";
      }
    });
  }
  loadPatients() {
    this.patientService.getPatients().subscribe({
      next: (patients) => {
        this.patients = patients;
      },
      error: (error) => {
        console.error("Error loading patients:", error);
        this.error = "Failed to load patients";
      }
    });
  }
  loadAppointment() {
    if (!this.appointmentId)
      return;
    this.loading = true;
    this.error = null;
    this.appointmentService.getAppointment(this.appointmentId).subscribe({
      next: (appointment) => {
        this.appointmentForm.patchValue(appointment);
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load appointment data";
        this.loading = false;
        console.error("Error loading appointment:", error);
      }
    });
  }
  onSubmit() {
    if (this.appointmentForm.valid) {
      this.loading = true;
      this.error = null;
      const appointmentData = this.appointmentForm.value;
      const request$ = this.isEditMode && this.appointmentId ? this.appointmentService.updateAppointment(this.appointmentId, appointmentData) : this.appointmentService.createAppointment(appointmentData);
      request$.subscribe({
        next: () => {
          this.loading = false;
          this.router.navigate(["/appointments"]);
        },
        error: (error) => {
          this.error = "Failed to save appointment data";
          this.loading = false;
          console.error("Error saving appointment:", error);
        }
      });
    }
  }
  static \u0275fac = function AppointmentFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppointmentFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AppointmentService), \u0275\u0275directiveInject(DoctorService), \u0275\u0275directiveInject(PatientService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppointmentFormComponent, selectors: [["app-appointment-form"]], decls: 54, vars: 17, consts: [[1, "container", "mx-auto", "p-4"], [1, "text-2xl", "font-bold", "mb-6"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], [1, "space-y-4", 3, "ngSubmit", "formGroup"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-4"], [1, "block", "text-sm", "font-medium", "text-gray-700"], ["formControlName", "patientId", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["class", "text-red-500 text-sm mt-1", 4, "ngIf"], ["formControlName", "doctorId", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["type", "date", "formControlName", "date", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["type", "time", "formControlName", "startTime", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["type", "time", "formControlName", "endTime", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], ["formControlName", "status", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], [1, "md:col-span-2"], ["formControlName", "notes", "rows", "3", 1, "mt-1", "block", "w-full", "rounded-md", "border-gray-300", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500"], [1, "flex", "justify-end", "space-x-4", "mt-6"], ["type", "button", 1, "px-4", "py-2", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", 3, "click"], ["type", "submit", 1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "disabled:opacity-50", 3, "disabled"], ["class", "inline-block animate-spin mr-2", 4, "ngIf"], [1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [3, "value"], [1, "text-red-500", "text-sm", "mt-1"], [1, "inline-block", "animate-spin", "mr-2"]], template: function AppointmentFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2);
      \u0275\u0275elementEnd();
      \u0275\u0275template(3, AppointmentFormComponent_div_3_Template, 2, 1, "div", 2);
      \u0275\u0275elementStart(4, "form", 3);
      \u0275\u0275listener("ngSubmit", function AppointmentFormComponent_Template_form_ngSubmit_4_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(5, "div", 4)(6, "div")(7, "label", 5);
      \u0275\u0275text(8, "Patient");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "select", 6)(10, "option", 7);
      \u0275\u0275text(11, "Select a patient");
      \u0275\u0275elementEnd();
      \u0275\u0275template(12, AppointmentFormComponent_option_12_Template, 2, 3, "option", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275template(13, AppointmentFormComponent_div_13_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "div")(15, "label", 5);
      \u0275\u0275text(16, "Doctor");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "select", 10)(18, "option", 7);
      \u0275\u0275text(19, "Select a doctor");
      \u0275\u0275elementEnd();
      \u0275\u0275template(20, AppointmentFormComponent_option_20_Template, 2, 4, "option", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275template(21, AppointmentFormComponent_div_21_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div")(23, "label", 5);
      \u0275\u0275text(24, "Date");
      \u0275\u0275elementEnd();
      \u0275\u0275element(25, "input", 11);
      \u0275\u0275template(26, AppointmentFormComponent_div_26_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "div")(28, "label", 5);
      \u0275\u0275text(29, "Start Time");
      \u0275\u0275elementEnd();
      \u0275\u0275element(30, "input", 12);
      \u0275\u0275template(31, AppointmentFormComponent_div_31_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "div")(33, "label", 5);
      \u0275\u0275text(34, "End Time");
      \u0275\u0275elementEnd();
      \u0275\u0275element(35, "input", 13);
      \u0275\u0275template(36, AppointmentFormComponent_div_36_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "div")(38, "label", 5);
      \u0275\u0275text(39, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "select", 14);
      \u0275\u0275template(41, AppointmentFormComponent_option_41_Template, 2, 2, "option", 8);
      \u0275\u0275pipe(42, "keyvalue");
      \u0275\u0275elementEnd();
      \u0275\u0275template(43, AppointmentFormComponent_div_43_Template, 2, 0, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "div", 15)(45, "label", 5);
      \u0275\u0275text(46, "Notes");
      \u0275\u0275elementEnd();
      \u0275\u0275element(47, "textarea", 16);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(48, "div", 17)(49, "button", 18);
      \u0275\u0275listener("click", function AppointmentFormComponent_Template_button_click_49_listener() {
        return ctx.router.navigate(["/appointments"]);
      });
      \u0275\u0275text(50, " Cancel ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "button", 19);
      \u0275\u0275template(52, AppointmentFormComponent_span_52_Template, 2, 0, "span", 20);
      \u0275\u0275text(53);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      let tmp_4_0;
      let tmp_6_0;
      let tmp_7_0;
      let tmp_8_0;
      let tmp_9_0;
      let tmp_11_0;
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("", ctx.isEditMode ? "Edit" : "Create", " Appointment");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.appointmentForm);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngForOf", ctx.patients);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ((tmp_4_0 = ctx.appointmentForm.get("patientId")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.appointmentForm.get("patientId")) == null ? null : tmp_4_0.touched));
      \u0275\u0275advance(7);
      \u0275\u0275property("ngForOf", ctx.doctors);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ((tmp_6_0 = ctx.appointmentForm.get("doctorId")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.appointmentForm.get("doctorId")) == null ? null : tmp_6_0.touched));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", ((tmp_7_0 = ctx.appointmentForm.get("date")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.appointmentForm.get("date")) == null ? null : tmp_7_0.touched));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", ((tmp_8_0 = ctx.appointmentForm.get("startTime")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.appointmentForm.get("startTime")) == null ? null : tmp_8_0.touched));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", ((tmp_9_0 = ctx.appointmentForm.get("endTime")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.appointmentForm.get("endTime")) == null ? null : tmp_9_0.touched));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngForOf", \u0275\u0275pipeBind1(42, 15, ctx.AppointmentStatus));
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ((tmp_11_0 = ctx.appointmentForm.get("status")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.appointmentForm.get("status")) == null ? null : tmp_11_0.touched));
      \u0275\u0275advance(8);
      \u0275\u0275property("disabled", ctx.appointmentForm.invalid || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Update" : "Create", " Appointment ");
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, KeyValuePipe, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName], styles: ["\n\n.animate-spin[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=appointment-form.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppointmentFormComponent, [{
    type: Component,
    args: [{ selector: "app-appointment-form", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="container mx-auto p-4">\r
  <h1 class="text-2xl font-bold mb-6">{{ isEditMode ? 'Edit' : 'Create' }} Appointment</h1>\r
\r
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">\r
    {{ error }}\r
  </div>\r
\r
  <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="space-y-4">\r
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">Patient</label>\r
        <select formControlName="patientId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
          <option value="">Select a patient</option>\r
          <option *ngFor="let patient of patients" [value]="patient.id">\r
            {{ patient.firstName }} {{ patient.lastName }}\r
          </option>\r
        </select>\r
        <div *ngIf="appointmentForm.get('patientId')?.invalid && appointmentForm.get('patientId')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select a patient\r
        </div>\r
      </div>\r
\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">Doctor</label>\r
        <select formControlName="doctorId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
          <option value="">Select a doctor</option>\r
          <option *ngFor="let doctor of doctors" [value]="doctor.id">\r
            Dr. {{ doctor.firstName }} {{ doctor.lastName }} ({{ doctor.specialization }})\r
          </option>\r
        </select>\r
        <div *ngIf="appointmentForm.get('doctorId')?.invalid && appointmentForm.get('doctorId')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select a doctor\r
        </div>\r
      </div>\r
\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">Date</label>\r
        <input type="date" formControlName="date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
        <div *ngIf="appointmentForm.get('date')?.invalid && appointmentForm.get('date')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select a date\r
        </div>\r
      </div>\r
\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">Start Time</label>\r
        <input type="time" formControlName="startTime" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
        <div *ngIf="appointmentForm.get('startTime')?.invalid && appointmentForm.get('startTime')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select a start time\r
        </div>\r
      </div>\r
\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">End Time</label>\r
        <input type="time" formControlName="endTime" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
        <div *ngIf="appointmentForm.get('endTime')?.invalid && appointmentForm.get('endTime')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select an end time\r
        </div>\r
      </div>\r
\r
      <div>\r
        <label class="block text-sm font-medium text-gray-700">Status</label>\r
        <select formControlName="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">\r
          <option *ngFor="let status of AppointmentStatus | keyvalue" [value]="status.value">\r
            {{ status.value }}\r
          </option>\r
        </select>\r
        <div *ngIf="appointmentForm.get('status')?.invalid && appointmentForm.get('status')?.touched" class="text-red-500 text-sm mt-1">\r
          Please select a status\r
        </div>\r
      </div>\r
\r
      <div class="md:col-span-2">\r
        <label class="block text-sm font-medium text-gray-700">Notes</label>\r
        <textarea formControlName="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>\r
      </div>\r
    </div>\r
\r
    <div class="flex justify-end space-x-4 mt-6">\r
      <button type="button" (click)="router.navigate(['/appointments'])" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">\r
        Cancel\r
      </button>\r
      <button type="submit" [disabled]="appointmentForm.invalid || loading" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">\r
        <span *ngIf="loading" class="inline-block animate-spin mr-2">\u27F3</span>\r
        {{ isEditMode ? 'Update' : 'Create' }} Appointment\r
      </button>\r
    </div>\r
  </form>\r
</div> `, styles: ["/* src/app/features/appointments/appointment-form/appointment-form.component.scss */\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=appointment-form.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AppointmentService }, { type: DoctorService }, { type: PatientService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppointmentFormComponent, { className: "AppointmentFormComponent", filePath: "src/app/features/appointments/appointment-form/appointment-form.component.ts", lineNumber: 19 });
})();
export {
  AppointmentFormComponent
};
//# sourceMappingURL=chunk-TFANXNKP.mjs.map
