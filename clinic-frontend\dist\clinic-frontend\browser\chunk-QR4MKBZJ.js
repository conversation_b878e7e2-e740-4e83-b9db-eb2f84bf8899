import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/doctors/doctors.routes.ts
var DOCTORS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-A4AJHFYF.js").then((m) => m.DoctorListComponent)
  }, false ? { \u0275entryName: "src/app/features/doctors/doctor-list/doctor-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-6JA7UBJD.js").then((m) => m.DoctorFormComponent)
  }, false ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-6JA7UBJD.js").then((m) => m.DoctorFormComponent)
  }, false ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-NTRTXUYG.js").then((m) => m.DoctorDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/doctors/doctor-detail/doctor-detail.component.ts" } : {})
];
export {
  DOCTORS_ROUTES
};
//# sourceMappingURL=chunk-QR4MKBZJ.js.map
