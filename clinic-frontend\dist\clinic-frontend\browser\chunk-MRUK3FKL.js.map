{"version": 3, "sources": ["src/app/features/doctors/doctor-detail/doctor-detail.component.ts", "src/app/features/doctors/doctor-detail/doctor-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { DoctorService } from '../services/doctor.service';\r\nimport { Doctor } from '../models/doctor.model';\r\n\r\n@Component({\r\n  selector: 'app-doctor-detail',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './doctor-detail.component.html',\r\n  styleUrls: ['./doctor-detail.component.scss']\r\n})\r\nexport class DoctorDetailComponent implements OnInit {\r\n  doctor: Doctor | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private doctorService: DoctorService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const doctorId = this.route.snapshot.paramMap.get('id');\r\n    if (doctorId) {\r\n      this.loadDoctor(doctorId);\r\n    } else {\r\n      this.error = 'Doctor ID not provided';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private loadDoctor(id: string): void {\r\n    this.doctorService.getDoctor(id).subscribe({\r\n      next: (doctor) => {\r\n        this.doctor = doctor;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading doctor:', error);\r\n        this.error = 'Failed to load doctor details';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onDelete(): void {\r\n    if (this.doctor && confirm('Are you sure you want to delete this doctor?')) {\r\n      this.doctorService.deleteDoctor(this.doctor.id).subscribe({\r\n        next: () => {\r\n          this.router.navigate(['/doctors']);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting doctor:', error);\r\n          this.error = 'Failed to delete doctor';\r\n        }\r\n      });\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div *ngIf=\"loading\" class=\"flex justify-center items-center h-64\">\r\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\r\n  </div>\r\n\r\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n    <strong class=\"font-bold\">Error!</strong>\r\n    <span class=\"block sm:inline\">{{ error }}</span>\r\n  </div>\r\n\r\n  <div *ngIf=\"doctor && !loading\" class=\"max-w-4xl mx-auto\">\r\n    <div class=\"bg-white shadow overflow-hidden sm:rounded-lg\">\r\n      <div class=\"px-4 py-5 sm:px-6 flex justify-between items-center\">\r\n        <div>\r\n          <h3 class=\"text-lg leading-6 font-medium text-gray-900\">Doctor Information</h3>\r\n          <p class=\"mt-1 max-w-2xl text-sm text-gray-500\">Personal details and specialization.</p>\r\n        </div>\r\n        <div class=\"flex space-x-4\">\r\n          <button\r\n            (click)=\"router.navigate(['/doctors', doctor.id, 'edit'])\"\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n          >\r\n            Edit\r\n          </button>\r\n          <button\r\n            (click)=\"onDelete()\"\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n          >\r\n            Delete\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"border-t border-gray-200\">\r\n        <dl>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Full name</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.firstName }} {{ doctor.lastName }}</dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Email address</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.email }}</dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Phone number</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.phoneNumber }}</dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Specialization</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.specialization }}</dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">License number</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.licenseNumber }}</dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Created at</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.createdAt | date:'medium' }}</dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Last updated</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{{ doctor.updatedAt | date:'medium' }}</dd>\r\n          </div>\r\n        </dl>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,OAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAiH,GAAA,UAAA,CAAA;AACrF,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AAChC,IAAA,yBAAA,GAAA,QAAA,CAAA;AAA8B,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA,EAAO;;;;AAAlB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;;AAGhC,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA0D,GAAA,OAAA,EAAA,EACG,GAAA,OAAA,EAAA,EACQ,GAAA,KAAA,EAC1D,GAAA,MAAA,EAAA;AACqD,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAC1E,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAgD,IAAA,iBAAA,GAAA,sCAAA;AAAoC,IAAA,uBAAA,EAAI;AAE1F,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,UAAA,EAAA;AAExB,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,OAAA,SAAA,CAAiB,YAAU,OAAA,OAAA,IAAa,MAAM,CAAA,CAAE;IAAA,CAAA;AAGzD,IAAA,iBAAA,IAAA,QAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AAGnB,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAER,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsC,IAAA,IAAA,EAChC,IAAA,OAAA,EAAA,EACwE,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,WAAA;AAAS,IAAA,uBAAA;AACvD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;AAA4C,IAAA,uBAAA,EAAK;AAEhH,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAK;AAEtF,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;AAAwB,IAAA,uBAAA,EAAK;AAE5F,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA,EAAK;AAE/F,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;AAA0B,IAAA,uBAAA,EAAK;AAE9F,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AACxD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;;AAAsC,IAAA,uBAAA,EAAK;AAE1G,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,MAAA,EAAA;AAA6D,IAAA,iBAAA,EAAA;;AAAsC,IAAA,uBAAA,EAAK,EACpG,EACH,EACD,EACF;;;;AA5B+D,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,OAAA,WAAA,KAAA,OAAA,OAAA,UAAA,EAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,OAAA,KAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,OAAA,WAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,OAAA,cAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,OAAA,aAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,GAAA,OAAA,OAAA,WAAA,QAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,IAAA,OAAA,OAAA,WAAA,QAAA,CAAA;;;AD/CnE,IAAO,wBAAP,MAAO,uBAAqB;EAMtB;EACA;EACD;EAPT,SAAwB;EACxB,UAAU;EACV,QAAuB;EAEvB,YACU,eACA,OACD,QAAc;AAFb,SAAA,gBAAA;AACA,SAAA,QAAA;AACD,SAAA,SAAA;EACN;EAEH,WAAQ;AACN,UAAM,WAAW,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AACtD,QAAI,UAAU;AACZ,WAAK,WAAW,QAAQ;IAC1B,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,UAAU;IACjB;EACF;EAEQ,WAAW,IAAU;AAC3B,SAAK,cAAc,UAAU,EAAE,EAAE,UAAU;MACzC,MAAM,CAAC,WAAU;AACf,aAAK,SAAS;AACd,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAK,QAAQ;AACb,aAAK,UAAU;MACjB;KACD;EACH;EAEA,WAAQ;AACN,QAAI,KAAK,UAAU,QAAQ,8CAA8C,GAAG;AAC1E,WAAK,cAAc,aAAa,KAAK,OAAO,EAAE,EAAE,UAAU;QACxD,MAAM,MAAK;AACT,eAAK,OAAO,SAAS,CAAC,UAAU,CAAC;QACnC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,0BAA0B,KAAK;AAC7C,eAAK,QAAQ;QACf;OACD;IACH;EACF;;qCA/CW,wBAAqB,4BAAA,aAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,SAAA,yCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,4EAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,mBAAA,GAAA,CAAA,QAAA,SAAA,GAAA,cAAA,UAAA,kBAAA,gBAAA,QAAA,QAAA,WAAA,UAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,WAAA,GAAA,CAAA,GAAA,aAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,mBAAA,eAAA,GAAA,CAAA,GAAA,QAAA,QAAA,WAAA,QAAA,mBAAA,cAAA,GAAA,CAAA,GAAA,WAAA,aAAA,eAAA,eAAA,GAAA,CAAA,GAAA,QAAA,aAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,GAAA,CAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,cAAA,oBAAA,sBAAA,gBAAA,uBAAA,sBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,QAAA,QAAA,WAAA,kBAAA,YAAA,SAAA,GAAA,CAAA,GAAA,WAAA,eAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,iBAAA,WAAA,eAAA,GAAA,CAAA,GAAA,YAAA,QAAA,QAAA,WAAA,kBAAA,YAAA,SAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACblC,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,sCAAA,GAAA,GAAA,OAAA,CAAA,EAAmE,GAAA,sCAAA,GAAA,GAAA,OAAA,CAAA,EAI8C,GAAA,sCAAA,IAAA,IAAA,OAAA,CAAA;AA6DnH,MAAA,uBAAA;;;AAjEQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA,IAAA,OAAA;;oBDDI,cAAY,MAAA,QAAA,GAAA,QAAA,CAAA,0YAAA,EAAA,CAAA;;;sEAIX,uBAAqB,CAAA;UAPjC;uBACW,qBAAmB,YACjB,MAAI,SACP,CAAC,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,iZAAA,EAAA,CAAA;;;;6EAIZ,uBAAqB,EAAA,WAAA,yBAAA,UAAA,qEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}