import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/patients/patient.routes.ts
var PATIENT_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-DSSV2JIX.mjs").then((m) => m.PatientListComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-list/patient-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-S3TWLZAR.mjs").then((m) => m.PatientFormComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-EPZ6TC5L.mjs").then((m) => m.PatientDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-detail/patient-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-S3TWLZAR.mjs").then((m) => m.PatientFormComponent)
  }, true ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {})
];
export {
  PATIENT_ROUTES
};
//# sourceMappingURL=chunk-JP2J6WID.mjs.map
