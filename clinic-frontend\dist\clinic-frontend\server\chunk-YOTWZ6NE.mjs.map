{"version": 3, "sources": ["src/app/core/services/storage.service.ts", "src/app/core/services/auth.service.ts"], "sourcesContent": ["import { Injectable, PLATFORM_ID, inject } from '@angular/core';\r\nimport { isPlatformBrowser } from '@angular/common';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StorageService {\r\n  private platformId = inject(PLATFORM_ID);\r\n\r\n  getItem(key: string): string | null {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      return localStorage.getItem(key);\r\n    }\r\n    return null;\r\n  }\r\n\r\n  setItem(key: string, value: string): void {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      localStorage.setItem(key, value);\r\n    }\r\n  }\r\n\r\n  removeItem(key: string): void {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      localStorage.removeItem(key);\r\n    }\r\n  }\r\n} ", "import { Injectable, inject } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, tap, catchError, throwError } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\nimport { UserRole, LoginRequest, LoginResponse, RegisterRequest, RefreshTokenResponse, User } from '../models/auth.model';\r\nimport { StorageService } from './storage.service';\r\n\r\n// Remove duplicate interfaces - they're now imported from auth.model.ts\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private http = inject(HttpClient);\r\n  private storage = inject(StorageService);\r\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  \r\n  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\r\n  currentUser$ = this.currentUserSubject.asObservable();\r\n\r\n  constructor() {\r\n    this.checkAuthStatus();\r\n  }\r\n\r\n  private checkAuthStatus(): void {\r\n    const token = this.getToken();\r\n    this.isAuthenticatedSubject.next(!!token);\r\n    if (token) {\r\n      this.loadCurrentUser();\r\n    }\r\n  }\r\n\r\n  private loadCurrentUser(): void {\r\n    this.http.get<User>(`${environment.apiUrl}/auth/me`).pipe(\r\n      catchError((error) => {\r\n        console.error('Failed to load current user:', error);\r\n        this.logout();\r\n        return throwError(() => error);\r\n      })\r\n    ).subscribe({\r\n      next: (user) => this.currentUserSubject.next(user),\r\n      error: () => this.logout()\r\n    });\r\n  }\r\n\r\n  login(request: LoginRequest): Observable<LoginResponse> {\r\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login`, request)\r\n      .pipe(\r\n        tap((response: LoginResponse) => {\r\n          this.storage.setItem('auth_token', response.token);\r\n          this.storage.setItem('token_expiration', response.expiration);\r\n          this.isAuthenticatedSubject.next(true);\r\n          this.currentUserSubject.next(response.user);\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Login failed:', error);\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  register(request: RegisterRequest): Observable<any> {\r\n    return this.http.post(`${environment.apiUrl}/auth/register`, request);\r\n  }\r\n\r\n  initializeAdmin(request: RegisterRequest): Observable<any> {\r\n    return this.http.post(`${environment.apiUrl}/auth/initialize-admin`, request);\r\n  }\r\n\r\n  refreshToken(): Observable<RefreshTokenResponse> {\r\n    return this.http.post<RefreshTokenResponse>(`${environment.apiUrl}/auth/refresh-token`, {})\r\n      .pipe(\r\n        tap((response: RefreshTokenResponse) => {\r\n          this.storage.setItem('auth_token', response.token);\r\n          this.storage.setItem('token_expiration', response.expiration);\r\n          this.currentUserSubject.next(response.user);\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Token refresh failed:', error);\r\n          this.logout();\r\n          return throwError(() => error);\r\n        })\r\n      );\r\n  }\r\n\r\n  logout(): void {\r\n    this.storage.removeItem('auth_token');\r\n    this.storage.removeItem('token_expiration');\r\n    this.isAuthenticatedSubject.next(false);\r\n    this.currentUserSubject.next(null);\r\n  }\r\n\r\n  getToken(): string | null {\r\n    return this.storage.getItem('auth_token');\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return !!this.getToken();\r\n  }\r\n\r\n  hasRole(roles: UserRole[]): boolean {\r\n    const user = this.currentUserSubject.value;\r\n    return user ? roles.includes(user.role) : false;\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;AAMM,IAAO,iBAAP,MAAO,gBAAc;EACjB,aAAa,OAAO,WAAW;EAEvC,QAAQ,KAAW;AACjB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,aAAO,aAAa,QAAQ,GAAG;IACjC;AACA,WAAO;EACT;EAEA,QAAQ,KAAa,OAAa;AAChC,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,mBAAa,QAAQ,KAAK,KAAK;IACjC;EACF;EAEA,WAAW,KAAW;AACpB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,mBAAa,WAAW,GAAG;IAC7B;EACF;;qCApBW,iBAAc;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;;;ACOK,IAAO,cAAP,MAAO,aAAW;EACd,OAAO,OAAO,UAAU;EACxB,UAAU,OAAO,cAAc;EAC/B,yBAAyB,IAAI,gBAAyB,KAAK;EAC3D,qBAAqB,IAAI,gBAA6B,IAAI;EAElE,mBAAmB,KAAK,uBAAuB,aAAY;EAC3D,eAAe,KAAK,mBAAmB,aAAY;EAEnD,cAAA;AACE,SAAK,gBAAe;EACtB;EAEQ,kBAAe;AACrB,UAAM,QAAQ,KAAK,SAAQ;AAC3B,SAAK,uBAAuB,KAAK,CAAC,CAAC,KAAK;AACxC,QAAI,OAAO;AACT,WAAK,gBAAe;IACtB;EACF;EAEQ,kBAAe;AACrB,SAAK,KAAK,IAAU,GAAG,YAAY,MAAM,UAAU,EAAE,KACnD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAK,OAAM;AACX,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC,EACF,UAAU;MACV,MAAM,CAAC,SAAS,KAAK,mBAAmB,KAAK,IAAI;MACjD,OAAO,MAAM,KAAK,OAAM;KACzB;EACH;EAEA,MAAM,SAAqB;AACzB,WAAO,KAAK,KAAK,KAAoB,GAAG,YAAY,MAAM,eAAe,OAAO,EAC7E,KACC,IAAI,CAAC,aAA2B;AAC9B,WAAK,QAAQ,QAAQ,cAAc,SAAS,KAAK;AACjD,WAAK,QAAQ,QAAQ,oBAAoB,SAAS,UAAU;AAC5D,WAAK,uBAAuB,KAAK,IAAI;AACrC,WAAK,mBAAmB,KAAK,SAAS,IAAI;IAC5C,CAAC,GACD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,iBAAiB,KAAK;AACpC,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAER;EAEA,SAAS,SAAwB;AAC/B,WAAO,KAAK,KAAK,KAAK,GAAG,YAAY,MAAM,kBAAkB,OAAO;EACtE;EAEA,gBAAgB,SAAwB;AACtC,WAAO,KAAK,KAAK,KAAK,GAAG,YAAY,MAAM,0BAA0B,OAAO;EAC9E;EAEA,eAAY;AACV,WAAO,KAAK,KAAK,KAA2B,GAAG,YAAY,MAAM,uBAAuB,CAAA,CAAE,EACvF,KACC,IAAI,CAAC,aAAkC;AACrC,WAAK,QAAQ,QAAQ,cAAc,SAAS,KAAK;AACjD,WAAK,QAAQ,QAAQ,oBAAoB,SAAS,UAAU;AAC5D,WAAK,mBAAmB,KAAK,SAAS,IAAI;IAC5C,CAAC,GACD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAK,OAAM;AACX,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAER;EAEA,SAAM;AACJ,SAAK,QAAQ,WAAW,YAAY;AACpC,SAAK,QAAQ,WAAW,kBAAkB;AAC1C,SAAK,uBAAuB,KAAK,KAAK;AACtC,SAAK,mBAAmB,KAAK,IAAI;EACnC;EAEA,WAAQ;AACN,WAAO,KAAK,QAAQ,QAAQ,YAAY;EAC1C;EAEA,kBAAe;AACb,WAAO,CAAC,CAAC,KAAK,SAAQ;EACxB;EAEA,QAAQ,OAAiB;AACvB,UAAM,OAAO,KAAK,mBAAmB;AACrC,WAAO,OAAO,MAAM,SAAS,KAAK,IAAI,IAAI;EAC5C;;qCA5FW,cAAW;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;", "names": []}