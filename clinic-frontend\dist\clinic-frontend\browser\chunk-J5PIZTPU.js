import {
  environment
} from "./chunk-7NNESOLN.js";
import {
  HttpClient,
  HttpParams,
  Injectable,
  catchError,
  setClassMetadata,
  throwError,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-BMSBKD5S.js";

// src/app/core/services/patient.service.ts
var PatientService = class _PatientService {
  http;
  apiUrl = `${environment.apiUrl}/frontend/patients`;
  // Use frontend-compatible endpoints
  constructor(http) {
    this.http = http;
  }
  getAll(pageNumber = 1, pageSize = 10, searchTerm) {
    let params = new HttpParams().set("pageNumber", pageNumber.toString()).set("pageSize", pageSize.toString()).set("skip", ((pageNumber - 1) * pageSize).toString()).set("take", pageSize.toString());
    if (searchTerm) {
      params = params.set("searchTerm", searchTerm);
    }
    return this.http.get(this.apiUrl, { params }).pipe(catchError((error) => {
      console.error("Failed to fetch patients:", error);
      return throwError(() => error);
    }));
  }
  getById(id) {
    return this.http.get(`${this.apiUrl}/${id}`).pipe(catchError((error) => {
      console.error(`Failed to fetch patient ${id}:`, error);
      return throwError(() => error);
    }));
  }
  getDetails(id) {
    return this.http.get(`${this.apiUrl}/${id}/details`).pipe(catchError((error) => {
      console.error(`Failed to fetch patient details ${id}:`, error);
      return throwError(() => error);
    }));
  }
  create(patient) {
    return this.http.post(this.apiUrl, patient).pipe(catchError((error) => {
      console.error("Failed to create patient:", error);
      return throwError(() => error);
    }));
  }
  update(id, patient) {
    patient.id = id;
    return this.http.put(`${this.apiUrl}/${id}`, patient).pipe(catchError((error) => {
      console.error(`Failed to update patient ${id}:`, error);
      return throwError(() => error);
    }));
  }
  delete(id) {
    return this.http.delete(`${this.apiUrl}/${id}`).pipe(catchError((error) => {
      console.error(`Failed to delete patient ${id}:`, error);
      return throwError(() => error);
    }));
  }
  // Convenience methods for backward compatibility
  getPatients(pageNumber = 1, pageSize = 10, searchTerm) {
    return this.getAll(pageNumber, pageSize, searchTerm);
  }
  deletePatient(id) {
    return this.delete(id);
  }
  static \u0275fac = function PatientService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PatientService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _PatientService, factory: _PatientService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PatientService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  PatientService
};
//# sourceMappingURL=chunk-J5PIZTPU.js.map
