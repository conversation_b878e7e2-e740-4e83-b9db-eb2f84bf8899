import './polyfills.server.mjs';
import {
  DoctorService
} from "./chunk-FZPYHGK6.mjs";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-VHJENVVD.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  Router,
  RouterLink,
  RouterModule,
  Subject,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate2
} from "./chunk-BUZS6RN2.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/doctors/doctor-list/doctor-list.component.ts
var _c0 = (a0) => ["/doctors", a0];
var _c1 = (a0) => ["/doctors/edit", a0];
function DoctorListComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275element(1, "div", 20);
    \u0275\u0275elementEnd();
  }
}
function DoctorListComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21);
    \u0275\u0275text(1, " No doctors found. Please try a different search term or add a new doctor. ");
    \u0275\u0275elementEnd();
  }
}
function DoctorListComponent_div_19_tr_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr")(1, "td", 28)(2, "div", 29)(3, "div", 30)(4, "div", 31);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(6, "td", 28)(7, "div", 32);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "td", 28)(10, "div", 32);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "td", 28)(13, "div", 32);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "td", 33)(16, "div", 34)(17, "a", 35);
    \u0275\u0275text(18, "View");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "a", 36);
    \u0275\u0275text(20, "Edit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "button", 37);
    \u0275\u0275listener("click", function DoctorListComponent_div_19_tr_15_Template_button_click_21_listener() {
      const doctor_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.onDelete(doctor_r2.id));
    });
    \u0275\u0275text(22, "Delete");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const doctor_r2 = ctx.$implicit;
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate2("", doctor_r2.firstName, " ", doctor_r2.lastName, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(doctor_r2.specialization);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(doctor_r2.email);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(doctor_r2.phoneNumber);
    \u0275\u0275advance(3);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(7, _c0, doctor_r2.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(9, _c1, doctor_r2.id));
  }
}
function DoctorListComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22)(1, "table", 23)(2, "thead", 24)(3, "tr")(4, "th", 25);
    \u0275\u0275text(5, "Name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th", 25);
    \u0275\u0275text(7, "Specialization");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th", 25);
    \u0275\u0275text(9, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "th", 25);
    \u0275\u0275text(11, "Phone");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "th", 25);
    \u0275\u0275text(13, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(14, "tbody", 26);
    \u0275\u0275template(15, DoctorListComponent_div_19_tr_15_Template, 23, 11, "tr", 27);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(15);
    \u0275\u0275property("ngForOf", ctx_r2.doctors);
  }
}
function DoctorListComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 38)(1, "div", 39)(2, "div", 40);
    \u0275\u0275text(3, " Showing ");
    \u0275\u0275elementStart(4, "span", 41);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275text(6, " to ");
    \u0275\u0275elementStart(7, "span", 41);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275text(9, " of ");
    \u0275\u0275elementStart(10, "span", 41);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275text(12, " results ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 34)(14, "button", 42);
    \u0275\u0275listener("click", function DoctorListComponent_div_20_Template_button_click_14_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPageChange(ctx_r2.currentPage - 1));
    });
    \u0275\u0275text(15, " Previous ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "button", 42);
    \u0275\u0275listener("click", function DoctorListComponent_div_20_Template_button_click_16_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPageChange(ctx_r2.currentPage + 1));
    });
    \u0275\u0275text(17, " Next ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r2.currentPage - 1) * ctx_r2.pageSize + 1);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.Math.min(ctx_r2.currentPage * ctx_r2.pageSize, ctx_r2.totalItems));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.totalItems);
    \u0275\u0275advance(3);
    \u0275\u0275property("disabled", ctx_r2.currentPage === 1);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r2.currentPage * ctx_r2.pageSize >= ctx_r2.totalItems);
  }
}
var DoctorListComponent = class _DoctorListComponent {
  doctorService;
  router;
  doctors = [];
  loading = false;
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  searchTerm = "";
  searchTermChanged = new Subject();
  Math = Math;
  // Make Math available in the template
  constructor(doctorService, router) {
    this.doctorService = doctorService;
    this.router = router;
    this.searchTermChanged.pipe(debounceTime(300), distinctUntilChanged()).subscribe((term) => {
      this.searchTerm = term;
      this.currentPage = 1;
      this.loadDoctors();
    });
  }
  ngOnInit() {
    this.loadDoctors();
  }
  loadDoctors() {
    this.loading = true;
    this.doctorService.getPaginatedDoctors(this.currentPage, this.pageSize, this.searchTerm).subscribe({
      next: (response) => {
        this.doctors = response.items;
        this.totalItems = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading doctors:", error);
        this.loading = false;
      }
    });
  }
  onSearch(searchTerm) {
    this.searchTermChanged.next(searchTerm);
  }
  onPageChange(page) {
    this.currentPage = page;
    this.loadDoctors();
  }
  onDelete(id) {
    if (confirm("Are you sure you want to delete this doctor?")) {
      this.loading = true;
      this.doctorService.delete(id).subscribe({
        next: () => {
          this.loadDoctors();
        },
        error: (error) => {
          console.error("Error deleting doctor:", error);
          this.loading = false;
        }
      });
    }
  }
  static \u0275fac = function DoctorListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DoctorListComponent)(\u0275\u0275directiveInject(DoctorService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DoctorListComponent, selectors: [["app-doctor-list"]], decls: 21, vars: 5, consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "flex", "justify-between", "items-center", "mb-6"], [1, "text-3xl", "font-bold", "text-gray-900"], ["routerLink", "/doctors/add", 1, "bg-primary-600", "hover:bg-primary-700", "text-white", "font-medium", "py-2", "px-4", "rounded-md", "flex", "items-center"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", 1, "h-5", "w-5", "mr-2"], ["fill-rule", "evenodd", "d", "M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z", "clip-rule", "evenodd"], [1, "bg-white", "rounded-lg", "shadow", "mb-6"], [1, "p-4", "border-b", "border-gray-200"], [1, "flex", "flex-col", "md:flex-row", "md:items-center", "md:justify-between"], [1, "flex-1", "mb-4", "md:mb-0"], [1, "relative"], ["type", "text", "placeholder", "Search doctors...", 1, "w-full", "pl-10", "pr-4", "py-2", "border", "border-gray-300", "rounded-md", "focus:outline-none", "focus:ring-2", "focus:ring-primary-500", "focus:border-primary-500", 3, "ngModelChange", "ngModel"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", 1, "h-5", "w-5", "text-gray-400"], ["fill-rule", "evenodd", "d", "M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z", "clip-rule", "evenodd"], ["class", "flex justify-center items-center p-8", 4, "ngIf"], ["class", "p-8 text-center text-gray-500", 4, "ngIf"], ["class", "overflow-x-auto", 4, "ngIf"], ["class", "px-6 py-4 border-t border-gray-200", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "p-8"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "p-8", "text-center", "text-gray-500"], [1, "overflow-x-auto"], [1, "min-w-full", "divide-y", "divide-gray-200"], [1, "bg-gray-50"], ["scope", "col", 1, "px-6", "py-3", "text-left", "text-xs", "font-medium", "text-gray-500", "uppercase", "tracking-wider"], [1, "bg-white", "divide-y", "divide-gray-200"], [4, "ngFor", "ngForOf"], [1, "px-6", "py-4", "whitespace-nowrap"], [1, "flex", "items-center"], [1, "ml-4"], [1, "text-sm", "font-medium", "text-gray-900"], [1, "text-sm", "text-gray-900"], [1, "px-6", "py-4", "whitespace-nowrap", "text-sm", "font-medium"], [1, "flex", "space-x-2"], [1, "text-primary-600", "hover:text-primary-900", 3, "routerLink"], [1, "text-indigo-600", "hover:text-indigo-900", 3, "routerLink"], [1, "text-red-600", "hover:text-red-900", 3, "click"], [1, "px-6", "py-4", "border-t", "border-gray-200"], [1, "flex", "justify-between", "items-center"], [1, "text-sm", "text-gray-700"], [1, "font-medium"], [1, "px-3", "py-1", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "bg-white", "hover:bg-gray-50", "disabled:opacity-50", "disabled:cursor-not-allowed", 3, "click", "disabled"]], template: function DoctorListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1", 2);
      \u0275\u0275text(3, "Doctors");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "a", 3);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(5, "svg", 4);
      \u0275\u0275element(6, "path", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275text(7, " Add Doctor ");
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(8, "div", 6)(9, "div", 7)(10, "div", 8)(11, "div", 9)(12, "div", 10)(13, "input", 11);
      \u0275\u0275listener("ngModelChange", function DoctorListComponent_Template_input_ngModelChange_13_listener($event) {
        return ctx.onSearch($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "div", 12);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(15, "svg", 13);
      \u0275\u0275element(16, "path", 14);
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275template(17, DoctorListComponent_div_17_Template, 2, 0, "div", 15)(18, DoctorListComponent_div_18_Template, 2, 0, "div", 16)(19, DoctorListComponent_div_19_Template, 16, 1, "div", 17)(20, DoctorListComponent_div_20_Template, 18, 5, "div", 18);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(13);
      \u0275\u0275property("ngModel", ctx.searchTerm);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.doctors.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.doctors.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.totalItems > ctx.pageSize);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule, RouterLink, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.action-button[_ngcontent-%COMP%] {\n  transition: all 0.2s ease;\n}\n.action-button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-1px);\n}\n/*# sourceMappingURL=doctor-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DoctorListComponent, [{
    type: Component,
    args: [{ selector: "app-doctor-list", standalone: true, imports: [CommonModule, RouterModule, FormsModule], template: `<div class="container mx-auto px-4 py-6">\r
  <div class="flex justify-between items-center mb-6">\r
    <h1 class="text-3xl font-bold text-gray-900">Doctors</h1>\r
    <a routerLink="/doctors/add" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md flex items-center">\r
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">\r
        <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />\r
      </svg>\r
      Add Doctor\r
    </a>\r
  </div>\r
\r
  <div class="bg-white rounded-lg shadow mb-6">\r
    <div class="p-4 border-b border-gray-200">\r
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">\r
        <div class="flex-1 mb-4 md:mb-0">\r
          <div class="relative">\r
            <input \r
              type="text" \r
              placeholder="Search doctors..." \r
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"\r
              [ngModel]="searchTerm"\r
              (ngModelChange)="onSearch($event)"\r
            >\r
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">\r
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">\r
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />\r
              </svg>\r
            </div>\r
          </div>\r
        </div>\r
      </div>\r
    </div>\r
\r
    <div *ngIf="loading" class="flex justify-center items-center p-8">\r
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>\r
    </div>\r
\r
    <div *ngIf="!loading && doctors.length === 0" class="p-8 text-center text-gray-500">\r
      No doctors found. Please try a different search term or add a new doctor.\r
    </div>\r
\r
    <div *ngIf="!loading && doctors.length > 0" class="overflow-x-auto">\r
      <table class="min-w-full divide-y divide-gray-200">\r
        <thead class="bg-gray-50">\r
          <tr>\r
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>\r
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Specialization</th>\r
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>\r
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>\r
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>\r
          </tr>\r
        </thead>\r
        <tbody class="bg-white divide-y divide-gray-200">\r
          <tr *ngFor="let doctor of doctors">\r
            <td class="px-6 py-4 whitespace-nowrap">\r
              <div class="flex items-center">\r
                <div class="ml-4">\r
                  <div class="text-sm font-medium text-gray-900">{{ doctor.firstName }} {{ doctor.lastName }}</div>\r
                </div>\r
              </div>\r
            </td>\r
            <td class="px-6 py-4 whitespace-nowrap">\r
              <div class="text-sm text-gray-900">{{ doctor.specialization }}</div>\r
            </td>\r
            <td class="px-6 py-4 whitespace-nowrap">\r
              <div class="text-sm text-gray-900">{{ doctor.email }}</div>\r
            </td>\r
            <td class="px-6 py-4 whitespace-nowrap">\r
              <div class="text-sm text-gray-900">{{ doctor.phoneNumber }}</div>\r
            </td>\r
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">\r
              <div class="flex space-x-2">\r
                <a [routerLink]="['/doctors', doctor.id]" class="text-primary-600 hover:text-primary-900">View</a>\r
                <a [routerLink]="['/doctors/edit', doctor.id]" class="text-indigo-600 hover:text-indigo-900">Edit</a>\r
                <button (click)="onDelete(doctor.id)" class="text-red-600 hover:text-red-900">Delete</button>\r
              </div>\r
            </td>\r
          </tr>\r
        </tbody>\r
      </table>\r
    </div>\r
\r
    <!-- Pagination -->\r
    <div *ngIf="totalItems > pageSize" class="px-6 py-4 border-t border-gray-200">\r
      <div class="flex justify-between items-center">\r
        <div class="text-sm text-gray-700">\r
          Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to \r
          <span class="font-medium">{{ Math.min(currentPage * pageSize, totalItems) }}</span> of \r
          <span class="font-medium">{{ totalItems }}</span> results\r
        </div>\r
        <div class="flex space-x-2">\r
          <button \r
            [disabled]="currentPage === 1"\r
            (click)="onPageChange(currentPage - 1)"\r
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"\r
          >\r
            Previous\r
          </button>\r
          <button \r
            [disabled]="currentPage * pageSize >= totalItems"\r
            (click)="onPageChange(currentPage + 1)"\r
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"\r
          >\r
            Next\r
          </button>\r
        </div>\r
      </div>\r
    </div>\r
  </div>\r
</div> `, styles: ["/* src/app/features/doctors/doctor-list/doctor-list.component.scss */\n:host {\n  display: block;\n}\n.table-container {\n  overflow-x: auto;\n}\n.action-button {\n  transition: all 0.2s ease;\n}\n.action-button:hover {\n  transform: translateY(-1px);\n}\n/*# sourceMappingURL=doctor-list.component.css.map */\n"] }]
  }], () => [{ type: DoctorService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DoctorListComponent, { className: "DoctorListComponent", filePath: "src/app/features/doctors/doctor-list/doctor-list.component.ts", lineNumber: 17 });
})();
export {
  DoctorListComponent
};
//# sourceMappingURL=chunk-EHBYXIUL.mjs.map
