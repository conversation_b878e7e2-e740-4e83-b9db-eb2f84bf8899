import {
  <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>or,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-T7IIKLN2.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-2NNV54NL.js";
import {
  PatientService
} from "./chunk-VSNPJ6BI.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵpureFunction3,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-7FZJUQ36.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/patients/patient-list/patient-list.component.ts
var _c0 = () => ["/patients/new"];
var _c1 = (a0, a1, a2) => ({ "bg-blue-100 text-blue-800": a0, "bg-pink-100 text-pink-800": a1, "bg-gray-100 text-gray-800": a2 });
var _c2 = (a0) => ["/patients", a0];
var _c3 = (a0) => ["/patients", a0, "edit"];
function PatientListComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27)(1, "div", 28)(2, "div", 29);
    \u0275\u0275element(3, "div", 30);
    \u0275\u0275elementStart(4, "p", 31);
    \u0275\u0275text(5, "Loading patients...");
    \u0275\u0275elementEnd()()()();
  }
}
function PatientListComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 32)(1, "div", 33)(2, "div", 34);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 35);
    \u0275\u0275element(4, "path", 36);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(5, "div", 37)(6, "p", 38);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function PatientListComponent_div_32_tr_19_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 59);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const patient_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", patient_r3.middleName, " ");
  }
}
function PatientListComponent_div_32_tr_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 49)(1, "td", 50)(2, "div", 51)(3, "div", 52)(4, "div", 53)(5, "span", 54);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(7, "div", 55)(8, "div", 56);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275template(10, PatientListComponent_div_32_tr_19_div_10_Template, 2, 1, "div", 57);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(11, "td", 50)(12, "div", 58);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "div", 59);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(16, "td", 60);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "td", 50)(19, "span", 61);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(21, "td", 62);
    \u0275\u0275text(22);
    \u0275\u0275pipe(23, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "td", 63)(25, "div", 64)(26, "button", 65);
    \u0275\u0275text(27, " View ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "button", 66);
    \u0275\u0275text(29, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "button", 67);
    \u0275\u0275listener("click", function PatientListComponent_div_32_tr_19_Template_button_click_30_listener() {
      const patient_r3 = \u0275\u0275restoreView(_r2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deletePatient(patient_r3.id));
    });
    \u0275\u0275text(31, " Delete ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const patient_r3 = ctx.$implicit;
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate2(" ", patient_r3.firstName.charAt(0), "", patient_r3.lastName.charAt(0), " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" ", patient_r3.firstName, " ", patient_r3.lastName, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", patient_r3.middleName);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(patient_r3.email);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(patient_r3.phoneNumber);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", patient_r3.age, " years ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction3(16, _c1, patient_r3.gender === "Male", patient_r3.gender === "Female", patient_r3.gender === "Other" || patient_r3.gender === "PreferNotToSay"));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", patient_r3.gender, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(23, 13, patient_r3.registrationDate, "MMM d, y"), " ");
    \u0275\u0275advance(4);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(20, _c2, patient_r3.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(22, _c3, patient_r3.id));
  }
}
function PatientListComponent_div_32_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 68);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 69);
    \u0275\u0275element(2, "path", 70);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(3, "h3", 71);
    \u0275\u0275text(4, "No patients found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p", 5);
    \u0275\u0275text(6, "Get started by adding a new patient.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 72)(8, "button", 73);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(9, "svg", 7);
    \u0275\u0275element(10, "path", 8);
    \u0275\u0275elementEnd();
    \u0275\u0275text(11, " Add Patient ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    \u0275\u0275advance(8);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(1, _c0));
  }
}
function PatientListComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 39)(1, "div", 40)(2, "table", 41)(3, "thead", 42)(4, "tr")(5, "th", 43);
    \u0275\u0275text(6, " Patient ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "th", 43);
    \u0275\u0275text(8, " Contact ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "th", 43);
    \u0275\u0275text(10, " Age ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "th", 43);
    \u0275\u0275text(12, " Gender ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "th", 43);
    \u0275\u0275text(14, " Registration ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "th", 44)(16, "span", 45);
    \u0275\u0275text(17, "Actions");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(18, "tbody", 46);
    \u0275\u0275template(19, PatientListComponent_div_32_tr_19_Template, 32, 24, "tr", 47);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(20, PatientListComponent_div_32_div_20_Template, 12, 2, "div", 48);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(19);
    \u0275\u0275property("ngForOf", ctx_r0.patients)("ngForTrackBy", ctx_r0.trackByPatientId);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.patients.length === 0);
  }
}
var PatientListComponent = class _PatientListComponent {
  patientService;
  patients = [];
  loading = false;
  error = "";
  searchTerm = "";
  currentPage = 1;
  pageSize = 10;
  constructor(patientService) {
    this.patientService = patientService;
  }
  ngOnInit() {
    this.loadPatients();
  }
  loadPatients() {
    this.loading = true;
    this.error = "";
    this.patientService.getAll(this.currentPage, this.pageSize, this.searchTerm).subscribe({
      next: (patients) => {
        this.patients = patients;
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load patients. Please try again.";
        this.loading = false;
        console.error("Error loading patients:", error);
      }
    });
  }
  onSearch() {
    this.currentPage = 1;
    this.loadPatients();
  }
  trackByPatientId(index, patient) {
    return patient.id;
  }
  deletePatient(id) {
    if (confirm("Are you sure you want to delete this patient? This action cannot be undone.")) {
      this.patientService.delete(id).subscribe({
        next: () => {
          this.loadPatients();
          console.log("Patient deleted successfully");
        },
        error: (error) => {
          this.error = "Failed to delete patient. Please try again.";
          console.error("Error deleting patient:", error);
        }
      });
    }
  }
  static \u0275fac = function PatientListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PatientListComponent)(\u0275\u0275directiveInject(PatientService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PatientListComponent, selectors: [["app-patient-list"]], decls: 33, vars: 6, consts: [[1, "min-h-screen", "bg-gray-50"], [1, "bg-white", "shadow-sm", "border-b", "border-gray-200"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8"], [1, "flex", "justify-between", "items-center", "py-6"], [1, "text-3xl", "font-bold", "text-gray-900"], [1, "mt-1", "text-sm", "text-gray-500"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "text-sm", "font-medium", "rounded-md", "shadow-sm", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "transition-colors", "duration-200", 3, "routerLink"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "-ml-1", "mr-2", "h-5", "w-5"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 6v6m0 0v6m0-6h6m-6 0H6"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8", "py-8"], [1, "bg-white", "rounded-lg", "shadow-sm", "border", "border-gray-200", "mb-6"], [1, "p-6"], [1, "flex", "flex-col", "sm:flex-row", "gap-4"], [1, "flex-1"], ["for", "search", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], [1, "relative"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-5", "w-5", "text-gray-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"], ["id", "search", "type", "text", "placeholder", "Search by name, email, or phone...", 1, "block", "w-full", "pl-10", "pr-3", "py-2", "border", "border-gray-300", "rounded-md", "leading-5", "bg-white", "placeholder-gray-500", "focus:outline-none", "focus:placeholder-gray-400", "focus:ring-1", "focus:ring-indigo-500", "focus:border-indigo-500", 3, "ngModelChange", "input", "ngModel"], [1, "flex", "items-end"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-gray-300", "text-sm", "font-medium", "rounded-md", "text-gray-700", "bg-white", "hover:bg-gray-50", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "transition-colors", "duration-200", 3, "click"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "-ml-1", "mr-2", "h-4", "w-4"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"], ["class", "bg-white rounded-lg shadow-sm border border-gray-200", 4, "ngIf"], ["class", "bg-red-50 border border-red-200 rounded-lg p-4 mb-6", 4, "ngIf"], ["class", "bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden", 4, "ngIf"], [1, "bg-white", "rounded-lg", "shadow-sm", "border", "border-gray-200"], [1, "flex", "justify-center", "items-center", "py-12"], [1, "flex", "flex-col", "items-center"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-indigo-600"], [1, "mt-4", "text-sm", "text-gray-500"], [1, "bg-red-50", "border", "border-red-200", "rounded-lg", "p-4", "mb-6"], [1, "flex"], [1, "flex-shrink-0"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-5", "w-5", "text-red-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "ml-3"], [1, "text-sm", "text-red-800"], [1, "bg-white", "rounded-lg", "shadow-sm", "border", "border-gray-200", "overflow-hidden"], [1, "overflow-x-auto"], [1, "min-w-full", "divide-y", "divide-gray-200"], [1, "bg-gray-50"], ["scope", "col", 1, "px-6", "py-3", "text-left", "text-xs", "font-medium", "text-gray-500", "uppercase", "tracking-wider"], ["scope", "col", 1, "relative", "px-6", "py-3"], [1, "sr-only"], [1, "bg-white", "divide-y", "divide-gray-200"], ["class", "hover:bg-gray-50 transition-colors duration-150", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "text-center py-12", 4, "ngIf"], [1, "hover:bg-gray-50", "transition-colors", "duration-150"], [1, "px-6", "py-4", "whitespace-nowrap"], [1, "flex", "items-center"], [1, "flex-shrink-0", "h-10", "w-10"], [1, "h-10", "w-10", "rounded-full", "bg-indigo-100", "flex", "items-center", "justify-center"], [1, "text-sm", "font-medium", "text-indigo-700"], [1, "ml-4"], [1, "text-sm", "font-medium", "text-gray-900"], ["class", "text-sm text-gray-500", 4, "ngIf"], [1, "text-sm", "text-gray-900"], [1, "text-sm", "text-gray-500"], [1, "px-6", "py-4", "whitespace-nowrap", "text-sm", "text-gray-900"], [1, "inline-flex", "px-2", "py-1", "text-xs", "font-semibold", "rounded-full", 3, "ngClass"], [1, "px-6", "py-4", "whitespace-nowrap", "text-sm", "text-gray-500"], [1, "px-6", "py-4", "whitespace-nowrap", "text-right", "text-sm", "font-medium"], [1, "flex", "items-center", "justify-end", "space-x-2"], [1, "text-indigo-600", "hover:text-indigo-900", "transition-colors", "duration-150", 3, "routerLink"], [1, "text-yellow-600", "hover:text-yellow-900", "transition-colors", "duration-150", 3, "routerLink"], [1, "text-red-600", "hover:text-red-900", "transition-colors", "duration-150", 3, "click"], [1, "text-center", "py-12"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "mx-auto", "h-12", "w-12", "text-gray-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"], [1, "mt-2", "text-sm", "font-medium", "text-gray-900"], [1, "mt-6"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "shadow-sm", "text-sm", "font-medium", "rounded-md", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", 3, "routerLink"]], template: function PatientListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div")(5, "h1", 4);
      \u0275\u0275text(6, "Patients");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "p", 5);
      \u0275\u0275text(8, "Manage patient records and information");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "button", 6);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(10, "svg", 7);
      \u0275\u0275element(11, "path", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275text(12, " Add Patient ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(13, "div", 9)(14, "div", 10)(15, "div", 11)(16, "div", 12)(17, "div", 13)(18, "label", 14);
      \u0275\u0275text(19, "Search Patients");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "div", 15)(21, "div", 16);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(22, "svg", 17);
      \u0275\u0275element(23, "path", 18);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(24, "input", 19);
      \u0275\u0275twoWayListener("ngModelChange", function PatientListComponent_Template_input_ngModelChange_24_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("input", function PatientListComponent_Template_input_input_24_listener() {
        return ctx.onSearch();
      });
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(25, "div", 20)(26, "button", 21);
      \u0275\u0275listener("click", function PatientListComponent_Template_button_click_26_listener() {
        return ctx.loadPatients();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(27, "svg", 22);
      \u0275\u0275element(28, "path", 23);
      \u0275\u0275elementEnd();
      \u0275\u0275text(29, " Refresh ");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275template(30, PatientListComponent_div_30_Template, 6, 0, "div", 24)(31, PatientListComponent_div_31_Template, 8, 1, "div", 25)(32, PatientListComponent_div_32_Template, 21, 3, "div", 26);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(9);
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(5, _c0));
      \u0275\u0275advance(15);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error && !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && !ctx.error);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PatientListComponent, [{
    type: Component,
    args: [{
      selector: "app-patient-list",
      standalone: true,
      imports: [CommonModule, RouterModule, FormsModule],
      template: `
    <div class="min-h-screen bg-gray-50">
      <!-- Header Section -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">Patients</h1>
              <p class="mt-1 text-sm text-gray-500">Manage patient records and information</p>
            </div>
            <button
              [routerLink]="['/patients/new']"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Add Patient
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="p-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Patients</label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                  </div>
                  <input
                    id="search"
                    type="text"
                    [(ngModel)]="searchTerm"
                    (input)="onSearch()"
                    class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Search by name, email, or phone..."
                  />
                </div>
              </div>
              <div class="flex items-end">
                <button
                  (click)="loadPatients()"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                  <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="flex justify-center items-center py-12">
            <div class="flex flex-col items-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
              <p class="mt-4 text-sm text-gray-500">Loading patients...</p>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Patients Table -->
        <div *ngIf="!loading && !error" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Patient
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Age
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gender
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Registration
                  </th>
                  <th scope="col" class="relative px-6 py-3">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr *ngFor="let patient of patients; trackBy: trackByPatientId" class="hover:bg-gray-50 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                          <span class="text-sm font-medium text-indigo-700">
                            {{ patient.firstName.charAt(0) }}{{ patient.lastName.charAt(0) }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ patient.firstName }} {{ patient.lastName }}
                        </div>
                        <div class="text-sm text-gray-500" *ngIf="patient.middleName">
                          {{ patient.middleName }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ patient.email }}</div>
                    <div class="text-sm text-gray-500">{{ patient.phoneNumber }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ patient.age }} years
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          [ngClass]="{
                            'bg-blue-100 text-blue-800': patient.gender === 'Male',
                            'bg-pink-100 text-pink-800': patient.gender === 'Female',
                            'bg-gray-100 text-gray-800': patient.gender === 'Other' || patient.gender === 'PreferNotToSay'
                          }">
                      {{ patient.gender }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ patient.registrationDate | date:'MMM d, y' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <button
                        [routerLink]="['/patients', patient.id]"
                        class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">
                        View
                      </button>
                      <button
                        [routerLink]="['/patients', patient.id, 'edit']"
                        class="text-yellow-600 hover:text-yellow-900 transition-colors duration-150">
                        Edit
                      </button>
                      <button
                        (click)="deletePatient(patient.id)"
                        class="text-red-600 hover:text-red-900 transition-colors duration-150">
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div *ngIf="patients.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No patients found</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by adding a new patient.</p>
            <div class="mt-6">
              <button
                [routerLink]="['/patients/new']"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Add Patient
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
    }]
  }], () => [{ type: PatientService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PatientListComponent, { className: "PatientListComponent", filePath: "src/app/features/patients/patient-list/patient-list.component.ts", lineNumber: 210 });
})();
export {
  PatientListComponent
};
//# sourceMappingURL=chunk-LM2JHUKF.js.map
