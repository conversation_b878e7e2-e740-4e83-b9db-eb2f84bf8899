import './polyfills.server.mjs';
import {
  environment
} from "./chunk-QSZZESH5.mjs";
import {
  BehaviorSubject,
  HttpClient,
  Injectable,
  PLATFORM_ID,
  catchError,
  inject,
  isPlatformBrowser,
  setClassMetadata,
  tap,
  throwError,
  ɵɵdefineInjectable
} from "./chunk-BUZS6RN2.mjs";

// src/app/core/services/storage.service.ts
var StorageService = class _StorageService {
  platformId = inject(PLATFORM_ID);
  getItem(key) {
    if (isPlatformBrowser(this.platformId)) {
      return localStorage.getItem(key);
    }
    return null;
  }
  setItem(key, value) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem(key, value);
    }
  }
  removeItem(key) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem(key);
    }
  }
  static \u0275fac = function StorageService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _StorageService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _StorageService, factory: _StorageService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StorageService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();

// src/app/core/services/auth.service.ts
var AuthService = class _AuthService {
  http = inject(HttpClient);
  storage = inject(StorageService);
  isAuthenticatedSubject = new BehaviorSubject(false);
  currentUserSubject = new BehaviorSubject(null);
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  currentUser$ = this.currentUserSubject.asObservable();
  constructor() {
    this.checkAuthStatus();
  }
  checkAuthStatus() {
    const token = this.getToken();
    this.isAuthenticatedSubject.next(!!token);
    if (token) {
      this.loadCurrentUser();
    }
  }
  loadCurrentUser() {
    this.http.get(`${environment.apiUrl}/auth/me`).pipe(catchError((error) => {
      console.error("Failed to load current user:", error);
      this.logout();
      return throwError(() => error);
    })).subscribe({
      next: (user) => this.currentUserSubject.next(user),
      error: () => this.logout()
    });
  }
  login(request) {
    return this.http.post(`${environment.apiUrl}/auth/login`, request).pipe(tap((response) => {
      this.storage.setItem("auth_token", response.token);
      this.storage.setItem("token_expiration", response.expiration);
      this.isAuthenticatedSubject.next(true);
      this.currentUserSubject.next(response.user);
    }), catchError((error) => {
      console.error("Login failed:", error);
      return throwError(() => error);
    }));
  }
  register(request) {
    return this.http.post(`${environment.apiUrl}/auth/register`, request);
  }
  initializeAdmin(request) {
    return this.http.post(`${environment.apiUrl}/auth/initialize-admin`, request);
  }
  refreshToken() {
    return this.http.post(`${environment.apiUrl}/auth/refresh-token`, {}).pipe(tap((response) => {
      this.storage.setItem("auth_token", response.token);
      this.storage.setItem("token_expiration", response.expiration);
      this.currentUserSubject.next(response.user);
    }), catchError((error) => {
      console.error("Token refresh failed:", error);
      this.logout();
      return throwError(() => error);
    }));
  }
  logout() {
    this.storage.removeItem("auth_token");
    this.storage.removeItem("token_expiration");
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
  }
  getToken() {
    return this.storage.getItem("auth_token");
  }
  isAuthenticated() {
    return !!this.getToken();
  }
  hasRole(roles) {
    const user = this.currentUserSubject.value;
    return user ? roles.includes(user.role) : false;
  }
  static \u0275fac = function AuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();

export {
  StorageService,
  AuthService
};
//# sourceMappingURL=chunk-FFAYD5LW.mjs.map
