import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/user-management/user-management.routes.ts
var USER_MANAGEMENT_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-XLQEIRSH.mjs").then((m) => m.UserListComponent)
  }, true ? { \u0275entryName: "src/app/features/user-management/user-list/user-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-S35PICXH.mjs").then((m) => m.UserFormComponent)
  }, true ? { \u0275entryName: "src/app/features/user-management/user-form/user-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-N5CFNU5F.mjs").then((m) => m.UserDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/user-management/user-detail/user-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-S35PICXH.mjs").then((m) => m.UserFormComponent)
  }, true ? { \u0275entryName: "src/app/features/user-management/user-form/user-form.component.ts" } : {})
];
export {
  USER_MANAGEMENT_ROUTES
};
//# sourceMappingURL=chunk-3VQGHJP2.mjs.map
