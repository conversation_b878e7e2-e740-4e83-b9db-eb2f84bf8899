import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/patients/patient.routes.ts
var PATIENT_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-LM2JHUKF.js").then((m) => m.PatientListComponent)
  }, false ? { \u0275entryName: "src/app/features/patients/patient-list/patient-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-DBS2LLZV.js").then((m) => m.PatientFormComponent)
  }, false ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-RHSUN3IT.js").then((m) => m.PatientDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/patients/patient-detail/patient-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-DBS2LLZV.js").then((m) => m.PatientFormComponent)
  }, false ? { \u0275entryName: "src/app/features/patients/patient-form/patient-form.component.ts" } : {})
];
export {
  PATIENT_ROUTES
};
//# sourceMappingURL=chunk-MFEWHNXC.js.map
