import {
  DoctorService
} from "./chunk-T7JLGHDG.js";
import {
  Activated<PERSON>out<PERSON>,
  Router
} from "./chunk-2NNV54NL.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate2
} from "./chunk-7FZJUQ36.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/doctors/doctor-detail/doctor-detail.component.ts
function DoctorDetailComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "div", 5);
    \u0275\u0275elementEnd();
  }
}
function DoctorDetailComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6)(1, "strong", 7);
    \u0275\u0275text(2, "Error!");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 8);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function DoctorDetailComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 9)(1, "div", 10)(2, "div", 11)(3, "div")(4, "h3", 12);
    \u0275\u0275text(5, "Doctor Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p", 13);
    \u0275\u0275text(7, "Personal details and specialization.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 14)(9, "button", 15);
    \u0275\u0275listener("click", function DoctorDetailComponent_div_3_Template_button_click_9_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.router.navigate(["/doctors", ctx_r0.doctor.id, "edit"]));
    });
    \u0275\u0275text(10, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "button", 16);
    \u0275\u0275listener("click", function DoctorDetailComponent_div_3_Template_button_click_11_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onDelete());
    });
    \u0275\u0275text(12, " Delete ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(13, "div", 17)(14, "dl")(15, "div", 18)(16, "dt", 19);
    \u0275\u0275text(17, "Full name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "dd", 20);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "div", 21)(21, "dt", 19);
    \u0275\u0275text(22, "Email address");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "dd", 20);
    \u0275\u0275text(24);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(25, "div", 18)(26, "dt", 19);
    \u0275\u0275text(27, "Phone number");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "dd", 20);
    \u0275\u0275text(29);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(30, "div", 21)(31, "dt", 19);
    \u0275\u0275text(32, "Specialization");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "dd", 20);
    \u0275\u0275text(34);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(35, "div", 18)(36, "dt", 19);
    \u0275\u0275text(37, "License number");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "dd", 20);
    \u0275\u0275text(39);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(40, "div", 21)(41, "dt", 19);
    \u0275\u0275text(42, "Created at");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(43, "dd", 20);
    \u0275\u0275text(44);
    \u0275\u0275pipe(45, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(46, "div", 18)(47, "dt", 19);
    \u0275\u0275text(48, "Last updated");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "dd", 20);
    \u0275\u0275text(50);
    \u0275\u0275pipe(51, "date");
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(19);
    \u0275\u0275textInterpolate2("", ctx_r0.doctor.firstName, " ", ctx_r0.doctor.lastName, "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.doctor.email);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.doctor.phoneNumber);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.doctor.specialization);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.doctor.licenseNumber);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(45, 8, ctx_r0.doctor.createdAt, "medium"));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(51, 11, ctx_r0.doctor.updatedAt, "medium"));
  }
}
var DoctorDetailComponent = class _DoctorDetailComponent {
  doctorService;
  route;
  router;
  doctor = null;
  loading = true;
  error = null;
  constructor(doctorService, route, router) {
    this.doctorService = doctorService;
    this.route = route;
    this.router = router;
  }
  ngOnInit() {
    const doctorId = this.route.snapshot.paramMap.get("id");
    if (doctorId) {
      this.loadDoctor(doctorId);
    } else {
      this.error = "Doctor ID not provided";
      this.loading = false;
    }
  }
  loadDoctor(id) {
    this.doctorService.getDoctor(id).subscribe({
      next: (doctor) => {
        this.doctor = doctor;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading doctor:", error);
        this.error = "Failed to load doctor details";
        this.loading = false;
      }
    });
  }
  onDelete() {
    if (this.doctor && confirm("Are you sure you want to delete this doctor?")) {
      this.doctorService.deleteDoctor(this.doctor.id).subscribe({
        next: () => {
          this.router.navigate(["/doctors"]);
        },
        error: (error) => {
          console.error("Error deleting doctor:", error);
          this.error = "Failed to delete doctor";
        }
      });
    }
  }
  static \u0275fac = function DoctorDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DoctorDetailComponent)(\u0275\u0275directiveInject(DoctorService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DoctorDetailComponent, selectors: [["app-doctor-detail"]], decls: 4, vars: 3, consts: [[1, "container", "mx-auto", "px-4", "py-8"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative", "role", "alert", 4, "ngIf"], ["class", "max-w-4xl mx-auto", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-indigo-600"], ["role", "alert", 1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "relative"], [1, "font-bold"], [1, "block", "sm:inline"], [1, "max-w-4xl", "mx-auto"], [1, "bg-white", "shadow", "overflow-hidden", "sm:rounded-lg"], [1, "px-4", "py-5", "sm:px-6", "flex", "justify-between", "items-center"], [1, "text-lg", "leading-6", "font-medium", "text-gray-900"], [1, "mt-1", "max-w-2xl", "text-sm", "text-gray-500"], [1, "flex", "space-x-4"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", 3, "click"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-red-600", "hover:bg-red-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-red-500", 3, "click"], [1, "border-t", "border-gray-200"], [1, "bg-gray-50", "px-4", "py-5", "sm:grid", "sm:grid-cols-3", "sm:gap-4", "sm:px-6"], [1, "text-sm", "font-medium", "text-gray-500"], [1, "mt-1", "text-sm", "text-gray-900", "sm:mt-0", "sm:col-span-2"], [1, "bg-white", "px-4", "py-5", "sm:grid", "sm:grid-cols-3", "sm:gap-4", "sm:px-6"]], template: function DoctorDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, DoctorDetailComponent_div_1_Template, 2, 0, "div", 1)(2, DoctorDetailComponent_div_2_Template, 5, 1, "div", 2)(3, DoctorDetailComponent_div_3_Template, 52, 14, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.doctor && !ctx.loading);
    }
  }, dependencies: [CommonModule, NgIf, DatePipe], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  min-height: calc(100vh - 64px);\n  background-color: #f9fafb;\n}\n.animate-spin[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=doctor-detail.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DoctorDetailComponent, [{
    type: Component,
    args: [{ selector: "app-doctor-detail", standalone: true, imports: [CommonModule], template: `<div class="container mx-auto px-4 py-8">\r
  <div *ngIf="loading" class="flex justify-center items-center h-64">\r
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>\r
  </div>\r
\r
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">\r
    <strong class="font-bold">Error!</strong>\r
    <span class="block sm:inline">{{ error }}</span>\r
  </div>\r
\r
  <div *ngIf="doctor && !loading" class="max-w-4xl mx-auto">\r
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">\r
      <div class="px-4 py-5 sm:px-6 flex justify-between items-center">\r
        <div>\r
          <h3 class="text-lg leading-6 font-medium text-gray-900">Doctor Information</h3>\r
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Personal details and specialization.</p>\r
        </div>\r
        <div class="flex space-x-4">\r
          <button\r
            (click)="router.navigate(['/doctors', doctor.id, 'edit'])"\r
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"\r
          >\r
            Edit\r
          </button>\r
          <button\r
            (click)="onDelete()"\r
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"\r
          >\r
            Delete\r
          </button>\r
        </div>\r
      </div>\r
      <div class="border-t border-gray-200">\r
        <dl>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Full name</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.firstName }} {{ doctor.lastName }}</dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Email address</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.email }}</dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Phone number</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.phoneNumber }}</dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Specialization</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.specialization }}</dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">License number</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.licenseNumber }}</dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Created at</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.createdAt | date:'medium' }}</dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Last updated</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ doctor.updatedAt | date:'medium' }}</dd>\r
          </div>\r
        </dl>\r
      </div>\r
    </div>\r
  </div>\r
</div> `, styles: ["/* src/app/features/doctors/doctor-detail/doctor-detail.component.scss */\n:host {\n  display: block;\n  min-height: calc(100vh - 64px);\n  background-color: #f9fafb;\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n/*# sourceMappingURL=doctor-detail.component.css.map */\n"] }]
  }], () => [{ type: DoctorService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DoctorDetailComponent, { className: "DoctorDetailComponent", filePath: "src/app/features/doctors/doctor-detail/doctor-detail.component.ts", lineNumber: 14 });
})();
export {
  DoctorDetailComponent
};
//# sourceMappingURL=chunk-MRUK3FKL.js.map
