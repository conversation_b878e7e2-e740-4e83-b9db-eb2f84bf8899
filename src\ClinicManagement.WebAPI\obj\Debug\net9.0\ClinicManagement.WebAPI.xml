<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ClinicManagement.WebAPI</name>
    </assembly>
    <members>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.GetById(System.Int32)">
            <summary>
            Gets an appointment by ID
            </summary>
            <param name="id">Appointment ID</param>
            <returns>Appointment details</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.GetByDateRange(System.DateTime,System.DateTime,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Gets appointments by date range
            </summary>
            <param name="startDate">Start date</param>
            <param name="endDate">End date</param>
            <param name="doctorId">Optional doctor ID</param>
            <param name="patientId">Optional patient ID</param>
            <returns>List of appointments</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.Create(ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand)">
            <summary>
            Creates a new appointment
            </summary>
            <param name="command">Appointment data</param>
            <returns>ID of the created appointment</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.Update(System.Int32,ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand)">
            <summary>
            Updates an existing appointment
            </summary>
            <param name="id">Appointment ID</param>
            <param name="command">Updated appointment data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.Cancel(System.Int32,ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand)">
            <summary>
            Cancels an appointment
            </summary>
            <param name="id">Appointment ID</param>
            <param name="command">Cancellation details</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AppointmentsController.CancelAppointment(System.Int32)">
            <summary>
            Cancels an appointment (alternative endpoint for simple cancellation)
            </summary>
            <param name="id">Appointment ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AuthController.Login(ClinicManagement.WebAPI.Controllers.LoginRequest)">
            <summary>
            Authenticates a user and returns a JWT token
            </summary>
            <param name="request">Login credentials</param>
            <returns>JWT token and user information</returns>
            <response code="200">Returns the JWT token and user information</response>
            <response code="401">If the credentials are invalid</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AuthController.Register(ClinicManagement.WebAPI.Controllers.RegisterRequest)">
            <summary>
            Registers a new user
            </summary>
            <param name="request">Registration information</param>
            <returns>Success message</returns>
            <response code="200">Returns success message</response>
            <response code="400">If the email is already taken or other validation errors</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AuthController.InitializeAdmin(ClinicManagement.WebAPI.Controllers.InitializeAdminRequest)">
            <summary>
            Creates the initial admin user if no users exist in the system
            </summary>
            <param name="request">Admin user information</param>
            <returns>Success message</returns>
            <response code="200">Returns success message</response>
            <response code="400">If users already exist or other validation errors</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AuthController.GetCurrentUser">
            <summary>
            Gets the current authenticated user's information
            </summary>
            <returns>Current user information</returns>
            <response code="200">Returns the current user information</response>
            <response code="401">If the user is not authenticated</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.AuthController.RefreshToken">
            <summary>
            Refreshes the JWT token for the current user
            </summary>
            <returns>New JWT token</returns>
            <response code="200">Returns the new JWT token</response>
            <response code="401">If the user is not authenticated</response>
        </member>
        <member name="T:ClinicManagement.WebAPI.Controllers.DoctorsController">
            <summary>
            API endpoints for managing doctors in the clinic system
            </summary>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.DoctorsController.#ctor(MediatR.IMediator)">
            <summary>
            Initializes a new instance of the DoctorsController
            </summary>
            <param name="mediator">Mediator instance for CQRS pattern</param>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.DoctorsController.GetDoctors(System.String)">
            <summary>
            Gets a list of all doctors with optional filtering by specialization
            </summary>
            <param name="specialization">Optional specialization to filter by</param>
            <returns>A list of doctors matching the criteria</returns>
            <response code="200">Returns the list of doctors</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.DoctorsController.GetDoctor(System.Int32)">
            <summary>
            Gets a specific doctor by their ID
            </summary>
            <param name="id">The ID of the doctor</param>
            <returns>Detailed information about the doctor</returns>
            <response code="200">Returns the doctor details</response>
            <response code="404">If the doctor is not found</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.DoctorsController.GetDoctorSchedule(System.Int32)">
            <summary>
            Gets the schedule for a specific doctor
            </summary>
            <param name="id">The ID of the doctor</param>
            <returns>The doctor's schedule information</returns>
            <response code="200">Returns the doctor's schedule</response>
            <response code="404">If the doctor is not found</response>
        </member>
        <member name="T:ClinicManagement.WebAPI.Controllers.FrontendPatientsController">
            <summary>
            Frontend-compatible API endpoints for managing patients (returns unwrapped responses)
            </summary>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.GetPatients(ClinicManagement.Application.Features.Patients.Queries.GetPatientsList.GetPatientsListQuery)">
            <summary>
            Gets a list of patients with optional filtering and pagination (unwrapped response)
            </summary>
            <param name="query">Query parameters for filtering and pagination</param>
            <returns>A list of patients matching the criteria</returns>
            <response code="200">Returns the list of patients</response>
            <response code="400">If the query parameters are invalid</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.GetPatientDetails(System.Int32)">
            <summary>
            Gets detailed information about a specific patient (unwrapped response)
            </summary>
            <param name="id">The ID of the patient</param>
            <returns>Detailed information about the patient</returns>
            <response code="200">Returns the patient details</response>
            <response code="404">If the patient is not found</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.GetById(System.Int32)">
            <summary>
            Gets a patient by ID (unwrapped response)
            </summary>
            <param name="id">Patient ID</param>
            <returns>Patient details</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.Create(ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand)">
            <summary>
            Creates a new patient (unwrapped response)
            </summary>
            <param name="command">Patient data</param>
            <returns>ID of the created patient</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.Update(System.Int32,ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand)">
            <summary>
            Updates an existing patient (unwrapped response)
            </summary>
            <param name="id">Patient ID</param>
            <param name="command">Updated patient data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.FrontendPatientsController.Delete(System.Int32)">
            <summary>
            Deletes a patient (unwrapped response)
            </summary>
            <param name="id">Patient ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.MedicalRecordsController.GetById(System.Int32)">
            <summary>
            Gets a medical record by ID
            </summary>
            <param name="id">Medical record ID</param>
            <returns>Medical record details</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.MedicalRecordsController.GetByPatient(System.Int32,System.Int32,System.Int32)">
            <summary>
            Gets all medical records for a patient
            </summary>
            <param name="patientId">Patient ID</param>
            <param name="skip">Number of records to skip</param>
            <param name="take">Number of records to take</param>
            <returns>List of medical records</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.MedicalRecordsController.Create(ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand)">
            <summary>
            Creates a new medical record
            </summary>
            <param name="command">Medical record data</param>
            <returns>ID of the created medical record</returns>
        </member>
        <member name="T:ClinicManagement.WebAPI.Controllers.PatientsController">
            <summary>
            API endpoints for managing patients in the clinic system
            </summary>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.GetPatients(ClinicManagement.Application.Features.Patients.Queries.GetPatientsList.GetPatientsListQuery)">
            <summary>
            Gets a list of patients with optional filtering and pagination
            </summary>
            <param name="query">Query parameters for filtering and pagination</param>
            <returns>A list of patients matching the criteria</returns>
            <response code="200">Returns the list of patients</response>
            <response code="400">If the query parameters are invalid</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.GetPatientDetails(System.Int32)">
            <summary>
            Gets detailed information about a specific patient
            </summary>
            <param name="id">The ID of the patient</param>
            <returns>Detailed information about the patient</returns>
            <response code="200">Returns the patient details</response>
            <response code="404">If the patient is not found</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.GetById(System.Int32)">
            <summary>
            Gets a patient by ID
            </summary>
            <param name="id">Patient ID</param>
            <returns>Patient details</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.Create(ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand)">
            <summary>
            Creates a new patient
            </summary>
            <param name="command">Patient data</param>
            <returns>ID of the created patient</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.Update(System.Int32,ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand)">
            <summary>
            Updates an existing patient
            </summary>
            <param name="id">Patient ID</param>
            <param name="command">Updated patient data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.PatientsController.Delete(System.Int32)">
            <summary>
            Deletes a patient
            </summary>
            <param name="id">Patient ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.SchedulesController.GetById(System.Int32)">
            <summary>
            Gets a schedule by ID
            </summary>
            <param name="id">Schedule ID</param>
            <returns>Schedule details</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.SchedulesController.GetByDoctor(System.Int32)">
            <summary>
            Gets all schedules for a doctor
            </summary>
            <param name="doctorId">Doctor ID</param>
            <returns>List of schedules</returns>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.SchedulesController.Create(ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand)">
            <summary>
            Creates a new schedule
            </summary>
            <param name="command">Schedule data</param>
            <returns>ID of the created schedule</returns>
        </member>
        <member name="T:ClinicManagement.WebAPI.Controllers.UserManagementController">
            <summary>
            Admin-only user management endpoints
            </summary>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.GetAllUsers">
            <summary>
            Gets all users in the system
            </summary>
            <returns>List of all users</returns>
            <response code="200">Returns the list of users</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.GetUsersByRole(ClinicManagement.Domain.Enums.UserRole)">
            <summary>
            Gets users by role
            </summary>
            <param name="role">User role to filter by</param>
            <returns>List of users with the specified role</returns>
            <response code="200">Returns the list of users</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.GetUser(System.Int32)">
            <summary>
            Gets a specific user by ID
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
            <response code="200">Returns the user</response>
            <response code="404">If the user is not found</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.CreateUser(ClinicManagement.WebAPI.Controllers.CreateUserRequest)">
            <summary>
            Creates a new user (Admin only)
            </summary>
            <param name="request">User creation data</param>
            <returns>Created user information</returns>
            <response code="201">Returns the created user</response>
            <response code="400">If the request is invalid</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.UpdateUser(System.Int32,ClinicManagement.WebAPI.Controllers.UpdateUserRequest)">
            <summary>
            Updates a user's profile information
            </summary>
            <param name="id">User ID</param>
            <param name="request">Updated user data</param>
            <returns>Success status</returns>
            <response code="200">Returns success</response>
            <response code="400">If the request is invalid</response>
            <response code="404">If the user is not found</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.DeactivateUser(System.Int32)">
            <summary>
            Deactivates a user account
            </summary>
            <param name="id">User ID</param>
            <returns>Success status</returns>
            <response code="200">Returns success</response>
            <response code="404">If the user is not found</response>
            <response code="403">If the user is not an admin</response>
        </member>
        <member name="M:ClinicManagement.WebAPI.Controllers.UserManagementController.ActivateUser(System.Int32)">
            <summary>
            Activates a user account
            </summary>
            <param name="id">User ID</param>
            <returns>Success status</returns>
            <response code="200">Returns success</response>
            <response code="404">If the user is not found</response>
            <response code="403">If the user is not an admin</response>
        </member>
    </members>
</doc>
