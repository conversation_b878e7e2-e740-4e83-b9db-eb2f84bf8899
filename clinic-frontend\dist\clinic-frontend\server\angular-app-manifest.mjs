
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: false,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "src/app/features/auth/not-found/not-found.component.ts": [
    {
      "path": "chunk-PHDRWZP6.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointment-detail/appointment-detail.component.ts": [
    {
      "path": "chunk-IUUXDPJH.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-7S6VBFUQ.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointments.routes.ts": [
    {
      "path": "chunk-CEVYVUKA.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-7FXYY6LB.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-GNJF73EW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-GNJF73EW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-IUUXDPJH.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts": [
    {
      "path": "chunk-U4F44TQH.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts": [
    {
      "path": "chunk-37NKFZAQ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts": [
    {
      "path": "chunk-N36LXRTV.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/medical-records.routes.ts": [
    {
      "path": "chunk-BI322YSX.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-U4F44TQH.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-37NKFZAQ.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-37NKFZAQ.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-N36LXRTV.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/schedules/schedules.routes.ts": [
    {
      "path": "chunk-FLTDDNYX.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/unauthorized/unauthorized.component.ts": [
    {
      "path": "chunk-Y54BQETF.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-detail/patient-detail.component.ts": [
    {
      "path": "chunk-RHSUN3IT.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-VSNPJ6BI.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient.routes.ts": [
    {
      "path": "chunk-MFEWHNXC.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-LM2JHUKF.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-DBS2LLZV.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-RHSUN3IT.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-DBS2LLZV.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/doctors/doctor-list/doctor-list.component.ts": [
    {
      "path": "chunk-XELKQIGP.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7JLGHDG.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctor-form/doctor-form.component.ts": [
    {
      "path": "chunk-TNV662RW.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7JLGHDG.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctor-detail/doctor-detail.component.ts": [
    {
      "path": "chunk-MRUK3FKL.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7JLGHDG.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctors.routes.ts": [
    {
      "path": "chunk-62N76AJU.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-XELKQIGP.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-TNV662RW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-TNV662RW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-MRUK3FKL.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/appointments/appointment-list/appointment-list.component.ts": [
    {
      "path": "chunk-7FXYY6LB.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-7S6VBFUQ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointment-form/appointment-form.component.ts": [
    {
      "path": "chunk-GNJF73EW.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7JLGHDG.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-7S6VBFUQ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-VSNPJ6BI.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/login/login.component.ts": [
    {
      "path": "chunk-VWUYDA5N.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/register/register.component.ts": [
    {
      "path": "chunk-CKSYCCPR.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/initialize-admin/initialize-admin.component.ts": [
    {
      "path": "chunk-GEDO4QZ2.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    }
  ],
  "src/app/layouts/main-layout/main-layout.component.ts": [
    {
      "path": "chunk-XYKCDUQR.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/dashboard/dashboard.component.ts": [
    {
      "path": "chunk-CZULAT3Z.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-VSNPJ6BI.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-list/patient-list.component.ts": [
    {
      "path": "chunk-LM2JHUKF.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-VSNPJ6BI.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-form/patient-form.component.ts": [
    {
      "path": "chunk-DBS2LLZV.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T7IIKLN2.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-VSNPJ6BI.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 750, hash: '2597ad01536dfe2f5c9803edeff8260fee0bcb02d19d69db5095180e03efac43', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1290, hash: '1294cd5a28a0f3c2cfce9d9110d263ebb63e41004911276f97bc3d82e11f2d58', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)}
  },
};
