
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: false,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {
  "src/app/features/user-management/user-detail/user-detail.component.ts": [
    {
      "path": "chunk-HOML5JBJ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BOTCETOX.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/user-management/user-management.routes.ts": [
    {
      "path": "chunk-6MWCDQ3W.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-T273NTF3.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-C6PMV3VT.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-HOML5JBJ.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-C6PMV3VT.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/auth/unauthorized/unauthorized.component.ts": [
    {
      "path": "chunk-MM3DXMGT.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/not-found/not-found.component.ts": [
    {
      "path": "chunk-75EU7HGB.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointments.routes.ts": [
    {
      "path": "chunk-OVLIP7PR.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-2K4IJDJA.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-OTILAL4T.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-OTILAL4T.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-SZEF4SCF.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts": [
    {
      "path": "chunk-XSG5HTCQ.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts": [
    {
      "path": "chunk-KLER3UXW.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts": [
    {
      "path": "chunk-DISNHZIJ.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/medical-records/medical-records.routes.ts": [
    {
      "path": "chunk-QCDAS7AN.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-XSG5HTCQ.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-KLER3UXW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-KLER3UXW.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-DISNHZIJ.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/schedules/schedules.routes.ts": [
    {
      "path": "chunk-FLTDDNYX.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/user-management/user-list/user-list.component.ts": [
    {
      "path": "chunk-T273NTF3.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BOTCETOX.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/user-management/user-form/user-form.component.ts": [
    {
      "path": "chunk-C6PMV3VT.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-BOTCETOX.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient.routes.ts": [
    {
      "path": "chunk-GQDWVQWQ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HF6EXBHS.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-63FT2YZH.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-SSDQASRP.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-63FT2YZH.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/doctors/doctor-list/doctor-list.component.ts": [
    {
      "path": "chunk-A4AJHFYF.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-3RROYE6Q.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctor-form/doctor-form.component.ts": [
    {
      "path": "chunk-6JA7UBJD.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-3RROYE6Q.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctor-detail/doctor-detail.component.ts": [
    {
      "path": "chunk-NTRTXUYG.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-3RROYE6Q.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/doctors/doctors.routes.ts": [
    {
      "path": "chunk-QR4MKBZJ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-A4AJHFYF.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-6JA7UBJD.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-6JA7UBJD.js",
      "dynamicImport": true
    },
    {
      "path": "chunk-NTRTXUYG.js",
      "dynamicImport": true
    }
  ],
  "src/app/features/appointments/appointment-list/appointment-list.component.ts": [
    {
      "path": "chunk-2K4IJDJA.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HEWIZI3Y.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointment-form/appointment-form.component.ts": [
    {
      "path": "chunk-OTILAL4T.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-3RROYE6Q.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HEWIZI3Y.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-J5PIZTPU.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/appointments/appointment-detail/appointment-detail.component.ts": [
    {
      "path": "chunk-SZEF4SCF.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-HEWIZI3Y.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/login/login.component.ts": [
    {
      "path": "chunk-PZDBKYJJ.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/auth/initialize-admin/initialize-admin.component.ts": [
    {
      "path": "chunk-IKN3VDOA.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    }
  ],
  "src/app/layouts/main-layout/main-layout.component.ts": [
    {
      "path": "chunk-VWFESNYS.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/dashboard/dashboard.component.ts": [
    {
      "path": "chunk-ODODX7WI.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-J5PIZTPU.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-list/patient-list.component.ts": [
    {
      "path": "chunk-HF6EXBHS.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-J5PIZTPU.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-form/patient-form.component.ts": [
    {
      "path": "chunk-63FT2YZH.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-UQU4VP33.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-J5PIZTPU.js",
      "dynamicImport": false
    }
  ],
  "src/app/features/patients/patient-detail/patient-detail.component.ts": [
    {
      "path": "chunk-SSDQASRP.js",
      "dynamicImport": false
    },
    {
      "path": "chunk-J5PIZTPU.js",
      "dynamicImport": false
    }
  ]
},
  assets: {
    'index.csr.html': {size: 699, hash: '791b7928e8e5f48b4ca7486c253ad686f036400a2b4123ebc1de91496571e44a', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 1239, hash: '5a4eb898b36bb074988d42886ab68b59f93375062ced03bb96a3be6c22858bbd', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)}
  },
};
