{"version": 3, "sources": ["src/app/core/services/patient.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpParams } from '@angular/common/http';\r\nimport { Observable, catchError, throwError } from 'rxjs';\r\nimport { Patient, CreatePatientRequest, UpdatePatientRequest } from '../models/patient.model';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PatientService {\r\n  private apiUrl = `${environment.apiUrl}/frontend/patients`;  // Use frontend-compatible endpoints\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getAll(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<Patient[]> {\r\n    let params = new HttpParams()\r\n      .set('pageNumber', pageNumber.toString())\r\n      .set('pageSize', pageSize.toString())\r\n      .set('skip', ((pageNumber - 1) * pageSize).toString())\r\n      .set('take', pageSize.toString());\r\n\r\n    if (searchTerm) {\r\n      params = params.set('searchTerm', searchTerm);\r\n    }\r\n\r\n    return this.http.get<Patient[]>(this.apiUrl, { params }).pipe(\r\n      catchError((error) => {\r\n        console.error('Failed to fetch patients:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  getById(id: number): Observable<Patient> {\r\n    return this.http.get<Patient>(`${this.apiUrl}/${id}`).pipe(\r\n      catchError((error) => {\r\n        console.error(`Failed to fetch patient ${id}:`, error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  getDetails(id: number): Observable<Patient> {\r\n    return this.http.get<Patient>(`${this.apiUrl}/${id}/details`).pipe(\r\n      catchError((error) => {\r\n        console.error(`Failed to fetch patient details ${id}:`, error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  create(patient: CreatePatientRequest): Observable<number> {\r\n    return this.http.post<number>(this.apiUrl, patient).pipe(\r\n      catchError((error) => {\r\n        console.error('Failed to create patient:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  update(id: number, patient: UpdatePatientRequest): Observable<boolean> {\r\n    patient.id = id; // Ensure ID is set\r\n    return this.http.put<boolean>(`${this.apiUrl}/${id}`, patient).pipe(\r\n      catchError((error) => {\r\n        console.error(`Failed to update patient ${id}:`, error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  delete(id: number): Observable<boolean> {\r\n    return this.http.delete<boolean>(`${this.apiUrl}/${id}`).pipe(\r\n      catchError((error) => {\r\n        console.error(`Failed to delete patient ${id}:`, error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Convenience methods for backward compatibility\r\n  getPatients(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<Patient[]> {\r\n    return this.getAll(pageNumber, pageSize, searchTerm);\r\n  }\r\n\r\n  deletePatient(id: number): Observable<boolean> {\r\n    return this.delete(id);\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,iBAAP,MAAO,gBAAc;EAGL;EAFZ,SAAS,GAAG,YAAY,MAAM;;EAEtC,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;EAEvC,OAAO,aAAqB,GAAG,WAAmB,IAAI,YAAmB;AACvE,QAAI,SAAS,IAAI,WAAU,EACxB,IAAI,cAAc,WAAW,SAAQ,CAAE,EACvC,IAAI,YAAY,SAAS,SAAQ,CAAE,EACnC,IAAI,UAAU,aAAa,KAAK,UAAU,SAAQ,CAAE,EACpD,IAAI,QAAQ,SAAS,SAAQ,CAAE;AAElC,QAAI,YAAY;AACd,eAAS,OAAO,IAAI,cAAc,UAAU;IAC9C;AAEA,WAAO,KAAK,KAAK,IAAe,KAAK,QAAQ,EAAE,OAAM,CAAE,EAAE,KACvD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,6BAA6B,KAAK;AAChD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,QAAQ,IAAU;AAChB,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE,EAAE,KACpD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,2BAA2B,EAAE,KAAK,KAAK;AACrD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,WAAW,IAAU;AACnB,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,MAAM,IAAI,EAAE,UAAU,EAAE,KAC5D,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,mCAAmC,EAAE,KAAK,KAAK;AAC7D,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,OAAO,SAA6B;AAClC,WAAO,KAAK,KAAK,KAAa,KAAK,QAAQ,OAAO,EAAE,KAClD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,6BAA6B,KAAK;AAChD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,OAAO,IAAY,SAA6B;AAC9C,YAAQ,KAAK;AACb,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,MAAM,IAAI,EAAE,IAAI,OAAO,EAAE,KAC7D,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,4BAA4B,EAAE,KAAK,KAAK;AACtD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,OAAO,IAAU;AACf,WAAO,KAAK,KAAK,OAAgB,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE,EAAE,KACvD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,4BAA4B,EAAE,KAAK,KAAK;AACtD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;;EAGA,YAAY,aAAqB,GAAG,WAAmB,IAAI,YAAmB;AAC5E,WAAO,KAAK,OAAO,YAAY,UAAU,UAAU;EACrD;EAEA,cAAc,IAAU;AACtB,WAAO,KAAK,OAAO,EAAE;EACvB;;qCA7EW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}