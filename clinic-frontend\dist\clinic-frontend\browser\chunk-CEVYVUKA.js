import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/appointments/appointments.routes.ts
var APPOINTMENTS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-7FXYY6LB.js").then((m) => m.AppointmentListComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-list/appointment-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-GNJF73EW.js").then((m) => m.AppointmentFormComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-GNJF73EW.js").then((m) => m.AppointmentFormComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-IUUXDPJH.js").then((m) => m.AppointmentDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-detail/appointment-detail.component.ts" } : {})
];
export {
  APPOINTMENTS_ROUTES
};
//# sourceMappingURL=chunk-CEVYVUKA.js.map
