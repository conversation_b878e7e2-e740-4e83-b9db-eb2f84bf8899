{"version": 3, "sources": ["src/app/features/doctors/doctors.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const DOCTORS_ROUTES: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./doctor-list/doctor-list.component').then(m => m.DoctorListComponent)\r\n  },\r\n  {\r\n    path: 'add',\r\n    loadComponent: () => import('./doctor-form/doctor-form.component').then(m => m.DoctorFormComponent)\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    loadComponent: () => import('./doctor-form/doctor-form.component').then(m => m.DoctorFormComponent)\r\n  },\r\n  {\r\n    path: ':id',\r\n    loadComponent: () => import('./doctor-detail/doctor-detail.component').then(m => m.DoctorDetailComponent)\r\n  }\r\n]; "], "mappings": ";;;;;;AAEO,IAAM,iBAAyB;EACpC;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAqC,EAAE,KAAK,OAAK,EAAE,mBAAmB;;EAEpG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAqC,EAAE,KAAK,OAAK,EAAE,mBAAmB;;EAEpG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAqC,EAAE,KAAK,OAAK,EAAE,mBAAmB;;EAEpG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAyC,EAAE,KAAK,OAAK,EAAE,qBAAqB;;;", "names": []}