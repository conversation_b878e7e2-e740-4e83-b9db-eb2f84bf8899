{"version": 3, "sources": ["src/app/features/auth/unauthorized/unauthorized.component.ts", "src/app/features/auth/unauthorized/unauthorized.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-unauthorized',\r\n  templateUrl: './unauthorized.component.html',\r\n  styleUrls: ['./unauthorized.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule]\r\n})\r\nexport class UnauthorizedComponent {\r\n  constructor(private router: Router) {}\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n} ", "<div class=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n  <div class=\"max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md text-center\">\r\n    <div>\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mx-auto h-24 w-24 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n      </svg>\r\n      <h2 class=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Unauthorized Access</h2>\r\n      <p class=\"mt-2 text-center text-sm text-gray-600\">\r\n        You don't have permission to access this resource.\r\n      </p>\r\n    </div>\r\n    \r\n    <div class=\"mt-8\">\r\n      <button \r\n        (click)=\"goBack()\" \r\n        class=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\r\n      >\r\n        <span class=\"absolute left-0 inset-y-0 flex items-center pl-3\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 group-hover:text-primary-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\r\n          </svg>\r\n        </span>\r\n        Back to Dashboard\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;AAWM,IAAO,wBAAP,MAAO,uBAAqB;EACZ;EAApB,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;;qCALW,wBAAqB,4BAAA,MAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,gBAAA,QAAA,gBAAA,kBAAA,cAAA,SAAA,QAAA,WAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,aAAA,YAAA,OAAA,cAAA,aAAA,aAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,WAAA,QAAA,QAAA,cAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,sIAAA,GAAA,CAAA,GAAA,QAAA,eAAA,YAAA,kBAAA,eAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,eAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,SAAA,YAAA,UAAA,QAAA,kBAAA,QAAA,QAAA,UAAA,sBAAA,WAAA,eAAA,cAAA,cAAA,kBAAA,wBAAA,sBAAA,gBAAA,uBAAA,0BAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,UAAA,aAAA,QAAA,gBAAA,MAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,OAAA,OAAA,oBAAA,8BAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,6BAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACXlC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiG,GAAA,OAAA,CAAA,EACV,GAAA,KAAA;;AAEjF,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;;AACA,MAAA,yBAAA,GAAA,MAAA,CAAA;AAAmE,MAAA,iBAAA,GAAA,qBAAA;AAAmB,MAAA,uBAAA;AACtF,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,GAAA,sDAAA;AACF,MAAA,uBAAA,EAAI;AAGN,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,IAAA,UAAA,CAAA;AAEd,MAAA,qBAAA,SAAA,SAAA,0DAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;AAGjB,MAAA,yBAAA,IAAA,QAAA,CAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA,EAAM;AAER,MAAA,iBAAA,IAAA,qBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;;oBDhBI,YAAY,GAAA,QAAA,CAAA,uHAAA,EAAA,CAAA;;;sEAEX,uBAAqB,CAAA;UAPjC;uBACW,oBAAkB,YAGhB,MAAI,SACP,CAAC,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,gLAAA,EAAA,CAAA;;;;6EAEZ,uBAAqB,EAAA,WAAA,yBAAA,UAAA,gEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}