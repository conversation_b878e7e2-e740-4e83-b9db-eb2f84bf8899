import './polyfills.server.mjs';
import {
  Default<PERSON><PERSON><PERSON><PERSON>cc<PERSON>or,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-IIIRLQMQ.mjs";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-YKEX2NSQ.mjs";
import {
  PatientService
} from "./chunk-4ZO7KR3M.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-TCK56SA4.mjs";
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/core/models/patient.model.ts
var Gender;
(function(Gender2) {
  Gender2["Male"] = "Male";
  Gender2["Female"] = "Female";
  Gender2["Other"] = "Other";
  Gender2["PreferNotToSay"] = "PreferNotToSay";
})(Gender || (Gender = {}));

// src/app/features/patients/patient-form/patient-form.component.ts
var _c0 = () => ["/patients"];
function PatientFormComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12)(1, "div", 13)(2, "div", 14);
    \u0275\u0275element(3, "div", 15);
    \u0275\u0275elementStart(4, "p", 16);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.isEditMode ? "Loading patient data..." : "Saving patient...");
  }
}
function PatientFormComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17)(1, "div", 18)(2, "div", 19);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 20);
    \u0275\u0275element(4, "path", 21);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(5, "div", 22)(6, "p", 23);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function PatientFormComponent_div_16_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " First name is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Last name is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Date of birth is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_47_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Gender is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_58_span_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_58_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Please enter a valid email address");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_58_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275template(1, PatientFormComponent_div_16_div_58_span_1_Template, 2, 0, "span", 77)(2, PatientFormComponent_div_16_div_58_span_2_Template, 2, 0, "span", 77);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_2_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["email"]);
  }
}
function PatientFormComponent_div_16_div_65_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Phone number is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_76_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Street address is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_83_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " City is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_90_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " State is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_div_97_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " Postal code is required ");
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16__svg_svg_143_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 78);
    \u0275\u0275element(1, "circle", 79)(2, "path", 80);
    \u0275\u0275elementEnd();
  }
}
function PatientFormComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 12)(1, "form", 24);
    \u0275\u0275listener("ngSubmit", function PatientFormComponent_div_16_Template_form_ngSubmit_1_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onSubmit());
    });
    \u0275\u0275elementStart(2, "div")(3, "h3", 25);
    \u0275\u0275text(4, "Personal Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 26)(6, "div")(7, "label", 27);
    \u0275\u0275text(8, " First Name ");
    \u0275\u0275elementStart(9, "span", 28);
    \u0275\u0275text(10, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(11, "input", 29);
    \u0275\u0275template(12, PatientFormComponent_div_16_div_12_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div")(14, "label", 31);
    \u0275\u0275text(15, " Last Name ");
    \u0275\u0275elementStart(16, "span", 28);
    \u0275\u0275text(17, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(18, "input", 32);
    \u0275\u0275template(19, PatientFormComponent_div_16_div_19_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div")(21, "label", 33);
    \u0275\u0275text(22, " Middle Name ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(23, "input", 34);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "div")(25, "label", 35);
    \u0275\u0275text(26, " Date of Birth ");
    \u0275\u0275elementStart(27, "span", 28);
    \u0275\u0275text(28, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(29, "input", 36);
    \u0275\u0275template(30, PatientFormComponent_div_16_div_30_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(31, "div")(32, "label", 37);
    \u0275\u0275text(33, " Gender ");
    \u0275\u0275elementStart(34, "span", 28);
    \u0275\u0275text(35, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(36, "select", 38)(37, "option", 39);
    \u0275\u0275text(38, "Select gender");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(39, "option", 40);
    \u0275\u0275text(40, "Male");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "option", 40);
    \u0275\u0275text(42, "Female");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(43, "option", 40);
    \u0275\u0275text(44, "Other");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(45, "option", 40);
    \u0275\u0275text(46, "Prefer not to say");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(47, PatientFormComponent_div_16_div_47_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(48, "div", 41)(49, "h3", 25);
    \u0275\u0275text(50, "Contact Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(51, "div", 42)(52, "div")(53, "label", 43);
    \u0275\u0275text(54, " Email Address ");
    \u0275\u0275elementStart(55, "span", 28);
    \u0275\u0275text(56, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(57, "input", 44);
    \u0275\u0275template(58, PatientFormComponent_div_16_div_58_Template, 3, 2, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(59, "div")(60, "label", 45);
    \u0275\u0275text(61, " Phone Number ");
    \u0275\u0275elementStart(62, "span", 28);
    \u0275\u0275text(63, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(64, "input", 46);
    \u0275\u0275template(65, PatientFormComponent_div_16_div_65_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(66, "div", 41)(67, "h3", 25);
    \u0275\u0275text(68, "Address Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(69, "div", 47)(70, "div", 48)(71, "label", 49);
    \u0275\u0275text(72, " Street Address ");
    \u0275\u0275elementStart(73, "span", 28);
    \u0275\u0275text(74, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(75, "input", 50);
    \u0275\u0275template(76, PatientFormComponent_div_16_div_76_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(77, "div")(78, "label", 51);
    \u0275\u0275text(79, " City ");
    \u0275\u0275elementStart(80, "span", 28);
    \u0275\u0275text(81, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(82, "input", 52);
    \u0275\u0275template(83, PatientFormComponent_div_16_div_83_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(84, "div")(85, "label", 53);
    \u0275\u0275text(86, " State ");
    \u0275\u0275elementStart(87, "span", 28);
    \u0275\u0275text(88, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(89, "input", 54);
    \u0275\u0275template(90, PatientFormComponent_div_16_div_90_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(91, "div")(92, "label", 55);
    \u0275\u0275text(93, " Postal Code ");
    \u0275\u0275elementStart(94, "span", 28);
    \u0275\u0275text(95, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(96, "input", 56);
    \u0275\u0275template(97, PatientFormComponent_div_16_div_97_Template, 2, 0, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(98, "div")(99, "label", 57);
    \u0275\u0275text(100, " Country ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(101, "input", 58);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(102, "div", 41)(103, "h3", 25);
    \u0275\u0275text(104, "Medical Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(105, "div", 59)(106, "div")(107, "label", 60);
    \u0275\u0275text(108, " Medical History ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(109, "textarea", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(110, "div")(111, "label", 62);
    \u0275\u0275text(112, " Allergies ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(113, "textarea", 63);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(114, "div", 41)(115, "h3", 25);
    \u0275\u0275text(116, "Emergency Contact");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(117, "div", 42)(118, "div")(119, "label", 64);
    \u0275\u0275text(120, " Emergency Contact Name ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(121, "input", 65);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(122, "div")(123, "label", 66);
    \u0275\u0275text(124, " Emergency Contact Phone ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(125, "input", 67);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(126, "div", 41)(127, "h3", 25);
    \u0275\u0275text(128, "Insurance Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(129, "div", 42)(130, "div")(131, "label", 68);
    \u0275\u0275text(132, " Insurance Provider ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(133, "input", 69);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(134, "div")(135, "label", 70);
    \u0275\u0275text(136, " Policy Number ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(137, "input", 71);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(138, "div", 41)(139, "div", 72)(140, "button", 73);
    \u0275\u0275text(141, " Cancel ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(142, "button", 74);
    \u0275\u0275template(143, PatientFormComponent_div_16__svg_svg_143_Template, 3, 0, "svg", 75);
    \u0275\u0275text(144);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    let tmp_4_0;
    let tmp_5_0;
    let tmp_6_0;
    let tmp_7_0;
    let tmp_8_0;
    let tmp_13_0;
    let tmp_14_0;
    let tmp_15_0;
    let tmp_16_0;
    let tmp_17_0;
    let tmp_18_0;
    let tmp_19_0;
    let tmp_20_0;
    let tmp_21_0;
    let tmp_22_0;
    let tmp_23_0;
    let tmp_24_0;
    let tmp_25_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r0.patientForm);
    \u0275\u0275advance(10);
    \u0275\u0275classProp("border-red-300", ((tmp_2_0 = ctx_r0.patientForm.get("firstName")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.patientForm.get("firstName")) == null ? null : tmp_2_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.patientForm.get("firstName")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.patientForm.get("firstName")) == null ? null : tmp_3_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_4_0 = ctx_r0.patientForm.get("lastName")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.patientForm.get("lastName")) == null ? null : tmp_4_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_5_0 = ctx_r0.patientForm.get("lastName")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.patientForm.get("lastName")) == null ? null : tmp_5_0.touched));
    \u0275\u0275advance(10);
    \u0275\u0275classProp("border-red-300", ((tmp_6_0 = ctx_r0.patientForm.get("dateOfBirth")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.patientForm.get("dateOfBirth")) == null ? null : tmp_6_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_7_0 = ctx_r0.patientForm.get("dateOfBirth")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.patientForm.get("dateOfBirth")) == null ? null : tmp_7_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_8_0 = ctx_r0.patientForm.get("gender")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r0.patientForm.get("gender")) == null ? null : tmp_8_0.touched));
    \u0275\u0275advance(3);
    \u0275\u0275property("value", ctx_r0.Gender.Male);
    \u0275\u0275advance(2);
    \u0275\u0275property("value", ctx_r0.Gender.Female);
    \u0275\u0275advance(2);
    \u0275\u0275property("value", ctx_r0.Gender.Other);
    \u0275\u0275advance(2);
    \u0275\u0275property("value", ctx_r0.Gender.PreferNotToSay);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ((tmp_13_0 = ctx_r0.patientForm.get("gender")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx_r0.patientForm.get("gender")) == null ? null : tmp_13_0.touched));
    \u0275\u0275advance(10);
    \u0275\u0275classProp("border-red-300", ((tmp_14_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_14_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_15_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx_r0.patientForm.get("email")) == null ? null : tmp_15_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_16_0 = ctx_r0.patientForm.get("phoneNumber")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx_r0.patientForm.get("phoneNumber")) == null ? null : tmp_16_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_17_0 = ctx_r0.patientForm.get("phoneNumber")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx_r0.patientForm.get("phoneNumber")) == null ? null : tmp_17_0.touched));
    \u0275\u0275advance(10);
    \u0275\u0275classProp("border-red-300", ((tmp_18_0 = ctx_r0.patientForm.get("street")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx_r0.patientForm.get("street")) == null ? null : tmp_18_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_19_0 = ctx_r0.patientForm.get("street")) == null ? null : tmp_19_0.invalid) && ((tmp_19_0 = ctx_r0.patientForm.get("street")) == null ? null : tmp_19_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_20_0 = ctx_r0.patientForm.get("city")) == null ? null : tmp_20_0.invalid) && ((tmp_20_0 = ctx_r0.patientForm.get("city")) == null ? null : tmp_20_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_21_0 = ctx_r0.patientForm.get("city")) == null ? null : tmp_21_0.invalid) && ((tmp_21_0 = ctx_r0.patientForm.get("city")) == null ? null : tmp_21_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_22_0 = ctx_r0.patientForm.get("state")) == null ? null : tmp_22_0.invalid) && ((tmp_22_0 = ctx_r0.patientForm.get("state")) == null ? null : tmp_22_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_23_0 = ctx_r0.patientForm.get("state")) == null ? null : tmp_23_0.invalid) && ((tmp_23_0 = ctx_r0.patientForm.get("state")) == null ? null : tmp_23_0.touched));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("border-red-300", ((tmp_24_0 = ctx_r0.patientForm.get("postalCode")) == null ? null : tmp_24_0.invalid) && ((tmp_24_0 = ctx_r0.patientForm.get("postalCode")) == null ? null : tmp_24_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_25_0 = ctx_r0.patientForm.get("postalCode")) == null ? null : tmp_25_0.invalid) && ((tmp_25_0 = ctx_r0.patientForm.get("postalCode")) == null ? null : tmp_25_0.touched));
    \u0275\u0275advance(43);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(39, _c0));
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r0.patientForm.invalid || ctx_r0.loading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.loading);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.isEditMode ? "Update Patient" : "Create Patient", " ");
  }
}
var PatientFormComponent = class _PatientFormComponent {
  fb;
  patientService;
  route;
  router;
  patientForm;
  isEditMode = false;
  patientId = null;
  loading = false;
  error = null;
  Gender = Gender;
  // Make Gender enum available in template
  constructor(fb, patientService, route, router) {
    this.fb = fb;
    this.patientService = patientService;
    this.route = route;
    this.router = router;
    this.patientForm = this.fb.group({
      firstName: ["", Validators.required],
      lastName: ["", Validators.required],
      middleName: [""],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: ["", Validators.required],
      dateOfBirth: ["", Validators.required],
      gender: ["", Validators.required],
      street: ["", Validators.required],
      city: ["", Validators.required],
      state: ["", Validators.required],
      postalCode: ["", Validators.required],
      country: ["USA"],
      medicalHistory: [""],
      allergies: [""],
      emergencyContactName: [""],
      emergencyContactPhone: [""],
      insuranceProvider: [""],
      insurancePolicyNumber: [""]
    });
  }
  ngOnInit() {
    const idParam = this.route.snapshot.paramMap.get("id");
    if (idParam) {
      this.patientId = parseInt(idParam, 10);
      this.isEditMode = true;
      this.loadPatient();
    }
  }
  loadPatient() {
    if (!this.patientId)
      return;
    this.loading = true;
    this.error = null;
    this.patientService.getById(this.patientId).subscribe({
      next: (patient) => {
        const dateOfBirth = patient.dateOfBirth ? new Date(patient.dateOfBirth).toISOString().split("T")[0] : "";
        this.patientForm.patchValue({
          firstName: patient.firstName,
          lastName: patient.lastName,
          middleName: patient.middleName || "",
          email: patient.email,
          phoneNumber: patient.phoneNumber,
          dateOfBirth,
          gender: patient.gender,
          street: patient.street,
          city: patient.city,
          state: patient.state,
          postalCode: patient.postalCode,
          country: patient.country || "USA",
          medicalHistory: patient.medicalHistory || "",
          allergies: patient.allergies || "",
          emergencyContactName: patient.emergencyContactName || "",
          emergencyContactPhone: patient.emergencyContactPhone || "",
          insuranceProvider: patient.insuranceProvider || "",
          insurancePolicyNumber: patient.insurancePolicyNumber || ""
        });
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load patient data. Please try again.";
        this.loading = false;
        console.error("Error loading patient:", error);
      }
    });
  }
  onSubmit() {
    if (this.patientForm.valid) {
      this.loading = true;
      this.error = null;
      const formData = this.patientForm.value;
      if (this.isEditMode && this.patientId) {
        const updateData = __spreadValues({
          id: this.patientId
        }, formData);
        this.patientService.update(this.patientId, updateData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(["/patients"]);
          },
          error: (error) => {
            this.error = "Failed to update patient. Please try again.";
            this.loading = false;
            console.error("Error updating patient:", error);
          }
        });
      } else {
        const createData = formData;
        this.patientService.create(createData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(["/patients"]);
          },
          error: (error) => {
            this.error = "Failed to create patient. Please try again.";
            this.loading = false;
            console.error("Error creating patient:", error);
          }
        });
      }
    } else {
      Object.keys(this.patientForm.controls).forEach((key) => {
        this.patientForm.get(key)?.markAsTouched();
      });
    }
  }
  static \u0275fac = function PatientFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PatientFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(PatientService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PatientFormComponent, selectors: [["app-patient-form"]], decls: 17, vars: 7, consts: [[1, "min-h-screen", "bg-gray-50"], [1, "bg-white", "shadow-sm", "border-b", "border-gray-200"], [1, "max-w-4xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8"], [1, "flex", "justify-between", "items-center", "py-6"], [1, "text-3xl", "font-bold", "text-gray-900"], [1, "mt-1", "text-sm", "text-gray-500"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-gray-300", "text-sm", "font-medium", "rounded-md", "text-gray-700", "bg-white", "hover:bg-gray-50", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "transition-colors", "duration-200", 3, "routerLink"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "-ml-1", "mr-2", "h-4", "w-4"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M10 19l-7-7m0 0l7-7m-7 7h18"], [1, "max-w-4xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8", "py-8"], ["class", "bg-white rounded-lg shadow-sm border border-gray-200", 4, "ngIf"], ["class", "bg-red-50 border border-red-200 rounded-lg p-4 mb-6", 4, "ngIf"], [1, "bg-white", "rounded-lg", "shadow-sm", "border", "border-gray-200"], [1, "flex", "justify-center", "items-center", "py-12"], [1, "flex", "flex-col", "items-center"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-indigo-600"], [1, "mt-4", "text-sm", "text-gray-500"], [1, "bg-red-50", "border", "border-red-200", "rounded-lg", "p-4", "mb-6"], [1, "flex"], [1, "flex-shrink-0"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-5", "w-5", "text-red-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "ml-3"], [1, "text-sm", "text-red-800"], [1, "space-y-6", "p-6", 3, "ngSubmit", "formGroup"], [1, "text-lg", "font-medium", "text-gray-900", "mb-4"], [1, "grid", "grid-cols-1", "gap-6", "sm:grid-cols-2", "lg:grid-cols-3"], ["for", "firstName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], [1, "text-red-500"], ["id", "firstName", "type", "text", "formControlName", "firstName", "placeholder", "Enter first name", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["class", "mt-1 text-sm text-red-600", 4, "ngIf"], ["for", "lastName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "lastName", "type", "text", "formControlName", "lastName", "placeholder", "Enter last name", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "middleName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "middleName", "type", "text", "formControlName", "middleName", "placeholder", "Enter middle name (optional)", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "dateOfBirth", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "dateOfBirth", "type", "date", "formControlName", "dateOfBirth", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "gender", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "gender", "formControlName", "gender", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["value", ""], [3, "value"], [1, "border-t", "border-gray-200", "pt-6"], [1, "grid", "grid-cols-1", "gap-6", "sm:grid-cols-2"], ["for", "email", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "email", "type", "email", "formControlName", "email", "placeholder", "Enter email address", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "phoneNumber", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "phoneNumber", "type", "tel", "formControlName", "phoneNumber", "placeholder", "Enter phone number", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], [1, "grid", "grid-cols-1", "gap-6", "sm:grid-cols-2", "lg:grid-cols-4"], [1, "sm:col-span-2"], ["for", "street", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "street", "type", "text", "formControlName", "street", "placeholder", "Enter street address", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "city", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "city", "type", "text", "formControlName", "city", "placeholder", "Enter city", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "state", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "state", "type", "text", "formControlName", "state", "placeholder", "Enter state", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "postalCode", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "postalCode", "type", "text", "formControlName", "postalCode", "placeholder", "Enter postal code", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "country", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "country", "type", "text", "formControlName", "country", "placeholder", "Enter country", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], [1, "grid", "grid-cols-1", "gap-6"], ["for", "medicalHistory", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "medicalHistory", "formControlName", "medicalHistory", "rows", "3", "placeholder", "Enter medical history (optional)", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "allergies", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "allergies", "formControlName", "allergies", "rows", "2", "placeholder", "Enter known allergies (optional)", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "emergencyContactName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "emergencyContactName", "type", "text", "formControlName", "emergencyContactName", "placeholder", "Enter emergency contact name", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "emergencyContactPhone", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "emergencyContactPhone", "type", "tel", "formControlName", "emergencyContactPhone", "placeholder", "Enter emergency contact phone", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "insuranceProvider", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "insuranceProvider", "type", "text", "formControlName", "insuranceProvider", "placeholder", "Enter insurance provider", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], ["for", "insurancePolicyNumber", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-2"], ["id", "insurancePolicyNumber", "type", "text", "formControlName", "insurancePolicyNumber", "placeholder", "Enter policy number", 1, "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "placeholder-gray-400", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500"], [1, "flex", "justify-end", "space-x-3"], ["type", "button", 1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-gray-300", "text-sm", "font-medium", "rounded-md", "text-gray-700", "bg-white", "hover:bg-gray-50", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "transition-colors", "duration-200", 3, "routerLink"], ["type", "submit", 1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "text-sm", "font-medium", "rounded-md", "shadow-sm", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "disabled:opacity-50", "disabled:cursor-not-allowed", "transition-colors", "duration-200", 3, "disabled"], ["class", "animate-spin -ml-1 mr-2 h-4 w-4 text-white", "fill", "none", "viewBox", "0 0 24 24", 4, "ngIf"], [1, "mt-1", "text-sm", "text-red-600"], [4, "ngIf"], ["fill", "none", "viewBox", "0 0 24 24", 1, "animate-spin", "-ml-1", "mr-2", "h-4", "w-4", "text-white"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"]], template: function PatientFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div")(5, "h1", 4);
      \u0275\u0275text(6);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "p", 5);
      \u0275\u0275text(8);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "button", 6);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(10, "svg", 7);
      \u0275\u0275element(11, "path", 8);
      \u0275\u0275elementEnd();
      \u0275\u0275text(12, " Back to Patients ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(13, "div", 9);
      \u0275\u0275template(14, PatientFormComponent_div_14_Template, 6, 1, "div", 10)(15, PatientFormComponent_div_15_Template, 8, 1, "div", 11)(16, PatientFormComponent_div_16_Template, 145, 40, "div", 10);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Edit Patient" : "Add New Patient", " ");
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Update patient information" : "Enter patient details to create a new record", " ");
      \u0275\u0275advance();
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(6, _c0));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error && !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule, RouterLink], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PatientFormComponent, [{
    type: Component,
    args: [{
      selector: "app-patient-form",
      standalone: true,
      imports: [CommonModule, ReactiveFormsModule, RouterModule],
      template: `
    <div class="min-h-screen bg-gray-50">
      <!-- Header Section -->
      <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center py-6">
            <div>
              <h1 class="text-3xl font-bold text-gray-900">
                {{ isEditMode ? 'Edit Patient' : 'Add New Patient' }}
              </h1>
              <p class="mt-1 text-sm text-gray-500">
                {{ isEditMode ? 'Update patient information' : 'Enter patient details to create a new record' }}
              </p>
            </div>
            <button
              [routerLink]="['/patients']"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
              </svg>
              Back to Patients
            </button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Loading State -->
        <div *ngIf="loading" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="flex justify-center items-center py-12">
            <div class="flex flex-col items-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
              <p class="mt-4 text-sm text-gray-500">{{ isEditMode ? 'Loading patient data...' : 'Saving patient...' }}</p>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Form -->
        <div *ngIf="!loading" class="bg-white rounded-lg shadow-sm border border-gray-200">
          <form [formGroup]="patientForm" (ngSubmit)="onSubmit()" class="space-y-6 p-6">
            <!-- Personal Information Section -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <!-- First Name -->
                <div>
                  <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                    First Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    formControlName="firstName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched"
                    placeholder="Enter first name"
                  />
                  <div *ngIf="patientForm.get('firstName')?.invalid && patientForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
                    First name is required
                  </div>
                </div>

                <!-- Last Name -->
                <div>
                  <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                    Last Name <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    formControlName="lastName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched"
                    placeholder="Enter last name"
                  />
                  <div *ngIf="patientForm.get('lastName')?.invalid && patientForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
                    Last name is required
                  </div>
                </div>

                <!-- Middle Name -->
                <div>
                  <label for="middleName" class="block text-sm font-medium text-gray-700 mb-2">
                    Middle Name
                  </label>
                  <input
                    id="middleName"
                    type="text"
                    formControlName="middleName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter middle name (optional)"
                  />
                </div>

                <!-- Date of Birth -->
                <div>
                  <label for="dateOfBirth" class="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="dateOfBirth"
                    type="date"
                    formControlName="dateOfBirth"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched"
                  />
                  <div *ngIf="patientForm.get('dateOfBirth')?.invalid && patientForm.get('dateOfBirth')?.touched" class="mt-1 text-sm text-red-600">
                    Date of birth is required
                  </div>
                </div>

                <!-- Gender -->
                <div>
                  <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                    Gender <span class="text-red-500">*</span>
                  </label>
                  <select
                    id="gender"
                    formControlName="gender"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched"
                  >
                    <option value="">Select gender</option>
                    <option [value]="Gender.Male">Male</option>
                    <option [value]="Gender.Female">Female</option>
                    <option [value]="Gender.Other">Other</option>
                    <option [value]="Gender.PreferNotToSay">Prefer not to say</option>
                  </select>
                  <div *ngIf="patientForm.get('gender')?.invalid && patientForm.get('gender')?.touched" class="mt-1 text-sm text-red-600">
                    Gender is required
                  </div>
                </div>
              </div>
            </div>

            <!-- Contact Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Email -->
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    type="email"
                    formControlName="email"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('email')?.invalid && patientForm.get('email')?.touched"
                    placeholder="Enter email address"
                  />
                  <div *ngIf="patientForm.get('email')?.invalid && patientForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
                    <span *ngIf="patientForm.get('email')?.errors?.['required']">Email is required</span>
                    <span *ngIf="patientForm.get('email')?.errors?.['email']">Please enter a valid email address</span>
                  </div>
                </div>

                <!-- Phone Number -->
                <div>
                  <label for="phoneNumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="phoneNumber"
                    type="tel"
                    formControlName="phoneNumber"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched"
                    placeholder="Enter phone number"
                  />
                  <div *ngIf="patientForm.get('phoneNumber')?.invalid && patientForm.get('phoneNumber')?.touched" class="mt-1 text-sm text-red-600">
                    Phone number is required
                  </div>
                </div>
              </div>
            </div>

            <!-- Address Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <!-- Street -->
                <div class="sm:col-span-2">
                  <label for="street" class="block text-sm font-medium text-gray-700 mb-2">
                    Street Address <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="street"
                    type="text"
                    formControlName="street"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('street')?.invalid && patientForm.get('street')?.touched"
                    placeholder="Enter street address"
                  />
                  <div *ngIf="patientForm.get('street')?.invalid && patientForm.get('street')?.touched" class="mt-1 text-sm text-red-600">
                    Street address is required
                  </div>
                </div>

                <!-- City -->
                <div>
                  <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                    City <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="city"
                    type="text"
                    formControlName="city"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('city')?.invalid && patientForm.get('city')?.touched"
                    placeholder="Enter city"
                  />
                  <div *ngIf="patientForm.get('city')?.invalid && patientForm.get('city')?.touched" class="mt-1 text-sm text-red-600">
                    City is required
                  </div>
                </div>

                <!-- State -->
                <div>
                  <label for="state" class="block text-sm font-medium text-gray-700 mb-2">
                    State <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="state"
                    type="text"
                    formControlName="state"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('state')?.invalid && patientForm.get('state')?.touched"
                    placeholder="Enter state"
                  />
                  <div *ngIf="patientForm.get('state')?.invalid && patientForm.get('state')?.touched" class="mt-1 text-sm text-red-600">
                    State is required
                  </div>
                </div>

                <!-- Postal Code -->
                <div>
                  <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-2">
                    Postal Code <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="postalCode"
                    type="text"
                    formControlName="postalCode"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    [class.border-red-300]="patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched"
                    placeholder="Enter postal code"
                  />
                  <div *ngIf="patientForm.get('postalCode')?.invalid && patientForm.get('postalCode')?.touched" class="mt-1 text-sm text-red-600">
                    Postal code is required
                  </div>
                </div>

                <!-- Country -->
                <div>
                  <label for="country" class="block text-sm font-medium text-gray-700 mb-2">
                    Country
                  </label>
                  <input
                    id="country"
                    type="text"
                    formControlName="country"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>

            <!-- Medical Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
              <div class="grid grid-cols-1 gap-6">
                <!-- Medical History -->
                <div>
                  <label for="medicalHistory" class="block text-sm font-medium text-gray-700 mb-2">
                    Medical History
                  </label>
                  <textarea
                    id="medicalHistory"
                    formControlName="medicalHistory"
                    rows="3"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter medical history (optional)"
                  ></textarea>
                </div>

                <!-- Allergies -->
                <div>
                  <label for="allergies" class="block text-sm font-medium text-gray-700 mb-2">
                    Allergies
                  </label>
                  <textarea
                    id="allergies"
                    formControlName="allergies"
                    rows="2"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter known allergies (optional)"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Emergency Contact Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Emergency Contact Name -->
                <div>
                  <label for="emergencyContactName" class="block text-sm font-medium text-gray-700 mb-2">
                    Emergency Contact Name
                  </label>
                  <input
                    id="emergencyContactName"
                    type="text"
                    formControlName="emergencyContactName"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter emergency contact name"
                  />
                </div>

                <!-- Emergency Contact Phone -->
                <div>
                  <label for="emergencyContactPhone" class="block text-sm font-medium text-gray-700 mb-2">
                    Emergency Contact Phone
                  </label>
                  <input
                    id="emergencyContactPhone"
                    type="tel"
                    formControlName="emergencyContactPhone"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter emergency contact phone"
                  />
                </div>
              </div>
            </div>

            <!-- Insurance Information Section -->
            <div class="border-t border-gray-200 pt-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Insurance Information</h3>
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Insurance Provider -->
                <div>
                  <label for="insuranceProvider" class="block text-sm font-medium text-gray-700 mb-2">
                    Insurance Provider
                  </label>
                  <input
                    id="insuranceProvider"
                    type="text"
                    formControlName="insuranceProvider"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter insurance provider"
                  />
                </div>

                <!-- Insurance Policy Number -->
                <div>
                  <label for="insurancePolicyNumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Policy Number
                  </label>
                  <input
                    id="insurancePolicyNumber"
                    type="text"
                    formControlName="insurancePolicyNumber"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter policy number"
                  />
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="border-t border-gray-200 pt-6">
              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  [routerLink]="['/patients']"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                  Cancel
                </button>
                <button
                  type="submit"
                  [disabled]="patientForm.invalid || loading"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                  <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isEditMode ? 'Update Patient' : 'Create Patient' }}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  `
    }]
  }], () => [{ type: FormBuilder }, { type: PatientService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PatientFormComponent, { className: "PatientFormComponent", filePath: "src/app/features/patients/patient-form/patient-form.component.ts", lineNumber: 426 });
})();
export {
  PatientFormComponent
};
//# sourceMappingURL=chunk-S3TWLZAR.mjs.map
