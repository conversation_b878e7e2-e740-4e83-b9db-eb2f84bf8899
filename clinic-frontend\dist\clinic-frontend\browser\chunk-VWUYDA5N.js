import {
  AuthService
} from "./chunk-AUAHXWWU.js";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-T7IIKLN2.js";
import {
  ActivatedRoute,
  Router,
  RouterModule
} from "./chunk-2NNV54NL.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  NgIf,
  finalize,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-7FZJUQ36.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/auth/login/login.component.ts
function LoginComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22)(1, "div", 23)(2, "div", 24);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 25);
    \u0275\u0275element(4, "path", 26);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(5, "div", 27)(6, "p", 28);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function LoginComponent_div_17_span_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_div_17_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Please enter a valid email address");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29);
    \u0275\u0275template(1, LoginComponent_div_17_span_1_Template, 2, 0, "span", 30)(2, LoginComponent_div_17_span_2_Template, 2, 0, "span", 30);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["email"].errors == null ? null : ctx_r0.f["email"].errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["email"].errors == null ? null : ctx_r0.f["email"].errors["email"]);
  }
}
function LoginComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29);
    \u0275\u0275text(1, " Password is required ");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent__svg_svg_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 31);
    \u0275\u0275element(1, "path", 32);
    \u0275\u0275elementEnd();
  }
}
function LoginComponent__svg_svg_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 33);
    \u0275\u0275element(1, "circle", 34)(2, "path", 35);
    \u0275\u0275elementEnd();
  }
}
var LoginComponent = class _LoginComponent {
  formBuilder;
  route;
  router;
  authService;
  loginForm;
  loading = false;
  submitted = false;
  error = "";
  returnUrl = "/dashboard";
  constructor(formBuilder, route, router, authService) {
    this.formBuilder = formBuilder;
    this.route = route;
    this.router = router;
    this.authService = authService;
    if (this.authService.isAuthenticated()) {
      this.router.navigate(["/dashboard"]);
    }
    this.loginForm = this.formBuilder.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", Validators.required]
    });
  }
  ngOnInit() {
    this.returnUrl = this.route.snapshot.queryParams["returnUrl"] || "/dashboard";
  }
  // Convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }
  onSubmit() {
    this.submitted = true;
    if (this.loginForm.invalid) {
      return;
    }
    this.loading = true;
    this.error = "";
    this.authService.login({
      email: this.f["email"].value,
      password: this.f["password"].value
    }).pipe(finalize(() => {
      this.loading = false;
    })).subscribe({
      next: () => {
        this.router.navigate([this.returnUrl]);
      },
      error: (error) => {
        this.error = error.message || "Invalid credentials";
      }
    });
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 45, vars: 12, consts: [[1, "min-h-screen", "flex", "items-center", "justify-center", "bg-gray-50", "py-12", "px-4", "sm:px-6", "lg:px-8"], [1, "max-w-md", "w-full", "space-y-8"], [1, "mx-auto", "h-12", "w-12", "flex", "items-center", "justify-center", "rounded-full", "bg-indigo-100"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-8", "w-8", "text-indigo-600"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "mt-6", "text-center", "text-3xl", "font-extrabold", "text-gray-900"], [1, "mt-2", "text-center", "text-sm", "text-gray-600"], ["class", "bg-red-50 border border-red-200 rounded-md p-4", 4, "ngIf"], [1, "mt-8", "space-y-6", 3, "ngSubmit", "formGroup"], [1, "space-y-4"], ["for", "email", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["id", "email", "name", "email", "type", "email", "autocomplete", "email", "formControlName", "email", "placeholder", "Enter your email address", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500", "focus:z-10", "sm:text-sm"], ["class", "mt-1 text-sm text-red-600", 4, "ngIf"], ["for", "password", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["id", "password", "name", "password", "type", "password", "autocomplete", "current-password", "formControlName", "password", "placeholder", "Enter your password", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500", "focus:z-10", "sm:text-sm"], ["type", "submit", 1, "group", "relative", "w-full", "flex", "justify-center", "py-2", "px-4", "border", "border-transparent", "text-sm", "font-medium", "rounded-md", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", "disabled:opacity-50", "disabled:cursor-not-allowed", "transition-colors", "duration-200", 3, "disabled"], [1, "absolute", "left-0", "inset-y-0", "flex", "items-center", "pl-3"], ["class", "h-5 w-5 text-indigo-500 group-hover:text-indigo-400", "fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 4, "ngIf"], ["class", "animate-spin h-5 w-5 text-indigo-500", "fill", "none", "viewBox", "0 0 24 24", 4, "ngIf"], [1, "mt-6", "p-4", "bg-blue-50", "border", "border-blue-200", "rounded-md"], [1, "text-sm", "font-medium", "text-blue-800", "mb-2"], [1, "text-xs", "text-blue-700", "space-y-1"], [1, "bg-red-50", "border", "border-red-200", "rounded-md", "p-4"], [1, "flex"], [1, "flex-shrink-0"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-5", "w-5", "text-red-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "ml-3"], [1, "text-sm", "text-red-800"], [1, "mt-1", "text-sm", "text-red-600"], [4, "ngIf"], ["fill", "none", "stroke", "currentColor", "viewBox", "0 0 24 24", 1, "h-5", "w-5", "text-indigo-500", "group-hover:text-indigo-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"], ["fill", "none", "viewBox", "0 0 24 24", 1, "animate-spin", "h-5", "w-5", "text-indigo-500"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div")(3, "div", 2);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 3);
      \u0275\u0275element(5, "path", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "h2", 5);
      \u0275\u0275text(7, " Sign in to your account ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "p", 6);
      \u0275\u0275text(9, " Welcome to Clinic Management System ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(10, LoginComponent_div_10_Template, 8, 1, "div", 7);
      \u0275\u0275elementStart(11, "form", 8);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_11_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(12, "div", 9)(13, "div")(14, "label", 10);
      \u0275\u0275text(15, " Email address ");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 11);
      \u0275\u0275template(17, LoginComponent_div_17_Template, 3, 2, "div", 12);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "div")(19, "label", 13);
      \u0275\u0275text(20, " Password ");
      \u0275\u0275elementEnd();
      \u0275\u0275element(21, "input", 14);
      \u0275\u0275template(22, LoginComponent_div_22_Template, 2, 0, "div", 12);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(23, "div")(24, "button", 15)(25, "span", 16);
      \u0275\u0275template(26, LoginComponent__svg_svg_26_Template, 2, 0, "svg", 17)(27, LoginComponent__svg_svg_27_Template, 3, 0, "svg", 18);
      \u0275\u0275elementEnd();
      \u0275\u0275text(28);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(29, "div", 19)(30, "h3", 20);
      \u0275\u0275text(31, "Demo Credentials");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "div", 21)(33, "p")(34, "strong");
      \u0275\u0275text(35, "Admin:");
      \u0275\u0275elementEnd();
      \u0275\u0275text(36, " <EMAIL> / Admin123!");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "p")(38, "strong");
      \u0275\u0275text(39, "Doctor:");
      \u0275\u0275elementEnd();
      \u0275\u0275text(40, " <EMAIL> / Doctor123!");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(41, "p")(42, "strong");
      \u0275\u0275text(43, "Receptionist:");
      \u0275\u0275elementEnd();
      \u0275\u0275text(44, " <EMAIL> / Receptionist123!");
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance(5);
      \u0275\u0275classProp("border-red-300", ctx.f["email"].invalid && (ctx.f["email"].dirty || ctx.f["email"].touched || ctx.submitted));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.f["email"].invalid && (ctx.f["email"].dirty || ctx.f["email"].touched || ctx.submitted));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("border-red-300", ctx.f["password"].invalid && (ctx.f["password"].dirty || ctx.f["password"].touched || ctx.submitted));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.f["password"].invalid && (ctx.f["password"].dirty || ctx.f["password"].touched || ctx.submitted));
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.loading);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.loading ? "Signing in..." : "Sign in", " ");
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{
      selector: "app-login",
      standalone: true,
      imports: [CommonModule, ReactiveFormsModule, RouterModule],
      template: `
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div>
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100">
            <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            Welcome to Clinic Management System
          </p>
        </div>

        <!-- Error Alert -->
        <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Login Form -->
        <form class="mt-8 space-y-6" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="space-y-4">
            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                formControlName="email"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                [class.border-red-300]="f['email'].invalid && (f['email'].dirty || f['email'].touched || submitted)"
                placeholder="Enter your email address"
              />
              <div *ngIf="f['email'].invalid && (f['email'].dirty || f['email'].touched || submitted)" class="mt-1 text-sm text-red-600">
                <span *ngIf="f['email'].errors?.['required']">Email is required</span>
                <span *ngIf="f['email'].errors?.['email']">Please enter a valid email address</span>
              </div>
            </div>

            <!-- Password Field -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="current-password"
                formControlName="password"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                [class.border-red-300]="f['password'].invalid && (f['password'].dirty || f['password'].touched || submitted)"
                placeholder="Enter your password"
              />
              <div *ngIf="f['password'].invalid && (f['password'].dirty || f['password'].touched || submitted)" class="mt-1 text-sm text-red-600">
                Password is required
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div>
            <button
              type="submit"
              [disabled]="loading"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg *ngIf="!loading" class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                </svg>
                <svg *ngIf="loading" class="animate-spin h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ loading ? 'Signing in...' : 'Sign in' }}
            </button>
          </div>

          <!-- Demo Credentials -->
          <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Demo Credentials</h3>
            <div class="text-xs text-blue-700 space-y-1">
              <p><strong>Admin:</strong> admin&#64;clinic.com / Admin123!</p>
              <p><strong>Doctor:</strong> doctor&#64;clinic.com / Doctor123!</p>
              <p><strong>Receptionist:</strong> receptionist&#64;clinic.com / Receptionist123!</p>
            </div>
          </div>
        </form>
      </div>
    </div>
  `
    }]
  }], () => [{ type: FormBuilder }, { type: ActivatedRoute }, { type: Router }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/features/auth/login/login.component.ts", lineNumber: 122 });
})();
export {
  LoginComponent
};
//# sourceMappingURL=chunk-VWUYDA5N.js.map
