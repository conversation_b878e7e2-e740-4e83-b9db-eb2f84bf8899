import {
  UserR<PERSON>
} from "./chunk-K2W23EYU.js";
import {
  AuthService
} from "./chunk-AUAHXWWU.js";
import {
  Default<PERSON><PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  RequiredValidator,
  Validators,
  ɵNgNoValidate
} from "./chunk-T7IIKLN2.js";
import {
  Router
} from "./chunk-2NNV54NL.js";
import "./chunk-7NNESOLN.js";
import {
  CommonModule,
  Component,
  NgClass,
  NgIf,
  finalize,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-7FZJUQ36.js";
import "./chunk-Y5RQAIA6.js";

// src/app/features/auth/initialize-admin/initialize-admin.component.ts
var _c0 = (a0) => ({ "border-red-500": a0 });
function InitializeAdminComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24)(1, "span", 25);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function InitializeAdminComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26)(1, "span", 25);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.success);
  }
}
function InitializeAdminComponent_div_17_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "First name is required");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275template(1, InitializeAdminComponent_div_17_div_1_Template, 2, 0, "div", 28);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["firstName"].errors["required"]);
  }
}
function InitializeAdminComponent_div_22_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Last name is required");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275template(1, InitializeAdminComponent_div_22_div_1_Template, 2, 0, "div", 28);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["lastName"].errors["required"]);
  }
}
function InitializeAdminComponent_div_27_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_27_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Email must be a valid email address");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275template(1, InitializeAdminComponent_div_27_div_1_Template, 2, 0, "div", 28)(2, InitializeAdminComponent_div_27_div_2_Template, 2, 0, "div", 28);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["email"].errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["email"].errors["email"]);
  }
}
function InitializeAdminComponent_div_32_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Password is required");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_32_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Password must be at least 6 characters");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275template(1, InitializeAdminComponent_div_32_div_1_Template, 2, 0, "div", 28)(2, InitializeAdminComponent_div_32_div_2_Template, 2, 0, "div", 28);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["password"].errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["password"].errors["minlength"]);
  }
}
function InitializeAdminComponent_div_37_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Confirm password is required");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_37_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Passwords do not match");
    \u0275\u0275elementEnd();
  }
}
function InitializeAdminComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275template(1, InitializeAdminComponent_div_37_div_1_Template, 2, 0, "div", 28)(2, InitializeAdminComponent_div_37_div_2_Template, 2, 0, "div", 28);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["confirmPassword"].errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.f["confirmPassword"].errors["passwordMismatch"]);
  }
}
function InitializeAdminComponent_span_40_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 29);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 30);
    \u0275\u0275element(2, "circle", 31)(3, "path", 32);
    \u0275\u0275elementEnd()();
  }
}
function InitializeAdminComponent_span_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 29);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 33);
    \u0275\u0275element(2, "path", 34);
    \u0275\u0275elementEnd()();
  }
}
var InitializeAdminComponent = class _InitializeAdminComponent {
  formBuilder;
  router;
  authService;
  adminForm;
  loading = false;
  submitted = false;
  error = "";
  success = "";
  constructor(formBuilder, router, authService) {
    this.formBuilder = formBuilder;
    this.router = router;
    this.authService = authService;
    if (this.authService.isAuthenticated()) {
      this.router.navigate(["/dashboard"]);
    }
    this.adminForm = this.formBuilder.group({
      firstName: ["", [Validators.required]],
      lastName: ["", [Validators.required]],
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(6)]],
      confirmPassword: ["", [Validators.required]]
    }, {
      validator: this.passwordMatchValidator
    });
  }
  ngOnInit() {
  }
  // Custom validator to check if passwords match
  passwordMatchValidator(formGroup) {
    const password = formGroup.get("password")?.value;
    const confirmPassword = formGroup.get("confirmPassword")?.value;
    if (password !== confirmPassword) {
      formGroup.get("confirmPassword")?.setErrors({ passwordMismatch: true });
    } else {
      formGroup.get("confirmPassword")?.setErrors(null);
    }
  }
  // Convenience getter for easy access to form fields
  get f() {
    return this.adminForm.controls;
  }
  onSubmit() {
    this.submitted = true;
    this.success = "";
    this.error = "";
    if (this.adminForm.invalid) {
      return;
    }
    this.loading = true;
    this.authService.initializeAdmin({
      firstName: this.f["firstName"].value,
      lastName: this.f["lastName"].value,
      email: this.f["email"].value,
      password: this.f["password"].value,
      role: UserRole.Admin
    }).pipe(finalize(() => {
      this.loading = false;
    })).subscribe({
      next: (response) => {
        this.success = response.message || "Admin user created successfully";
        setTimeout(() => {
          this.router.navigate(["/login"]);
        }, 2e3);
      },
      error: (error) => {
        this.error = error.error?.message || "Initialization failed";
      }
    });
  }
  static \u0275fac = function InitializeAdminComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _InitializeAdminComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _InitializeAdminComponent, selectors: [["app-initialize-admin"]], decls: 43, vars: 26, consts: [[1, "min-h-screen", "flex", "items-center", "justify-center", "bg-gray-50", "py-12", "px-4", "sm:px-6", "lg:px-8"], [1, "max-w-md", "w-full", "space-y-8", "bg-white", "p-8", "rounded-lg", "shadow-md"], [1, "text-center"], ["src", "assets/images/logo.png", "alt", "Clinic Logo", 1, "mx-auto", "h-16", "w-auto"], [1, "mt-6", "text-center", "text-3xl", "font-extrabold", "text-gray-900"], [1, "mt-2", "text-center", "text-sm", "text-gray-600"], ["class", "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative", "role", "alert", 4, "ngIf"], ["class", "bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative", "role", "alert", 4, "ngIf"], [1, "mt-8", "space-y-6", 3, "ngSubmit", "formGroup"], [1, "rounded-md", "shadow-sm", "space-y-4"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-4"], ["for", "first-name", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["id", "first-name", "name", "firstName", "type", "text", "formControlName", "firstName", "required", "", "placeholder", "First Name", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", "focus:z-10", "sm:text-sm", 3, "ngClass"], ["class", "text-red-500 text-xs mt-1", 4, "ngIf"], ["for", "last-name", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["id", "last-name", "name", "lastName", "type", "text", "formControlName", "lastName", "required", "", "placeholder", "Last Name", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", "focus:z-10", "sm:text-sm", 3, "ngClass"], ["for", "email-address", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["id", "email-address", "name", "email", "type", "email", "formControlName", "email", "autocomplete", "email", "required", "", "placeholder", "Email address", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", "focus:z-10", "sm:text-sm", 3, "ngClass"], ["for", "password", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["id", "password", "name", "password", "type", "password", "formControlName", "password", "required", "", "placeholder", "Password", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", "focus:z-10", "sm:text-sm", 3, "ngClass"], ["for", "confirm-password", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["id", "confirm-password", "name", "confirmPassword", "type", "password", "formControlName", "confirmPassword", "required", "", "placeholder", "Confirm Password", 1, "appearance-none", "relative", "block", "w-full", "px-3", "py-2", "border", "border-gray-300", "placeholder-gray-500", "text-gray-900", "rounded-md", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", "focus:z-10", "sm:text-sm", 3, "ngClass"], ["type", "submit", 1, "group", "relative", "w-full", "flex", "justify-center", "py-2", "px-4", "border", "border-transparent", "text-sm", "font-medium", "rounded-md", "text-white", "bg-primary-600", "hover:bg-primary-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-primary-500", "disabled:opacity-50", 3, "disabled"], ["class", "absolute left-0 inset-y-0 flex items-center pl-3", 4, "ngIf"], ["role", "alert", 1, "bg-red-50", "border", "border-red-200", "text-red-700", "px-4", "py-3", "rounded", "relative"], [1, "block", "sm:inline"], ["role", "alert", 1, "bg-green-50", "border", "border-green-200", "text-green-700", "px-4", "py-3", "rounded", "relative"], [1, "text-red-500", "text-xs", "mt-1"], [4, "ngIf"], [1, "absolute", "left-0", "inset-y-0", "flex", "items-center", "pl-3"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", 1, "animate-spin", "h-5", "w-5", "text-white"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", "aria-hidden", "true", 1, "h-5", "w-5", "text-primary-500", "group-hover:text-primary-400"], ["fill-rule", "evenodd", "d", "M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", "clip-rule", "evenodd"]], template: function InitializeAdminComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2);
      \u0275\u0275element(3, "img", 3);
      \u0275\u0275elementStart(4, "h2", 4);
      \u0275\u0275text(5, "Initialize Admin Account");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 5);
      \u0275\u0275text(7, " Create the first admin user for the clinic management system ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(8, InitializeAdminComponent_div_8_Template, 3, 1, "div", 6)(9, InitializeAdminComponent_div_9_Template, 3, 1, "div", 7);
      \u0275\u0275elementStart(10, "form", 8);
      \u0275\u0275listener("ngSubmit", function InitializeAdminComponent_Template_form_ngSubmit_10_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(11, "div", 9)(12, "div", 10)(13, "div")(14, "label", 11);
      \u0275\u0275text(15, "First Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 12);
      \u0275\u0275template(17, InitializeAdminComponent_div_17_Template, 2, 1, "div", 13);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "div")(19, "label", 14);
      \u0275\u0275text(20, "Last Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(21, "input", 15);
      \u0275\u0275template(22, InitializeAdminComponent_div_22_Template, 2, 1, "div", 13);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(23, "div")(24, "label", 16);
      \u0275\u0275text(25, "Email address");
      \u0275\u0275elementEnd();
      \u0275\u0275element(26, "input", 17);
      \u0275\u0275template(27, InitializeAdminComponent_div_27_Template, 3, 2, "div", 13);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div")(29, "label", 18);
      \u0275\u0275text(30, "Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(31, "input", 19);
      \u0275\u0275template(32, InitializeAdminComponent_div_32_Template, 3, 2, "div", 13);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "div")(34, "label", 20);
      \u0275\u0275text(35, "Confirm Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(36, "input", 21);
      \u0275\u0275template(37, InitializeAdminComponent_div_37_Template, 3, 2, "div", 13);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(38, "div")(39, "button", 22);
      \u0275\u0275template(40, InitializeAdminComponent_span_40_Template, 4, 0, "span", 23)(41, InitializeAdminComponent_span_41_Template, 3, 0, "span", 23);
      \u0275\u0275text(42, " Create Admin Account ");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.success);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.adminForm);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(16, _c0, ctx.submitted && ctx.f["firstName"].errors));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.submitted && ctx.f["firstName"].errors);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(18, _c0, ctx.submitted && ctx.f["lastName"].errors));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.submitted && ctx.f["lastName"].errors);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(20, _c0, ctx.submitted && ctx.f["email"].errors));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.submitted && ctx.f["email"].errors);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(22, _c0, ctx.submitted && ctx.f["password"].errors));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.submitted && ctx.f["password"].errors);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(24, _c0, ctx.submitted && ctx.f["confirmPassword"].errors));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.submitted && ctx.f["confirmPassword"].errors);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgClass, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(InitializeAdminComponent, [{
    type: Component,
    args: [{ selector: "app-initialize-admin", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">\r
  <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">\r
    <div class="text-center">\r
      <img class="mx-auto h-16 w-auto" src="assets/images/logo.png" alt="Clinic Logo">\r
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Initialize Admin Account</h2>\r
      <p class="mt-2 text-center text-sm text-gray-600">\r
        Create the first admin user for the clinic management system\r
      </p>\r
    </div>\r
    \r
    <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">\r
      <span class="block sm:inline">{{ error }}</span>\r
    </div>\r
\r
    <div *ngIf="success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">\r
      <span class="block sm:inline">{{ success }}</span>\r
    </div>\r
    \r
    <form class="mt-8 space-y-6" [formGroup]="adminForm" (ngSubmit)="onSubmit()">\r
      <div class="rounded-md shadow-sm space-y-4">\r
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">\r
          <div>\r
            <label for="first-name" class="block text-sm font-medium text-gray-700">First Name</label>\r
            <input \r
              id="first-name" \r
              name="firstName" \r
              type="text" \r
              formControlName="firstName" \r
              required \r
              class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" \r
              placeholder="First Name"\r
              [ngClass]="{ 'border-red-500': submitted && f['firstName'].errors }"\r
            >\r
            <div *ngIf="submitted && f['firstName'].errors" class="text-red-500 text-xs mt-1">\r
              <div *ngIf="f['firstName'].errors['required']">First name is required</div>\r
            </div>\r
          </div>\r
          \r
          <div>\r
            <label for="last-name" class="block text-sm font-medium text-gray-700">Last Name</label>\r
            <input \r
              id="last-name" \r
              name="lastName" \r
              type="text" \r
              formControlName="lastName" \r
              required \r
              class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" \r
              placeholder="Last Name"\r
              [ngClass]="{ 'border-red-500': submitted && f['lastName'].errors }"\r
            >\r
            <div *ngIf="submitted && f['lastName'].errors" class="text-red-500 text-xs mt-1">\r
              <div *ngIf="f['lastName'].errors['required']">Last name is required</div>\r
            </div>\r
          </div>\r
        </div>\r
        \r
        <div>\r
          <label for="email-address" class="block text-sm font-medium text-gray-700">Email address</label>\r
          <input \r
            id="email-address" \r
            name="email" \r
            type="email" \r
            formControlName="email" \r
            autocomplete="email" \r
            required \r
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" \r
            placeholder="Email address"\r
            [ngClass]="{ 'border-red-500': submitted && f['email'].errors }"\r
          >\r
          <div *ngIf="submitted && f['email'].errors" class="text-red-500 text-xs mt-1">\r
            <div *ngIf="f['email'].errors['required']">Email is required</div>\r
            <div *ngIf="f['email'].errors['email']">Email must be a valid email address</div>\r
          </div>\r
        </div>\r
        \r
        <div>\r
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>\r
          <input \r
            id="password" \r
            name="password" \r
            type="password" \r
            formControlName="password" \r
            required \r
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" \r
            placeholder="Password"\r
            [ngClass]="{ 'border-red-500': submitted && f['password'].errors }"\r
          >\r
          <div *ngIf="submitted && f['password'].errors" class="text-red-500 text-xs mt-1">\r
            <div *ngIf="f['password'].errors['required']">Password is required</div>\r
            <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>\r
          </div>\r
        </div>\r
        \r
        <div>\r
          <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm Password</label>\r
          <input \r
            id="confirm-password" \r
            name="confirmPassword" \r
            type="password" \r
            formControlName="confirmPassword" \r
            required \r
            class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" \r
            placeholder="Confirm Password"\r
            [ngClass]="{ 'border-red-500': submitted && f['confirmPassword'].errors }"\r
          >\r
          <div *ngIf="submitted && f['confirmPassword'].errors" class="text-red-500 text-xs mt-1">\r
            <div *ngIf="f['confirmPassword'].errors['required']">Confirm password is required</div>\r
            <div *ngIf="f['confirmPassword'].errors['passwordMismatch']">Passwords do not match</div>\r
          </div>\r
        </div>\r
      </div>\r
\r
      <div>\r
        <button \r
          type="submit" \r
          [disabled]="loading"\r
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"\r
        >\r
          <span *ngIf="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">\r
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">\r
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>\r
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>\r
            </svg>\r
          </span>\r
          <span *ngIf="!loading" class="absolute left-0 inset-y-0 flex items-center pl-3">\r
            <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">\r
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />\r
            </svg>\r
          </span>\r
          Create Admin Account\r
        </button>\r
      </div>\r
    </form>\r
  </div>\r
</div> ` }]
  }], () => [{ type: FormBuilder }, { type: Router }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(InitializeAdminComponent, { className: "InitializeAdminComponent", filePath: "src/app/features/auth/initialize-admin/initialize-admin.component.ts", lineNumber: 16 });
})();
export {
  InitializeAdminComponent
};
//# sourceMappingURL=chunk-GEDO4QZ2.js.map
