using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ClinicManagement.Domain.Entities;
using ClinicManagement.Domain.Enums;
using ClinicManagement.Domain.Repositories;
using ClinicManagement.Application.Common.Interfaces;

namespace ClinicManagement.WebAPI.Controllers
{
    /// <summary>
    /// Admin-only user management endpoints
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]
    [Produces("application/json")]
    public class UserManagementController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IAuthService _authService;

        public UserManagementController(IUserRepository userRepository, IAuthService authService)
        {
            _userRepository = userRepository;
            _authService = authService;
        }

        /// <summary>
        /// Gets all users in the system
        /// </summary>
        /// <returns>List of all users</returns>
        /// <response code="200">Returns the list of users</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<UserManagementDto>>> GetAllUsers()
        {
            var users = await _userRepository.GetAllAsync();
            var userDtos = users.Select(u => new UserManagementDto
            {
                Id = u.Id,
                Email = u.Email,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Role = u.Role,
                IsActive = u.IsActive,
                LastLoginDate = u.LastLoginDate,
                CreatedAt = u.CreatedAt
            });

            return Ok(userDtos);
        }

        /// <summary>
        /// Gets users by role
        /// </summary>
        /// <param name="role">User role to filter by</param>
        /// <returns>List of users with the specified role</returns>
        /// <response code="200">Returns the list of users</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpGet("by-role/{role}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IEnumerable<UserManagementDto>>> GetUsersByRole(UserRole role)
        {
            var users = await _userRepository.GetByRoleAsync(role);
            var userDtos = users.Select(u => new UserManagementDto
            {
                Id = u.Id,
                Email = u.Email,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Role = u.Role,
                IsActive = u.IsActive,
                LastLoginDate = u.LastLoginDate,
                CreatedAt = u.CreatedAt
            });

            return Ok(userDtos);
        }

        /// <summary>
        /// Gets a specific user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        /// <response code="200">Returns the user</response>
        /// <response code="404">If the user is not found</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<UserManagementDto>> GetUser(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            return Ok(new UserManagementDto
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                IsActive = user.IsActive,
                LastLoginDate = user.LastLoginDate,
                CreatedAt = user.CreatedAt
            });
        }

        /// <summary>
        /// Creates a new user (Admin only)
        /// </summary>
        /// <param name="request">User creation data</param>
        /// <returns>Created user information</returns>
        /// <response code="201">Returns the created user</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<UserManagementDto>> CreateUser([FromBody] CreateUserRequest request)
        {
            var result = await _authService.RegisterAsync(
                request.Email,
                request.Password,
                request.FirstName,
                request.LastName,
                request.Role
            );

            if (!result.Success)
            {
                return BadRequest(new { message = result.Message });
            }

            var user = await _userRepository.GetByEmailAsync(request.Email);
            var userDto = new UserManagementDto
            {
                Id = user.Id,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                IsActive = user.IsActive,
                LastLoginDate = user.LastLoginDate,
                CreatedAt = user.CreatedAt
            };

            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, userDto);
        }

        /// <summary>
        /// Updates a user's profile information
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Updated user data</param>
        /// <returns>Success status</returns>
        /// <response code="200">Returns success</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="404">If the user is not found</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] UpdateUserRequest request)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            user.UpdateProfile(request.FirstName, request.LastName);
            if (request.Role != user.Role)
            {
                user.ChangeRole(request.Role);
            }

            await _userRepository.UpdateAsync(user);

            return Ok(new { message = "User updated successfully" });
        }

        /// <summary>
        /// Deactivates a user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Success status</returns>
        /// <response code="200">Returns success</response>
        /// <response code="404">If the user is not found</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpPost("{id}/deactivate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeactivateUser(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            user.Deactivate();
            await _userRepository.UpdateAsync(user);

            return Ok(new { message = "User deactivated successfully" });
        }

        /// <summary>
        /// Activates a user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Success status</returns>
        /// <response code="200">Returns success</response>
        /// <response code="404">If the user is not found</response>
        /// <response code="403">If the user is not an admin</response>
        [HttpPost("{id}/activate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ActivateUser(int id)
        {
            var user = await _userRepository.GetByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            user.Activate();
            await _userRepository.UpdateAsync(user);

            return Ok(new { message = "User activated successfully" });
        }
    }

    public class UserManagementDto
    {
        public int Id { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public UserRole Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateUserRequest
    {
        public string Email { get; set; }
        public string Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public UserRole Role { get; set; }
    }

    public class UpdateUserRequest
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public UserRole Role { get; set; }
    }
}
