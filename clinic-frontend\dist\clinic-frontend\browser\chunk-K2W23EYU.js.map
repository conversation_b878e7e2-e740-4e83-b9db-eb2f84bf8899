{"version": 3, "sources": ["src/app/core/models/auth.model.ts"], "sourcesContent": ["export enum UserRole {\r\n  Admin = 'Admin',\r\n  Doctor = 'Doctor',\r\n  Receptionist = 'Receptionist',\r\n  Patient = 'Patient'\r\n}\r\n\r\nexport interface User {\r\n  id: number;  // Changed from string to number to match backend\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  role: UserRole;\r\n  token?: string;\r\n}\r\n\r\nexport interface LoginRequest {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport interface LoginResponse {\r\n  token: string;\r\n  expiration: string;  // Now matches backend DateTime format\r\n  user: User;\r\n}\r\n\r\nexport interface RegisterRequest {\r\n  email: string;\r\n  password: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  role: UserRole;\r\n}\r\n\r\nexport interface InitializeAdminRequest {\r\n  email: string;\r\n  password: string;\r\n  firstName: string;\r\n  lastName: string;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  token: string;\r\n  user: UserInfo;\r\n  expiresIn: number;\r\n}\r\n\r\nexport interface UserInfo {\r\n  id: number;\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  role: UserRole;\r\n}\r\n\r\nexport interface RefreshTokenResponse {\r\n  token: string;\r\n  expiration: string;\r\n  user: User;\r\n}"], "mappings": ";AAAA,IAAY;CAAZ,SAAYA,WAAQ;AAClB,EAAAA,UAAA,OAAA,IAAA;AACA,EAAAA,UAAA,QAAA,IAAA;AACA,EAAAA,UAAA,cAAA,IAAA;AACA,EAAAA,UAAA,SAAA,IAAA;AACF,GALY,aAAA,WAAQ,CAAA,EAAA;", "names": ["UserRole"]}