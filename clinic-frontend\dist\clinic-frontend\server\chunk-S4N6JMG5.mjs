import './polyfills.server.mjs';
import "./chunk-5WKMABBB.mjs";

// src/app/features/schedules/schedules.routes.ts
var SCHEDULES_ROUTES = [
  // Comment out missing component routes
  // {
  //   path: 'list',
  //   loadComponent: () => import('./schedule-list/schedule-list.component').then(m => m.ScheduleListComponent)
  // },
  // {
  //   path: 'new',
  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)
  // },
  // {
  //   path: 'edit/:id',
  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)
  // },
  // {
  //   path: ':id',
  //   loadComponent: () => import('./schedule-detail/schedule-detail.component').then(m => m.ScheduleDetailComponent)
  // },
];
export {
  SCHEDULES_ROUTES
};
//# sourceMappingURL=chunk-S4N6JMG5.mjs.map
