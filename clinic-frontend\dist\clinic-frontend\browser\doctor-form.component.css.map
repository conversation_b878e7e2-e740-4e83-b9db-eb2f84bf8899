{"version": 3, "sources": ["src/app/features/doctors/doctor-form/doctor-form.component.scss"], "sourcesContent": [".form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\ninput.ng-invalid.ng-touched {\r\n  border-color: #ef4444;\r\n}\r\n\r\n.error-message {\r\n  color: #ef4444;\r\n  font-size: 0.875rem;\r\n  margin-top: 0.25rem;\r\n} "], "mappings": ";AAAA,CAAA;AACE,iBAAA;;AAGF,KAAA,CAAA,UAAA,CAAA;AACE,gBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;;", "names": []}