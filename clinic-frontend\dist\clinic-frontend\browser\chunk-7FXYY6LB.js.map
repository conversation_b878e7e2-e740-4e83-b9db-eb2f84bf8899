{"version": 3, "sources": ["src/app/features/appointments/appointment-list/appointment-list.component.ts", "src/app/features/appointments/appointment-list/appointment-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { AppointmentService } from '../services/appointment.service';\r\nimport { Appointment } from '../models/appointment.model';\r\nimport { PaginatedResponse } from '../../../core/models/api-response.model';\r\nimport { debounceTime, distinctUntilChanged, Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-appointment-list',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, FormsModule],\r\n  templateUrl: './appointment-list.component.html',\r\n  styleUrls: ['./appointment-list.component.scss']\r\n})\r\nexport class AppointmentListComponent implements OnInit {\r\n  appointments: Appointment[] = [];\r\n  loading = false;\r\n  currentPage = 1;\r\n  pageSize = 10;\r\n  totalItems = 0;\r\n  searchTerm = '';\r\n  searchTermChanged = new Subject<string>();\r\n  Math = Math;\r\n\r\n  constructor(private appointmentService: AppointmentService) {\r\n    this.searchTermChanged\r\n      .pipe(\r\n        debounceTime(300),\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe(term => {\r\n        this.searchTerm = term;\r\n        this.currentPage = 1;\r\n        this.loadAppointments();\r\n      });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadAppointments();\r\n  }\r\n\r\n  loadAppointments(): void {\r\n    this.loading = true;\r\n    this.appointmentService.getPaginatedAppointments(this.currentPage, this.pageSize, this.searchTerm)\r\n      .subscribe({\r\n        next: (response: PaginatedResponse<Appointment>) => {\r\n          this.appointments = response.items;\r\n          this.totalItems = response.totalCount;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading appointments:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  onSearch(searchTerm: string): void {\r\n    this.searchTermChanged.next(searchTerm);\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.loadAppointments();\r\n  }\r\n\r\n  onDelete(id: string): void {\r\n    if (confirm('Are you sure you want to delete this appointment?')) {\r\n      this.loading = true;\r\n      this.appointmentService.deleteAppointment(id).subscribe({\r\n        next: () => {\r\n          this.loadAppointments();\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error deleting appointment:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div class=\"flex justify-between items-center mb-6\">\r\n    <h1 class=\"text-2xl font-bold\">Appointments</h1>\r\n    <button\r\n      routerLink=\"new\"\r\n      class=\"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\"\r\n    >\r\n      New Appointment\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"mb-4\">\r\n    <input\r\n      type=\"text\"\r\n      placeholder=\"Search appointments...\"\r\n      class=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\r\n      (input)=\"onSearch($any($event.target).value)\"\r\n    >\r\n  </div>\r\n\r\n  <div *ngIf=\"loading\" class=\"flex justify-center items-center h-64\">\r\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\r\n  </div>\r\n\r\n  <div *ngIf=\"!loading && appointments.length === 0\" class=\"text-center py-8\">\r\n    <p class=\"text-gray-500\">No appointments found.</p>\r\n  </div>\r\n\r\n  <div *ngIf=\"!loading && appointments.length > 0\" class=\"bg-white shadow overflow-hidden sm:rounded-md\">\r\n    <ul class=\"divide-y divide-gray-200\">\r\n      <li *ngFor=\"let appointment of appointments\">\r\n        <div class=\"px-4 py-4 sm:px-6\">\r\n          <div class=\"flex items-center justify-between\">\r\n            <div class=\"flex-1 min-w-0\">\r\n              <p class=\"text-sm font-medium text-indigo-600 truncate\">\r\n                {{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }} with Dr. {{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }}\r\n              </p>\r\n              <p class=\"mt-1 text-sm text-gray-500\">\r\n                {{ appointment.date | date:'mediumDate' }} at {{ appointment.startTime }} - {{ appointment.endTime }}\r\n              </p>\r\n            </div>\r\n            <div class=\"flex space-x-2\">\r\n              <button\r\n                [routerLink]=\"[appointment.id]\"\r\n                class=\"px-3 py-1 text-sm text-indigo-600 hover:text-indigo-900\"\r\n              >\r\n                View\r\n              </button>\r\n              <button\r\n                [routerLink]=\"[appointment.id, 'edit']\"\r\n                class=\"px-3 py-1 text-sm text-gray-600 hover:text-gray-900\"\r\n              >\r\n                Edit\r\n              </button>\r\n              <button\r\n                (click)=\"onDelete(appointment.id)\"\r\n                class=\"px-3 py-1 text-sm text-red-600 hover:text-red-900\"\r\n              >\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </div>\r\n          <div class=\"mt-2\">\r\n            <span\r\n              [ngClass]=\"{\r\n                'bg-green-100 text-green-800': appointment.status === 'Completed',\r\n                'bg-yellow-100 text-yellow-800': appointment.status === 'Scheduled',\r\n                'bg-red-100 text-red-800': appointment.status === 'Cancelled' || appointment.status === 'NoShow'\r\n              }\"\r\n              class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n            >\r\n              {{ appointment.status }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n\r\n  <div *ngIf=\"!loading && totalItems > 0\" class=\"mt-4 flex justify-center\">\r\n    <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\r\n      <button\r\n        *ngFor=\"let page of [].constructor(Math.ceil(totalItems / pageSize)); let i = index\"\r\n        (click)=\"onPageChange(i + 1)\"\r\n        [class.bg-indigo-50]=\"currentPage === i + 1\"\r\n        class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\"\r\n      >\r\n        {{ i + 1 }}\r\n      </button>\r\n    </nav>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoBE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4E,GAAA,KAAA,EAAA;AACjD,IAAA,iBAAA,GAAA,wBAAA;AAAsB,IAAA,uBAAA,EAAI;;;;;;AAKjD,IAAA,yBAAA,GAAA,IAAA,EAA6C,GAAA,OAAA,EAAA,EACZ,GAAA,OAAA,EAAA,EACkB,GAAA,OAAA,EAAA,EACjB,GAAA,KAAA,EAAA;AAExB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,KAAA,EAAA;AACE,IAAA,iBAAA,CAAA;;AACF,IAAA,uBAAA,EAAI;AAEN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,IAAA,UAAA,EAAA;AAKxB,IAAA,iBAAA,IAAA,QAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAIE,IAAA,iBAAA,IAAA,QAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,iBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,eAAA,EAAA,CAAwB;IAAA,CAAA;AAGjC,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAER,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAkB,IAAA,QAAA,EAAA;AASd,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO,EACH,EACF;;;;AAvCE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,eAAA,WAAA,OAAA,OAAA,eAAA,QAAA,WAAA,KAAA,eAAA,WAAA,OAAA,OAAA,eAAA,QAAA,UAAA,cAAA,eAAA,UAAA,OAAA,OAAA,eAAA,OAAA,WAAA,KAAA,eAAA,UAAA,OAAA,OAAA,eAAA,OAAA,UAAA,GAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,GAAA,IAAA,eAAA,MAAA,YAAA,GAAA,QAAA,eAAA,WAAA,OAAA,eAAA,SAAA,GAAA;AAKA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,IAAA,KAAA,eAAA,EAAA,CAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,IAAA,KAAA,eAAA,EAAA,CAAA;AAeF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,0BAAA,IAAA,KAAA,eAAA,WAAA,aAAA,eAAA,WAAA,aAAA,eAAA,WAAA,eAAA,eAAA,WAAA,QAAA,CAAA;AAOA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,eAAA,QAAA,GAAA;;;;;AA3CZ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuG,GAAA,MAAA,EAAA;AAEnG,IAAA,qBAAA,GAAA,+CAAA,IAAA,IAAA,MAAA,EAAA;AA8CF,IAAA,uBAAA,EAAK;;;;AA9CyB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,YAAA;;;;;;AAmD5B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,YAAA,OAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,OAAiB,CAAC,CAAC;IAAA,CAAA;AAI5B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAJE,IAAA,sBAAA,gBAAA,OAAA,gBAAA,OAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,GAAA,GAAA;;;;;AARN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyE,GAAA,OAAA,EAAA;AAErE,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,UAAA,EAAA;AAQF,IAAA,uBAAA,EAAM;;;;AAPe,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,0BAAA,GAAA,GAAA,EAAA,YAAA,OAAA,KAAA,KAAA,OAAA,aAAA,OAAA,QAAA,CAAA,CAAA;;;ADlEnB,IAAO,2BAAP,MAAO,0BAAwB;EAUf;EATpB,eAA8B,CAAA;EAC9B,UAAU;EACV,cAAc;EACd,WAAW;EACX,aAAa;EACb,aAAa;EACb,oBAAoB,IAAI,QAAO;EAC/B,OAAO;EAEP,YAAoB,oBAAsC;AAAtC,SAAA,qBAAA;AAClB,SAAK,kBACF,KACC,aAAa,GAAG,GAChB,qBAAoB,CAAE,EAEvB,UAAU,UAAO;AAChB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,iBAAgB;IACvB,CAAC;EACL;EAEA,WAAQ;AACN,SAAK,iBAAgB;EACvB;EAEA,mBAAgB;AACd,SAAK,UAAU;AACf,SAAK,mBAAmB,yBAAyB,KAAK,aAAa,KAAK,UAAU,KAAK,UAAU,EAC9F,UAAU;MACT,MAAM,CAAC,aAA4C;AACjD,aAAK,eAAe,SAAS;AAC7B,aAAK,aAAa,SAAS;AAC3B,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAc;AACpB,gBAAQ,MAAM,+BAA+B,KAAK;AAClD,aAAK,UAAU;MACjB;KACD;EACL;EAEA,SAAS,YAAkB;AACzB,SAAK,kBAAkB,KAAK,UAAU;EACxC;EAEA,aAAa,MAAY;AACvB,SAAK,cAAc;AACnB,SAAK,iBAAgB;EACvB;EAEA,SAAS,IAAU;AACjB,QAAI,QAAQ,mDAAmD,GAAG;AAChE,WAAK,UAAU;AACf,WAAK,mBAAmB,kBAAkB,EAAE,EAAE,UAAU;QACtD,MAAM,MAAK;AACT,eAAK,iBAAgB;QACvB;QACA,OAAO,CAAC,UAAc;AACpB,kBAAQ,MAAM,+BAA+B,KAAK;AAClD,eAAK,UAAU;QACjB;OACD;IACH;EACF;;qCAjEW,2BAAwB,4BAAA,kBAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,GAAA,QAAA,mBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,cAAA,OAAA,GAAA,QAAA,QAAA,iBAAA,cAAA,cAAA,uBAAA,sBAAA,gBAAA,yBAAA,qBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,QAAA,eAAA,0BAAA,GAAA,UAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,sBAAA,gBAAA,yBAAA,2BAAA,GAAA,OAAA,GAAA,CAAA,SAAA,yCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iDAAA,GAAA,MAAA,GAAA,CAAA,SAAA,4BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,mBAAA,GAAA,CAAA,GAAA,eAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,UAAA,mBAAA,eAAA,GAAA,CAAA,GAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,QAAA,QAAA,SAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,iBAAA,GAAA,CAAA,GAAA,UAAA,SAAA,GAAA,CAAA,GAAA,WAAA,eAAA,mBAAA,UAAA,GAAA,CAAA,GAAA,QAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,GAAA,CAAA,GAAA,QAAA,QAAA,WAAA,mBAAA,yBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,QAAA,QAAA,WAAA,iBAAA,uBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,QAAA,QAAA,WAAA,gBAAA,sBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,aAAA,iBAAA,gBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,QAAA,QAAA,gBAAA,GAAA,CAAA,cAAA,cAAA,GAAA,YAAA,OAAA,eAAA,cAAA,aAAA,aAAA,GAAA,CAAA,SAAA,kIAAA,GAAA,gBAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,mBAAA,YAAA,WAAA,eAAA,iBAAA,oBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AChBrC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,OAAA,CAAA,EACa,GAAA,MAAA,CAAA;AACnB,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;AAC3C,MAAA,yBAAA,GAAA,UAAA,CAAA;AAIE,MAAA,iBAAA,GAAA,mBAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,GAAA,SAAA,CAAA;AAKd,MAAA,qBAAA,SAAA,SAAA,yDAAA,QAAA;AAAA,eAAS,IAAA,SAAA,OAAA,OAAA,KAAA;MAAmC,CAAA;AAJ9C,MAAA,uBAAA,EAKC;AAGH,MAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,CAAA,EAAmE,GAAA,yCAAA,GAAA,GAAA,OAAA,CAAA,EAIS,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA,EAI2B,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AA+DzG,MAAA,uBAAA;;;AAvEQ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,aAAA,WAAA,CAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,aAAA,SAAA,CAAA;AAmDA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,aAAA,CAAA;;oBDnEI,cAAY,SAAA,SAAA,MAAA,UAAE,cAAY,YAAE,WAAW,GAAA,QAAA,CAAA,kSAAA,EAAA,CAAA;;;sEAItC,0BAAwB,CAAA;UAPpC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,+TAAA,EAAA,CAAA;;;;6EAIvC,0BAAwB,EAAA,WAAA,4BAAA,UAAA,gFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}