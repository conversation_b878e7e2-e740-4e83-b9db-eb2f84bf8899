{"version": 3, "sources": ["src/app/features/appointments/models/appointment.model.ts", "src/app/features/appointments/appointment-form/appointment-form.component.ts", "src/app/features/appointments/appointment-form/appointment-form.component.html"], "sourcesContent": ["export enum AppointmentStatus {\r\n  Scheduled = 'Scheduled',\r\n  Completed = 'Completed',\r\n  Cancelled = 'Cancelled',\r\n  NoShow = 'NoShow'\r\n}\r\n\r\nexport interface Appointment {\r\n  id: string;\r\n  patientId: string;\r\n  doctorId: string;\r\n  date: string;\r\n  startTime: string;\r\n  endTime: string;\r\n  status: AppointmentStatus;\r\n  notes?: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  patient?: {\r\n    id: string;\r\n    firstName: string;\r\n    lastName: string;\r\n  };\r\n  doctor?: {\r\n    id: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    specialization: string;\r\n  };\r\n} ", "import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AppointmentService } from '../services/appointment.service';\r\nimport { DoctorService } from '../../doctors/services/doctor.service';\r\nimport { PatientService } from '../../../core/services/patient.service';\r\nimport { Appointment, AppointmentStatus } from '../models/appointment.model';\r\nimport { Doctor } from '../../doctors/models/doctor.model';\r\nimport { Patient } from '../../../core/models/patient.model';\r\n\r\n@Component({\r\n  selector: 'app-appointment-form',\r\n  standalone: true,\r\n  imports: [CommonModule, ReactiveFormsModule],\r\n  templateUrl: './appointment-form.component.html',\r\n  styleUrls: ['./appointment-form.component.scss']\r\n})\r\nexport class AppointmentFormComponent implements OnInit {\r\n  appointmentForm: FormGroup;\r\n  isEditMode = false;\r\n  appointmentId: string | null = null;\r\n  loading = false;\r\n  error: string | null = null;\r\n  doctors: Doctor[] = [];\r\n  patients: Patient[] = [];\r\n  AppointmentStatus = AppointmentStatus;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private appointmentService: AppointmentService,\r\n    private doctorService: DoctorService,\r\n    private patientService: PatientService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) {\r\n    this.appointmentForm = this.fb.group({\r\n      patientId: ['', Validators.required],\r\n      doctorId: ['', Validators.required],\r\n      date: ['', Validators.required],\r\n      startTime: ['', Validators.required],\r\n      endTime: ['', Validators.required],\r\n      status: [AppointmentStatus.Scheduled, Validators.required],\r\n      notes: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadDoctors();\r\n    this.loadPatients();\r\n    this.appointmentId = this.route.snapshot.paramMap.get('id');\r\n    if (this.appointmentId) {\r\n      this.isEditMode = true;\r\n      this.loadAppointment();\r\n    }\r\n  }\r\n\r\n  private loadDoctors(): void {\r\n    this.doctorService.getDoctors().subscribe({\r\n      next: (doctors) => {\r\n        this.doctors = doctors;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading doctors:', error);\r\n        this.error = 'Failed to load doctors';\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadPatients(): void {\r\n    this.patientService.getPatients().subscribe({\r\n      next: (patients: Patient[]) => {\r\n        this.patients = patients;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading patients:', error);\r\n        this.error = 'Failed to load patients';\r\n      }\r\n    });\r\n  }\r\n\r\n  private loadAppointment(): void {\r\n    if (!this.appointmentId) return;\r\n    \r\n    this.loading = true;\r\n    this.error = null;\r\n    \r\n    this.appointmentService.getAppointment(this.appointmentId).subscribe({\r\n      next: (appointment) => {\r\n        this.appointmentForm.patchValue(appointment);\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.error = 'Failed to load appointment data';\r\n        this.loading = false;\r\n        console.error('Error loading appointment:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.appointmentForm.valid) {\r\n      this.loading = true;\r\n      this.error = null;\r\n      const appointmentData = this.appointmentForm.value;\r\n\r\n      const request$ = this.isEditMode && this.appointmentId\r\n        ? this.appointmentService.updateAppointment(this.appointmentId, appointmentData)\r\n        : this.appointmentService.createAppointment(appointmentData);\r\n\r\n      request$.subscribe({\r\n        next: () => {\r\n          this.loading = false;\r\n          this.router.navigate(['/appointments']);\r\n        },\r\n        error: (error) => {\r\n          this.error = 'Failed to save appointment data';\r\n          this.loading = false;\r\n          console.error('Error saving appointment:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto p-4\">\r\n  <h1 class=\"text-2xl font-bold mb-6\">{{ isEditMode ? 'Edit' : 'Create' }} Appointment</h1>\r\n\r\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\r\n    {{ error }}\r\n  </div>\r\n\r\n  <form [formGroup]=\"appointmentForm\" (ngSubmit)=\"onSubmit()\" class=\"space-y-4\">\r\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Patient</label>\r\n        <select formControlName=\"patientId\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n          <option value=\"\">Select a patient</option>\r\n          <option *ngFor=\"let patient of patients\" [value]=\"patient.id\">\r\n            {{ patient.firstName }} {{ patient.lastName }}\r\n          </option>\r\n        </select>\r\n        <div *ngIf=\"appointmentForm.get('patientId')?.invalid && appointmentForm.get('patientId')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select a patient\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Doctor</label>\r\n        <select formControlName=\"doctorId\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n          <option value=\"\">Select a doctor</option>\r\n          <option *ngFor=\"let doctor of doctors\" [value]=\"doctor.id\">\r\n            Dr. {{ doctor.firstName }} {{ doctor.lastName }} ({{ doctor.specialization }})\r\n          </option>\r\n        </select>\r\n        <div *ngIf=\"appointmentForm.get('doctorId')?.invalid && appointmentForm.get('doctorId')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select a doctor\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Date</label>\r\n        <input type=\"date\" formControlName=\"date\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n        <div *ngIf=\"appointmentForm.get('date')?.invalid && appointmentForm.get('date')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select a date\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Start Time</label>\r\n        <input type=\"time\" formControlName=\"startTime\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n        <div *ngIf=\"appointmentForm.get('startTime')?.invalid && appointmentForm.get('startTime')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select a start time\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">End Time</label>\r\n        <input type=\"time\" formControlName=\"endTime\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n        <div *ngIf=\"appointmentForm.get('endTime')?.invalid && appointmentForm.get('endTime')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select an end time\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label class=\"block text-sm font-medium text-gray-700\">Status</label>\r\n        <select formControlName=\"status\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\">\r\n          <option *ngFor=\"let status of AppointmentStatus | keyvalue\" [value]=\"status.value\">\r\n            {{ status.value }}\r\n          </option>\r\n        </select>\r\n        <div *ngIf=\"appointmentForm.get('status')?.invalid && appointmentForm.get('status')?.touched\" class=\"text-red-500 text-sm mt-1\">\r\n          Please select a status\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"md:col-span-2\">\r\n        <label class=\"block text-sm font-medium text-gray-700\">Notes</label>\r\n        <textarea formControlName=\"notes\" rows=\"3\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500\"></textarea>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"flex justify-end space-x-4 mt-6\">\r\n      <button type=\"button\" (click)=\"router.navigate(['/appointments'])\" class=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\">\r\n        Cancel\r\n      </button>\r\n      <button type=\"submit\" [disabled]=\"appointmentForm.invalid || loading\" class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\">\r\n        <span *ngIf=\"loading\" class=\"inline-block animate-spin mr-2\">⟳</span>\r\n        {{ isEditMode ? 'Update' : 'Create' }} Appointment\r\n      </button>\r\n    </div>\r\n  </form>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAY;CAAZ,SAAYA,oBAAiB;AAC3B,EAAAA,mBAAA,WAAA,IAAA;AACA,EAAAA,mBAAA,WAAA,IAAA;AACA,EAAAA,mBAAA,WAAA,IAAA;AACA,EAAAA,mBAAA,QAAA,IAAA;AACF,GALY,sBAAA,oBAAiB,CAAA,EAAA;;;;;AEG3B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,OAAA,GAAA;;;;;AASM,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFyC,IAAA,qBAAA,SAAA,WAAA,EAAA;AACvC,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,WAAA,KAAA,WAAA,UAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;;;AAOE,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFuC,IAAA,qBAAA,SAAA,UAAA,EAAA;AACrC,IAAA,oBAAA;AAAA,IAAA,6BAAA,SAAA,UAAA,WAAA,KAAA,UAAA,UAAA,MAAA,UAAA,gBAAA,IAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,wBAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,8BAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;AAME,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAF4D,IAAA,qBAAA,SAAA,UAAA,KAAA;AAC1D,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,OAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA;;;;;AAcA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6D,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;;;ADhEhE,IAAO,2BAAP,MAAO,0BAAwB;EAWzB;EACA;EACA;EACA;EACA;EACD;EAfT;EACA,aAAa;EACb,gBAA+B;EAC/B,UAAU;EACV,QAAuB;EACvB,UAAoB,CAAA;EACpB,WAAsB,CAAA;EACtB,oBAAoB;EAEpB,YACU,IACA,oBACA,eACA,gBACA,OACD,QAAc;AALb,SAAA,KAAA;AACA,SAAA,qBAAA;AACA,SAAA,gBAAA;AACA,SAAA,iBAAA;AACA,SAAA,QAAA;AACD,SAAA,SAAA;AAEP,SAAK,kBAAkB,KAAK,GAAG,MAAM;MACnC,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,UAAU,CAAC,IAAI,WAAW,QAAQ;MAClC,MAAM,CAAC,IAAI,WAAW,QAAQ;MAC9B,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,SAAS,CAAC,IAAI,WAAW,QAAQ;MACjC,QAAQ,CAAC,kBAAkB,WAAW,WAAW,QAAQ;MACzD,OAAO,CAAC,EAAE;KACX;EACH;EAEA,WAAQ;AACN,SAAK,YAAW;AAChB,SAAK,aAAY;AACjB,SAAK,gBAAgB,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AAC1D,QAAI,KAAK,eAAe;AACtB,WAAK,aAAa;AAClB,WAAK,gBAAe;IACtB;EACF;EAEQ,cAAW;AACjB,SAAK,cAAc,WAAU,EAAG,UAAU;MACxC,MAAM,CAAC,YAAW;AAChB,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAK,QAAQ;MACf;KACD;EACH;EAEQ,eAAY;AAClB,SAAK,eAAe,YAAW,EAAG,UAAU;MAC1C,MAAM,CAAC,aAAuB;AAC5B,aAAK,WAAW;MAClB;MACA,OAAO,CAAC,UAAc;AACpB,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAK,QAAQ;MACf;KACD;EACH;EAEQ,kBAAe;AACrB,QAAI,CAAC,KAAK;AAAe;AAEzB,SAAK,UAAU;AACf,SAAK,QAAQ;AAEb,SAAK,mBAAmB,eAAe,KAAK,aAAa,EAAE,UAAU;MACnE,MAAM,CAAC,gBAAe;AACpB,aAAK,gBAAgB,WAAW,WAAW;AAC3C,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,gBAAQ,MAAM,8BAA8B,KAAK;MACnD;KACD;EACH;EAEA,WAAQ;AACN,QAAI,KAAK,gBAAgB,OAAO;AAC9B,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,YAAM,kBAAkB,KAAK,gBAAgB;AAE7C,YAAM,WAAW,KAAK,cAAc,KAAK,gBACrC,KAAK,mBAAmB,kBAAkB,KAAK,eAAe,eAAe,IAC7E,KAAK,mBAAmB,kBAAkB,eAAe;AAE7D,eAAS,UAAU;QACjB,MAAM,MAAK;AACT,eAAK,UAAU;AACf,eAAK,OAAO,SAAS,CAAC,eAAe,CAAC;QACxC;QACA,OAAO,CAAC,UAAS;AACf,eAAK,QAAQ;AACb,eAAK,UAAU;AACf,kBAAQ,MAAM,6BAA6B,KAAK;QAClD;OACD;IACH;EACF;;qCAxGW,2BAAwB,4BAAA,WAAA,GAAA,4BAAA,kBAAA,GAAA,4BAAA,aAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,KAAA,GAAA,CAAA,GAAA,YAAA,aAAA,MAAA,GAAA,CAAA,SAAA,wEAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,QAAA,eAAA,kBAAA,OAAA,GAAA,CAAA,GAAA,SAAA,WAAA,eAAA,eAAA,GAAA,CAAA,mBAAA,aAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,6BAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,YAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,QAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,aAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,WAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,mBAAA,SAAA,QAAA,KAAA,GAAA,QAAA,SAAA,UAAA,cAAA,mBAAA,aAAA,2BAAA,uBAAA,GAAA,CAAA,GAAA,QAAA,eAAA,aAAA,MAAA,GAAA,CAAA,QAAA,UAAA,GAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,WAAA,eAAA,iBAAA,oBAAA,sBAAA,gBAAA,uBAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,uBAAA,GAAA,UAAA,GAAA,CAAA,SAAA,kCAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,UAAA,kBAAA,gBAAA,QAAA,QAAA,WAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,WAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,MAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AClBrC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,MAAA,CAAA;AACG,MAAA,iBAAA,CAAA;AAAgD,MAAA,uBAAA;AAEpF,MAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,CAAA;AAIA,MAAA,yBAAA,GAAA,QAAA,CAAA;AAAoC,MAAA,qBAAA,YAAA,SAAA,6DAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AACxD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmD,GAAA,KAAA,EAC5C,GAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AAC9D,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAiJ,IAAA,UAAA,CAAA;AAC9H,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjC,MAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,CAAA;AAGF,MAAA,uBAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7D,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAgJ,IAAA,UAAA,CAAA;AAC7H,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAChC,MAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,CAAA;AAGF,MAAA,uBAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AAC3D,MAAA,oBAAA,IAAA,SAAA,EAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACjE,MAAA,oBAAA,IAAA,SAAA,EAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAC/D,MAAA,oBAAA,IAAA,SAAA,EAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAK,IAAA,SAAA,CAAA;AACoD,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7D,MAAA,yBAAA,IAAA,UAAA,EAAA;AACE,MAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,CAAA;;AAGF,MAAA,uBAAA;AACA,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,SAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAC5D,MAAA,oBAAA,IAAA,YAAA,EAAA;AACF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6C,IAAA,UAAA,EAAA;AACrB,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,OAAA,SAAA,CAAiB,eAAe,CAAA;MAAE,CAAA;AAC/D,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,EAAA;AACE,MAAA,qBAAA,IAAA,2CAAA,GAAA,GAAA,QAAA,EAAA;AACA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD;;;;;;;;;AArF6B,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,aAAA,SAAA,UAAA,cAAA;AAE9B,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,eAAA;AAM8B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,QAAA;AAIxB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,UAAA,IAAA,gBAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,IAAA,gBAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AASuB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,OAAA;AAIvB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,UAAA,IAAA,gBAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,IAAA,gBAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,UAAA,UAAA,IAAA,gBAAA,IAAA,MAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,IAAA,gBAAA,IAAA,MAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,UAAA,UAAA,IAAA,gBAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,IAAA,gBAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,UAAA,UAAA,IAAA,gBAAA,IAAA,SAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,IAAA,gBAAA,IAAA,SAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAQuB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,sBAAA,IAAA,IAAA,IAAA,iBAAA,CAAA;AAIvB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,UAAA,WAAA,IAAA,gBAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,cAAA,WAAA,IAAA,gBAAA,IAAA,QAAA,MAAA,OAAA,OAAA,SAAA,QAAA;AAec,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,gBAAA,WAAA,IAAA,OAAA;AACb,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AACP,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,WAAA,UAAA,eAAA;;oBDrEI,cAAY,SAAA,MAAA,cAAE,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,oBAAA,eAAA,GAAA,QAAA,CAAA,kSAAA,EAAA,CAAA;;;sEAIhC,0BAAwB,CAAA;UAPpC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,+TAAA,EAAA,CAAA;;;;6EAIjC,0BAAwB,EAAA,WAAA,4BAAA,UAAA,gFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["AppointmentStatus"]}