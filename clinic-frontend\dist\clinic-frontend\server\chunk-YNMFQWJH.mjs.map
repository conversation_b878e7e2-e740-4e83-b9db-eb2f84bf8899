{"version": 3, "sources": ["src/app/layouts/main-layout/main-layout.component.ts", "src/app/layouts/main-layout/main-layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from '../../core/services/auth.service';\r\nimport { User } from '../../core/models/auth.model';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-main-layout',\r\n  templateUrl: './main-layout.component.html',\r\n  styleUrls: ['./main-layout.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule]\r\n})\r\nexport class MainLayoutComponent implements OnInit {\r\n  isSidebarOpen = false;\r\n  currentUser$: Observable<User | null>;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {\r\n    this.currentUser$ = this.authService.currentUser$;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Initialize sidebar state based on screen size\r\n    this.isSidebarOpen = window.innerWidth >= 768; // 768px is the md breakpoint in Tailwind\r\n  }\r\n\r\n  toggleSidebar(): void {\r\n    this.isSidebarOpen = !this.isSidebarOpen;\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout();\r\n    this.router.navigate(['/login']);\r\n  }\r\n} ", "<div class=\"min-h-screen bg-gray-50\">\r\n  <!-- Mobile menu button -->\r\n  <div class=\"fixed top-0 left-0 right-0 z-50 bg-white shadow-md\">\r\n    <div class=\"flex items-center justify-between px-4 py-3\">\r\n      <button \r\n        (click)=\"toggleSidebar()\" \r\n        class=\"text-gray-600 focus:outline-none focus:text-primary-600 md:hidden\"\r\n      >\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\r\n        </svg>\r\n      </button>\r\n      \r\n      <div class=\"flex items-center\">\r\n        <img src=\"assets/images/logo.png\" alt=\"Clinic Logo\" class=\"h-8 w-auto mr-2\">\r\n        <span class=\"text-xl font-semibold text-primary-700\">Clinic Management</span>\r\n      </div>\r\n      \r\n      <!-- User dropdown -->\r\n      <div class=\"relative\" *ngIf=\"currentUser$ | async as user\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <span class=\"hidden md:block text-sm text-gray-700\">{{ user.firstName }} {{ user.lastName }}</span>\r\n          <button class=\"flex items-center focus:outline-none\">\r\n            <div class=\"w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center text-white\">\r\n              {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}\r\n            </div>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\">\r\n          <a href=\"#\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Profile</a>\r\n          <a href=\"#\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Settings</a>\r\n          <button (click)=\"logout()\" class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n            Sign out\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Sidebar -->\r\n  <div class=\"flex pt-16\">\r\n    <aside \r\n      class=\"fixed inset-y-0 left-0 z-40 w-64 mt-16 transform bg-white border-r border-gray-200 transition duration-300 ease-in-out\"\r\n      [ngClass]=\"{'translate-x-0': isSidebarOpen, '-translate-x-full': !isSidebarOpen, 'md:translate-x-0': true}\"\r\n    >\r\n      <nav class=\"flex flex-col h-full py-4\">\r\n        <div class=\"flex-1 space-y-1 px-2\">\r\n          <a routerLink=\"/dashboard\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\r\n            </svg>\r\n            Dashboard\r\n          </a>\r\n          \r\n          <a routerLink=\"/patients\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n            </svg>\r\n            Patients\r\n          </a>\r\n          \r\n          <a routerLink=\"/doctors\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\r\n            </svg>\r\n            Doctors\r\n          </a>\r\n          \r\n          <a routerLink=\"/appointments\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n            </svg>\r\n            Appointments\r\n          </a>\r\n          \r\n          <a routerLink=\"/medical-records\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n            </svg>\r\n            Medical Records\r\n          </a>\r\n          \r\n          <a routerLink=\"/schedules\" routerLinkActive=\"bg-primary-50 text-primary-700\" class=\"group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n            Schedules\r\n          </a>\r\n        </div>\r\n        \r\n        <div class=\"px-4 mt-6\">\r\n          <button (click)=\"logout()\" class=\"w-full flex items-center px-2 py-2 text-base font-medium text-red-600 rounded-md hover:bg-red-50\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-4 h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n            Sign out\r\n          </button>\r\n        </div>\r\n      </nav>\r\n    </aside>\r\n\r\n    <!-- Main content -->\r\n    <main class=\"flex-1 md:ml-64 p-6 pt-20\">\r\n      <router-outlet></router-outlet>\r\n    </main>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBM,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA,EAChB,GAAA,QAAA,EAAA;AACa,IAAA,iBAAA,CAAA;AAAwC,IAAA,uBAAA;AAC5F,IAAA,yBAAA,GAAA,UAAA,EAAA,EAAqD,GAAA,OAAA,EAAA;AAEjD,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM,EACC;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgF,GAAA,KAAA,EAAA;AACF,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AACnF,IAAA,yBAAA,IAAA,KAAA,EAAA;AAA4E,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACpF,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAQ,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,OAAA,CAAQ;IAAA,CAAA;AACvB,IAAA,iBAAA,IAAA,YAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAdgD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,QAAA,WAAA,KAAA,QAAA,UAAA,EAAA;AAGhD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,UAAA,OAAA,CAAA,GAAA,IAAA,QAAA,SAAA,OAAA,CAAA,GAAA,GAAA;;;ADTR,IAAO,sBAAP,MAAO,qBAAmB;EAKpB;EACA;EALV,gBAAgB;EAChB;EAEA,YACU,QACA,aAAwB;AADxB,SAAA,SAAA;AACA,SAAA,cAAA;AAER,SAAK,eAAe,KAAK,YAAY;EACvC;EAEA,WAAQ;AAEN,SAAK,gBAAgB,OAAO,cAAc;EAC5C;EAEA,gBAAa;AACX,SAAK,gBAAgB,CAAC,KAAK;EAC7B;EAEA,SAAM;AACJ,SAAK,YAAY,OAAM;AACvB,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;;qCAvBW,sBAAmB,4BAAA,MAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,gBAAA,YAAA,GAAA,CAAA,GAAA,SAAA,SAAA,UAAA,WAAA,QAAA,YAAA,WAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,mBAAA,QAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,sBAAA,0BAAA,aAAA,GAAA,OAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,OAAA,KAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,yBAAA,GAAA,CAAA,GAAA,QAAA,cAAA,GAAA,CAAA,OAAA,0BAAA,OAAA,eAAA,GAAA,OAAA,UAAA,MAAA,GAAA,CAAA,GAAA,WAAA,iBAAA,kBAAA,GAAA,CAAA,SAAA,YAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,SAAA,aAAA,UAAA,QAAA,QAAA,SAAA,aAAA,YAAA,YAAA,mBAAA,cAAA,gBAAA,eAAA,GAAA,SAAA,GAAA,CAAA,GAAA,QAAA,YAAA,UAAA,MAAA,GAAA,CAAA,GAAA,UAAA,aAAA,MAAA,GAAA,CAAA,cAAA,cAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,QAAA,OAAA,KAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,kJAAA,GAAA,CAAA,cAAA,aAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,wQAAA,GAAA,CAAA,cAAA,YAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,qEAAA,GAAA,CAAA,cAAA,iBAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,wFAAA,GAAA,CAAA,cAAA,oBAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,sHAAA,GAAA,CAAA,cAAA,cAAA,oBAAA,kCAAA,GAAA,SAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,cAAA,uBAAA,wBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,6CAAA,GAAA,CAAA,GAAA,QAAA,MAAA,GAAA,CAAA,GAAA,UAAA,QAAA,gBAAA,QAAA,QAAA,aAAA,eAAA,gBAAA,cAAA,mBAAA,GAAA,OAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,2FAAA,GAAA,CAAA,GAAA,UAAA,YAAA,OAAA,OAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,WAAA,GAAA,CAAA,GAAA,UAAA,YAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,gBAAA,oBAAA,GAAA,CAAA,GAAA,OAAA,OAAA,gBAAA,kBAAA,QAAA,gBAAA,kBAAA,YAAA,GAAA,CAAA,GAAA,YAAA,WAAA,QAAA,QAAA,YAAA,cAAA,aAAA,QAAA,MAAA,GAAA,CAAA,QAAA,KAAA,GAAA,SAAA,QAAA,QAAA,WAAA,iBAAA,mBAAA,GAAA,CAAA,GAAA,SAAA,UAAA,aAAA,QAAA,QAAA,WAAA,iBAAA,qBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACfhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAqC,GAAA,OAAA,CAAA,EAE6B,GAAA,OAAA,CAAA,EACL,GAAA,UAAA,CAAA;AAErD,MAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,eAAS,IAAA,cAAA;MAAe,CAAA;;AAGxB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA,EAAM;;AAGR,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,OAAA,CAAA;AACA,MAAA,yBAAA,GAAA,QAAA,CAAA;AAAqD,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA,EAAO;AAI/E,MAAA,qBAAA,IAAA,qCAAA,IAAA,GAAA,OAAA,CAAA;;AAkBF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA,EAIrB,IAAA,OAAA,EAAA,EACwC,IAAA,OAAA,EAAA,EACF,IAAA,KAAA,EAAA;;AAE/B,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,aAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,YAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,WAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,KAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,aAAA;AACF,MAAA,uBAAA,EAAI;;AAGN,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,UAAA,EAAA;AACb,MAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;;AACvB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,YAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;;AAIR,MAAA,yBAAA,IAAA,QAAA,EAAA;AACE,MAAA,oBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAO,EACH;;;AAvFqB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,sBAAA,IAAA,GAAA,IAAA,YAAA,CAAA;AAyBvB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,0BAAA,GAAA,KAAA,IAAA,eAAA,CAAA,IAAA,aAAA,CAAA;;oBD/BM,cAAY,SAAA,MAAA,WAAE,cAAY,cAAA,YAAA,gBAAA,GAAA,QAAA,CAAA,iVAAA,EAAA,CAAA;;;sEAEzB,qBAAmB,CAAA;UAP/B;uBACW,mBAAiB,YAGf,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,6TAAA,EAAA,CAAA;;;;6EAE1B,qBAAmB,EAAA,WAAA,uBAAA,UAAA,wDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}