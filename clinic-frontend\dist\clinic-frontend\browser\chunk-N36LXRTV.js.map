{"version": 3, "sources": ["src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-medical-record-detail',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  template: `\r\n    <div class=\"container mx-auto p-4\">\r\n      <h1 class=\"text-2xl font-bold mb-4\">Medical Record Details</h1>\r\n      <div class=\"grid gap-4\">\r\n        <!-- Placeholder for medical record details -->\r\n        <p>Medical record details will be displayed here</p>\r\n      </div>\r\n      <div class=\"mt-4\">\r\n        <a routerLink=\"..\" class=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\">\r\n          Back to List\r\n        </a>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: []\r\n})\r\nexport class MedicalRecordDetailComponent implements OnInit {\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {}\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;AAwBM,IAAO,+BAAP,MAAO,8BAA4B;EACvC,cAAA;EAAe;EAEf,WAAQ;EAAU;;qCAHP,+BAA4B;EAAA;yEAA5B,+BAA4B,WAAA,CAAA,CAAA,2BAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,KAAA,GAAA,CAAA,GAAA,YAAA,aAAA,MAAA,GAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,cAAA,MAAA,GAAA,eAAA,cAAA,QAAA,QAAA,WAAA,mBAAA,CAAA,GAAA,UAAA,SAAA,sCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAfrC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,MAAA,CAAA;AACG,MAAA,iBAAA,GAAA,wBAAA;AAAsB,MAAA,uBAAA;AAC1D,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,GAAA;AAEnB,MAAA,iBAAA,GAAA,+CAAA;AAA6C,MAAA,uBAAA,EAAI;AAEtD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,GAAA,KAAA,CAAA;AAEd,MAAA,iBAAA,GAAA,gBAAA;AACF,MAAA,uBAAA,EAAI,EACA;;oBAZA,cAAc,cAAY,UAAA,GAAA,eAAA,EAAA,CAAA;;;sEAiBzB,8BAA4B,CAAA;UApBxC;uBACW,6BAA2B,YACzB,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAC3B;;;;;;;;;;;;;IAaT,CAAA;;;;6EAGU,8BAA4B,EAAA,WAAA,gCAAA,UAAA,wGAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}