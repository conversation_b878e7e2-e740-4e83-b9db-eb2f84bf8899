{"version": 3, "sources": ["src/app/features/user-management/services/user-management.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, catchError, throwError } from 'rxjs';\nimport { UserManagement, CreateUserRequest, UpdateUserRequest } from '../models/user-management.model';\nimport { UserRole } from '../../../core/models/auth.model';\nimport { environment } from '../../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserManagementService {\n  private apiUrl = `${environment.apiUrl}/usermanagement`;\n\n  constructor(private http: HttpClient) {}\n\n  getAllUsers(): Observable<UserManagement[]> {\n    return this.http.get<UserManagement[]>(this.apiUrl).pipe(\n      catchError((error) => {\n        console.error('Failed to fetch users:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  getUsersByRole(role: UserRole): Observable<UserManagement[]> {\n    return this.http.get<UserManagement[]>(`${this.apiUrl}/by-role/${role}`).pipe(\n      catchError((error) => {\n        console.error(`Failed to fetch users by role ${role}:`, error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  getUserById(id: number): Observable<UserManagement> {\n    return this.http.get<UserManagement>(`${this.apiUrl}/${id}`).pipe(\n      catchError((error) => {\n        console.error(`Failed to fetch user ${id}:`, error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  createUser(user: CreateUserRequest): Observable<UserManagement> {\n    return this.http.post<UserManagement>(this.apiUrl, user).pipe(\n      catchError((error) => {\n        console.error('Failed to create user:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  updateUser(id: number, user: UpdateUserRequest): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, user).pipe(\n      catchError((error) => {\n        console.error(`Failed to update user ${id}:`, error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  deactivateUser(id: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${id}/deactivate`, {}).pipe(\n      catchError((error) => {\n        console.error(`Failed to deactivate user ${id}:`, error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  activateUser(id: number): Observable<any> {\n    return this.http.post(`${this.apiUrl}/${id}/activate`, {}).pipe(\n      catchError((error) => {\n        console.error(`Failed to activate user ${id}:`, error);\n        return throwError(() => error);\n      })\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAUM,IAAO,wBAAP,MAAO,uBAAqB;EAGZ;EAFZ,SAAS,GAAG,YAAY,MAAM;EAEtC,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;EAEvC,cAAW;AACT,WAAO,KAAK,KAAK,IAAsB,KAAK,MAAM,EAAE,KAClD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,eAAe,MAAc;AAC3B,WAAO,KAAK,KAAK,IAAsB,GAAG,KAAK,MAAM,YAAY,IAAI,EAAE,EAAE,KACvE,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,iCAAiC,IAAI,KAAK,KAAK;AAC7D,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,YAAY,IAAU;AACpB,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE,EAAE,KAC3D,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,wBAAwB,EAAE,KAAK,KAAK;AAClD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,WAAW,MAAuB;AAChC,WAAO,KAAK,KAAK,KAAqB,KAAK,QAAQ,IAAI,EAAE,KACvD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,WAAW,IAAY,MAAuB;AAC5C,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,IAAI,EAAE,IAAI,IAAI,EAAE,KACjD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,yBAAyB,EAAE,KAAK,KAAK;AACnD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,eAAe,IAAU;AACvB,WAAO,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,EAAE,eAAe,CAAA,CAAE,EAAE,KAC3D,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,6BAA6B,EAAE,KAAK,KAAK;AACvD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;EAEA,aAAa,IAAU;AACrB,WAAO,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,EAAE,aAAa,CAAA,CAAE,EAAE,KACzD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,2BAA2B,EAAE,KAAK,KAAK;AACrD,aAAO,WAAW,MAAM,KAAK;IAC/B,CAAC,CAAC;EAEN;;qCAlEW,wBAAqB,mBAAA,UAAA,CAAA;EAAA;4EAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;;;sEAEP,uBAAqB,CAAA;UAHjC;WAAW;MACV,YAAY;KACb;;;", "names": []}