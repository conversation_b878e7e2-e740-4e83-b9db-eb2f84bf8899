{"openapi": "3.0.4", "info": {"title": "Clinic Management API", "description": "A modern REST API for clinic management built with ASP.NET Core 9", "contact": {"name": "Clinic Management Team", "url": "https://clinicmanagement.com/contact", "email": "<EMAIL>"}, "license": {"name": "Use under license", "url": "https://clinicmanagement.com/license"}, "version": "v1"}, "paths": {"/api/Appointments/{id}": {"get": {"tags": ["Appointments"], "summary": "Gets an appointment by ID", "parameters": [{"name": "id", "in": "path", "description": "Appointment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Common.AppointmentDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["Appointments"], "summary": "Updates an existing appointment", "parameters": [{"name": "id", "in": "path", "description": "Appointment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Updated appointment data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Appointments"], "summary": "Cancels an appointment", "parameters": [{"name": "id", "in": "path", "description": "Appointment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Cancellation details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Appointments/date-range": {"get": {"tags": ["Appointments"], "summary": "Gets appointments by date range", "parameters": [{"name": "startDate", "in": "query", "description": "Start date", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "End date", "schema": {"type": "string", "format": "date-time"}}, {"name": "doctorId", "in": "query", "description": "Optional doctor ID", "schema": {"type": "integer", "format": "int32"}}, {"name": "patientId", "in": "query", "description": "Optional patient ID", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Appointments": {"post": {"tags": ["Appointments"], "summary": "Creates a new appointment", "requestBody": {"description": "Appointment data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Appointments/{id}/cancel": {"post": {"tags": ["Appointments"], "summary": "Cancels an appointment (alternative endpoint for simple cancellation)", "parameters": [{"name": "id", "in": "path", "description": "Appointment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Authenticates a user and returns a JWT token", "requestBody": {"description": "Login credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.LoginRequest"}}}}, "responses": {"200": {"description": "Returns the JWT token and user information"}, "401": {"description": "If the credentials are invalid", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Registers a new user", "requestBody": {"description": "Registration information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.RegisterRequest"}}}}, "responses": {"200": {"description": "Returns success message"}, "400": {"description": "If the email is already taken or other validation errors", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Auth/initialize": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Creates the initial admin user if no users exist in the system", "requestBody": {"description": "Admin user information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.InitializeAdminRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.InitializeAdminRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.InitializeAdminRequest"}}}}, "responses": {"200": {"description": "Returns success message"}, "400": {"description": "If users already exist or other validation errors", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Gets the current authenticated user's information", "responses": {"200": {"description": "Returns the current user information"}, "401": {"description": "If the user is not authenticated", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refreshes the JWT token for the current user", "responses": {"200": {"description": "Returns the new JWT token"}, "401": {"description": "If the user is not authenticated", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Doctors": {"get": {"tags": ["Doctors"], "summary": "Gets a list of all doctors with optional filtering by specialization", "parameters": [{"name": "specialization", "in": "query", "description": "Optional specialization to filter by", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns the list of doctors", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Doctors/{id}": {"get": {"tags": ["Doctors"], "summary": "Gets a specific doctor by their ID", "parameters": [{"name": "id", "in": "path", "description": "The ID of the doctor", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the doctor details"}, "404": {"description": "If the doctor is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/Doctors/{id}/schedule": {"get": {"tags": ["Doctors"], "summary": "Gets the schedule for a specific doctor", "parameters": [{"name": "id", "in": "path", "description": "The ID of the doctor", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the doctor's schedule", "content": {"application/json": {"schema": {"type": "array", "items": {}}}}}, "404": {"description": "If the doctor is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/frontend/FrontendPatients": {"get": {"tags": ["FrontendPatients"], "summary": "Gets a list of patients with optional filtering and pagination (unwrapped response)", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the list of patients", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Common.PatientDto"}}}}}, "400": {"description": "If the query parameters are invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["FrontendPatients"], "summary": "Creates a new patient (unwrapped response)", "requestBody": {"description": "Patient data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/frontend/FrontendPatients/{id}/details": {"get": {"tags": ["FrontendPatients"], "summary": "Gets detailed information about a specific patient (unwrapped response)", "parameters": [{"name": "id", "in": "path", "description": "The ID of the patient", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the patient details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.PatientDetailDto"}}}}, "404": {"description": "If the patient is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/frontend/FrontendPatients/{id}": {"get": {"tags": ["FrontendPatients"], "summary": "Gets a patient by ID (unwrapped response)", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Common.PatientDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["FrontendPatients"], "summary": "Updates an existing patient (unwrapped response)", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Updated patient data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["FrontendPatients"], "summary": "Deletes a patient (unwrapped response)", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/MedicalRecords/{id}": {"get": {"tags": ["MedicalRecords"], "summary": "Gets a medical record by ID", "parameters": [{"name": "id", "in": "path", "description": "Medical record ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.MedicalRecords.Common.MedicalRecordDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/MedicalRecords/patient/{patientId}": {"get": {"tags": ["MedicalRecords"], "summary": "Gets all medical records for a patient", "parameters": [{"name": "patientId", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "skip", "in": "query", "description": "Number of records to skip", "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "take", "in": "query", "description": "Number of records to take", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/MedicalRecords": {"post": {"tags": ["MedicalRecords"], "summary": "Creates a new medical record", "requestBody": {"description": "Medical record data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Patients": {"get": {"tags": ["Patients"], "summary": "Gets a list of patients with optional filtering and pagination", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the list of patients", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Common.PatientDto"}}}}}, "400": {"description": "If the query parameters are invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "post": {"tags": ["Patients"], "summary": "Creates a new patient", "requestBody": {"description": "Patient data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Patients/{id}/details": {"get": {"tags": ["Patients"], "summary": "Gets detailed information about a specific patient", "parameters": [{"name": "id", "in": "path", "description": "The ID of the patient", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the patient details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.PatientDetailDto"}}}}, "404": {"description": "If the patient is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Patients/{id}": {"get": {"tags": ["Patients"], "summary": "Gets a patient by ID", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Common.PatientDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["Patients"], "summary": "Updates an existing patient", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Updated patient data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Patients"], "summary": "Deletes a patient", "parameters": [{"name": "id", "in": "path", "description": "Patient ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Schedules/{id}": {"get": {"tags": ["Schedules"], "summary": "Gets a schedule by ID", "parameters": [{"name": "id", "in": "path", "description": "Schedule ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Schedules.Common.ScheduleDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Schedules/doctor/{doctorId}": {"get": {"tags": ["Schedules"], "summary": "Gets all schedules for a doctor", "parameters": [{"name": "doctorId", "in": "path", "description": "Doctor ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Schedules": {"post": {"tags": ["Schedules"], "summary": "Creates a new schedule", "requestBody": {"description": "Schedule data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/UserManagement": {"get": {"tags": ["UserManagement"], "summary": "Gets all users in the system", "responses": {"200": {"description": "Returns the list of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UserManagementDto"}}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}, "post": {"tags": ["UserManagement"], "summary": "Creates a new user (Admin only)", "requestBody": {"description": "User creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.CreateUserRequest"}}}}, "responses": {"201": {"description": "Returns the created user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UserManagementDto"}}}}, "400": {"description": "If the request is invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/UserManagement/by-role/{role}": {"get": {"tags": ["UserManagement"], "summary": "Gets users by role", "parameters": [{"name": "role", "in": "path", "description": "User role to filter by", "required": true, "schema": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.UserRole"}}], "responses": {"200": {"description": "Returns the list of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UserManagementDto"}}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/UserManagement/{id}": {"get": {"tags": ["UserManagement"], "summary": "Gets a specific user by ID", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns the user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UserManagementDto"}}}}, "404": {"description": "If the user is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}, "put": {"tags": ["UserManagement"], "summary": "Updates a user's profile information", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "Updated user data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClinicManagement.WebAPI.Controllers.UpdateUserRequest"}}}}, "responses": {"200": {"description": "Returns success"}, "400": {"description": "If the request is invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "404": {"description": "If the user is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/UserManagement/{id}/deactivate": {"post": {"tags": ["UserManagement"], "summary": "Deactivates a user account", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns success"}, "404": {"description": "If the user is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}, "/api/UserManagement/{id}/activate": {"post": {"tags": ["UserManagement"], "summary": "Activates a user account", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns success"}, "404": {"description": "If the user is not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}, "403": {"description": "If the user is not an admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ProblemDetails"}}}}}}}}, "components": {"schemas": {"ClinicManagement.Application.Features.Appointments.Commands.CancelAppointment.CancelAppointmentCommand": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Appointments.Commands.CreateAppointment.CreateAppointmentCommand": {"required": ["Notes"], "type": "object", "properties": {"PatientId": {"type": "integer", "format": "int32"}, "DoctorId": {"type": "integer", "format": "int32"}, "AppointmentDate": {"type": "string", "format": "date-time"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "Status": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Appointments.Commands.UpdateAppointment.UpdateAppointmentCommand": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "PatientId": {"type": "integer", "format": "int32"}, "DoctorId": {"type": "integer", "format": "int32"}, "AppointmentDate": {"type": "string", "format": "date-time"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "Type": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.AppointmentType"}, "Status": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.AppointmentStatus"}, "Notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Appointments.Common.AppointmentDto": {"required": ["CreatedBy", "<PERSON><PERSON><PERSON>", "Notes", "PatientName", "Status", "Type"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "PatientId": {"type": "integer", "format": "int32"}, "PatientName": {"type": "string", "nullable": true}, "DoctorId": {"type": "integer", "format": "int32"}, "DoctorName": {"type": "string", "nullable": true}, "AppointmentDate": {"type": "string", "format": "date-time"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "Status": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "string", "nullable": true}, "LastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "LastModifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.MedicalRecords.Commands.CreateMedicalRecord.CreateMedicalRecordCommand": {"required": ["Diagnosis", "Notes", "Prescription", "Treatment"], "type": "object", "properties": {"PatientId": {"type": "integer", "format": "int32"}, "AppointmentId": {"type": "integer", "format": "int32", "nullable": true}, "Diagnosis": {"type": "string", "nullable": true}, "Treatment": {"type": "string", "nullable": true}, "Prescription": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}, "RecordDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ClinicManagement.Application.Features.MedicalRecords.Common.MedicalRecordDto": {"required": ["CreatedBy", "Diagnosis", "Notes", "PatientName", "Prescription", "Treatment"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "PatientId": {"type": "integer", "format": "int32"}, "PatientName": {"type": "string", "nullable": true}, "AppointmentId": {"type": "integer", "format": "int32", "nullable": true}, "Diagnosis": {"type": "string", "nullable": true}, "Treatment": {"type": "string", "nullable": true}, "Prescription": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}, "RecordDate": {"type": "string", "format": "date-time"}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "string", "nullable": true}, "LastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "LastModifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Commands.CreatePatient.CreatePatientCommand": {"required": ["City", "Email", "FirstName", "LastName", "PhoneNumber", "PostalCode", "State", "Street"], "type": "object", "properties": {"FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "MiddleName": {"type": "string", "nullable": true}, "DateOfBirth": {"type": "string", "format": "date-time"}, "Gender": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.Gender"}, "PhoneNumber": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Street": {"type": "string", "nullable": true}, "City": {"type": "string", "nullable": true}, "State": {"type": "string", "nullable": true}, "PostalCode": {"type": "string", "nullable": true}, "Country": {"type": "string", "nullable": true}, "MedicalHistory": {"type": "string", "nullable": true}, "EmergencyContactName": {"type": "string", "nullable": true}, "EmergencyContactPhone": {"type": "string", "nullable": true}, "InsuranceProvider": {"type": "string", "nullable": true}, "InsurancePolicyNumber": {"type": "string", "nullable": true}, "Allergies": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Commands.UpdatePatient.UpdatePatientCommand": {"required": ["City", "Email", "FirstName", "LastName", "PhoneNumber", "PostalCode", "State", "Street"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "MiddleName": {"type": "string", "nullable": true}, "DateOfBirth": {"type": "string", "format": "date-time"}, "Gender": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.Gender"}, "PhoneNumber": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Street": {"type": "string", "nullable": true}, "City": {"type": "string", "nullable": true}, "State": {"type": "string", "nullable": true}, "PostalCode": {"type": "string", "nullable": true}, "Country": {"type": "string", "nullable": true}, "MedicalHistory": {"type": "string", "nullable": true}, "EmergencyContactName": {"type": "string", "nullable": true}, "EmergencyContactPhone": {"type": "string", "nullable": true}, "InsuranceProvider": {"type": "string", "nullable": true}, "InsurancePolicyNumber": {"type": "string", "nullable": true}, "Allergies": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Common.PatientDto": {"required": ["City", "Country", "CreatedBy", "Email", "FirstName", "LastName", "MedicalHistory", "PhoneNumber", "PostalCode", "State", "Street"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "MiddleName": {"type": "string", "nullable": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "DateOfBirth": {"type": "string", "format": "date-time"}, "Age": {"type": "integer", "format": "int32", "readOnly": true}, "Gender": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.Gender"}, "PhoneNumber": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Street": {"type": "string", "nullable": true}, "City": {"type": "string", "nullable": true}, "State": {"type": "string", "nullable": true}, "PostalCode": {"type": "string", "nullable": true}, "Country": {"type": "string", "nullable": true}, "RegistrationDate": {"type": "string", "format": "date-time"}, "MedicalHistory": {"type": "string", "nullable": true}, "EmergencyContactName": {"type": "string", "nullable": true}, "EmergencyContactPhone": {"type": "string", "nullable": true}, "InsuranceProvider": {"type": "string", "nullable": true}, "InsurancePolicyNumber": {"type": "string", "nullable": true}, "Allergies": {"type": "string", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "string", "nullable": true}, "LastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "LastModifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.AppointmentDto": {"required": ["<PERSON><PERSON><PERSON>", "Notes", "Status", "Type"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "DoctorId": {"type": "integer", "format": "int32"}, "DoctorName": {"type": "string", "nullable": true}, "AppointmentDate": {"type": "string", "format": "date-time"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "Status": {"type": "string", "nullable": true}, "Type": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.MedicalRecordDto": {"required": ["Diagnosis", "Notes", "Prescription", "Treatment"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "AppointmentId": {"type": "integer", "format": "int32", "nullable": true}, "Diagnosis": {"type": "string", "nullable": true}, "Treatment": {"type": "string", "nullable": true}, "Prescription": {"type": "string", "nullable": true}, "Notes": {"type": "string", "nullable": true}, "RecordDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.PatientDetailDto": {"required": ["City", "Country", "CreatedBy", "Email", "FirstName", "LastName", "MedicalHistory", "PhoneNumber", "PostalCode", "State", "Street"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "MiddleName": {"type": "string", "nullable": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "DateOfBirth": {"type": "string", "format": "date-time"}, "Age": {"type": "integer", "format": "int32", "readOnly": true}, "Gender": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.Gender"}, "PhoneNumber": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Street": {"type": "string", "nullable": true}, "City": {"type": "string", "nullable": true}, "State": {"type": "string", "nullable": true}, "PostalCode": {"type": "string", "nullable": true}, "Country": {"type": "string", "nullable": true}, "RegistrationDate": {"type": "string", "format": "date-time"}, "MedicalHistory": {"type": "string", "nullable": true}, "EmergencyContactName": {"type": "string", "nullable": true}, "EmergencyContactPhone": {"type": "string", "nullable": true}, "InsuranceProvider": {"type": "string", "nullable": true}, "InsurancePolicyNumber": {"type": "string", "nullable": true}, "Allergies": {"type": "string", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "string", "nullable": true}, "LastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "LastModifiedBy": {"type": "string", "nullable": true}, "Appointments": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.AppointmentDto"}, "nullable": true}, "MedicalRecords": {"type": "array", "items": {"$ref": "#/components/schemas/ClinicManagement.Application.Features.Patients.Queries.GetPatientDetail.MedicalRecordDto"}, "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Schedules.Commands.CreateSchedule.CreateScheduleCommand": {"type": "object", "properties": {"DoctorId": {"type": "integer", "format": "int32"}, "DayOfWeek": {"$ref": "#/components/schemas/System.DayOfWeek"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "IsAvailable": {"type": "boolean"}}, "additionalProperties": false}, "ClinicManagement.Application.Features.Schedules.Common.ScheduleDto": {"required": ["CreatedBy", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "DoctorId": {"type": "integer", "format": "int32"}, "DoctorName": {"type": "string", "nullable": true}, "DayOfWeek": {"$ref": "#/components/schemas/System.DayOfWeek"}, "StartTime": {"type": "string", "format": "date-span"}, "EndTime": {"type": "string", "format": "date-span"}, "IsAvailable": {"type": "boolean"}, "CreatedAt": {"type": "string", "format": "date-time"}, "CreatedBy": {"type": "string", "nullable": true}, "LastModifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "LastModifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.Domain.Enums.AppointmentStatus": {"enum": ["Scheduled", "Confirmed", "InProgress", "Completed", "Cancelled", "NoShow", "Rescheduled"], "type": "string"}, "ClinicManagement.Domain.Enums.AppointmentType": {"enum": ["Regular", "FollowUp", "Emergency", "Consultation", "Checkup", "Surgery", "Therapy"], "type": "string"}, "ClinicManagement.Domain.Enums.Gender": {"enum": ["Male", "Female", "Other", "PreferNotToSay"], "type": "string"}, "ClinicManagement.Domain.Enums.UserRole": {"enum": ["Admin", "Doctor", "Receptionist", "Patient"], "type": "string"}, "ClinicManagement.WebAPI.Controllers.CreateUserRequest": {"type": "object", "properties": {"Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "Role": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.UserRole"}}, "additionalProperties": false}, "ClinicManagement.WebAPI.Controllers.InitializeAdminRequest": {"type": "object", "properties": {"Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.WebAPI.Controllers.LoginRequest": {"type": "object", "properties": {"Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClinicManagement.WebAPI.Controllers.RegisterRequest": {"type": "object", "properties": {"Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "Role": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.UserRole"}}, "additionalProperties": false}, "ClinicManagement.WebAPI.Controllers.UpdateUserRequest": {"type": "object", "properties": {"FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "Role": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.UserRole"}}, "additionalProperties": false}, "ClinicManagement.WebAPI.Controllers.UserManagementDto": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "Email": {"type": "string", "nullable": true}, "FirstName": {"type": "string", "nullable": true}, "LastName": {"type": "string", "nullable": true}, "Role": {"$ref": "#/components/schemas/ClinicManagement.Domain.Enums.UserRole"}, "IsActive": {"type": "boolean"}, "LastLoginDate": {"type": "string", "format": "date-time", "nullable": true}, "CreatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Microsoft.AspNetCore.Mvc.ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "System.DayOfWeek": {"enum": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "type": "string"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}