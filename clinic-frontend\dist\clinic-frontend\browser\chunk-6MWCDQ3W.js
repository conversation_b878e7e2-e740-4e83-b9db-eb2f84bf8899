import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/user-management/user-management.routes.ts
var USER_MANAGEMENT_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-T273NTF3.js").then((m) => m.UserListComponent)
  }, false ? { \u0275entryName: "src/app/features/user-management/user-list/user-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-C6PMV3VT.js").then((m) => m.UserFormComponent)
  }, false ? { \u0275entryName: "src/app/features/user-management/user-form/user-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-HOML5JBJ.js").then((m) => m.UserDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/user-management/user-detail/user-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-C6PMV3VT.js").then((m) => m.UserFormComponent)
  }, false ? { \u0275entryName: "src/app/features/user-management/user-form/user-form.component.ts" } : {})
];
export {
  USER_MANAGEMENT_ROUTES
};
//# sourceMappingURL=chunk-6MWCDQ3W.js.map
