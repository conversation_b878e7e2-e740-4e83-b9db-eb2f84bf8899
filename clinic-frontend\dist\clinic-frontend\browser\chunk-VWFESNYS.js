import {
  UserRole
} from "./chunk-K2W23EYU.js";
import {
  AuthService
} from "./chunk-E4RC7AJA.js";
import "./chunk-7NNESOLN.js";
import {
  AsyncPipe,
  CommonModule,
  Component,
  NgClass,
  NgIf,
  Router,
  RouterLink,
  RouterLinkActive,
  RouterModule,
  RouterOutlet,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵpureFunction2,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate2
} from "./chunk-BMSBKD5S.js";
import "./chunk-Y5RQAIA6.js";

// src/app/layouts/main-layout/main-layout.component.ts
var _c0 = (a0, a1) => ({ "translate-x-0": a0, "-translate-x-full": a1, "md:translate-x-0": true });
function MainLayoutComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 32)(1, "div", 33)(2, "span", 34);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 35)(5, "div", 36);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(7, "div", 37)(8, "a", 38);
    \u0275\u0275text(9, "Profile");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "a", 38);
    \u0275\u0275text(11, "Settings");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "button", 39);
    \u0275\u0275listener("click", function MainLayoutComponent_div_10_Template_button_click_12_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.logout());
    });
    \u0275\u0275text(13, " Sign out ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const user_r3 = ctx.ngIf;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2("", user_r3.firstName, " ", user_r3.lastName, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" ", user_r3.firstName.charAt(0), "", user_r3.lastName.charAt(0), " ");
  }
}
function MainLayoutComponent_a_40_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "a", 40);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 15);
    \u0275\u0275element(2, "path", 41);
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " User Management ");
    \u0275\u0275elementEnd();
  }
}
var MainLayoutComponent = class _MainLayoutComponent {
  router;
  authService;
  isSidebarOpen = false;
  currentUser$;
  constructor(router, authService) {
    this.router = router;
    this.authService = authService;
    this.currentUser$ = this.authService.currentUser$;
  }
  ngOnInit() {
    this.isSidebarOpen = window.innerWidth >= 768;
  }
  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }
  logout() {
    this.authService.logout();
    this.router.navigate(["/login"]);
  }
  isAdmin() {
    return this.authService.hasRole([UserRole.Admin]);
  }
  static \u0275fac = function MainLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MainLayoutComponent)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MainLayoutComponent, selectors: [["app-main-layout"]], decls: 48, vars: 8, consts: [[1, "min-h-screen", "bg-gray-50"], [1, "fixed", "top-0", "left-0", "right-0", "z-50", "bg-white", "shadow-md"], [1, "flex", "items-center", "justify-between", "px-4", "py-3"], [1, "text-gray-600", "focus:outline-none", "focus:text-primary-600", "md:hidden", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 6h16M4 12h16M4 18h16"], [1, "flex", "items-center"], ["src", "assets/images/logo.png", "alt", "Clinic Logo", 1, "h-8", "w-auto", "mr-2"], [1, "text-xl", "font-semibold", "text-primary-700"], ["class", "relative", 4, "ngIf"], [1, "flex", "pt-16"], [1, "fixed", "inset-y-0", "left-0", "z-40", "w-64", "mt-16", "transform", "bg-white", "border-r", "border-gray-200", "transition", "duration-300", "ease-in-out", 3, "ngClass"], [1, "flex", "flex-col", "h-full", "py-4"], [1, "flex-1", "space-y-1", "px-2"], ["routerLink", "/dashboard", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "mr-4", "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"], ["routerLink", "/patients", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"], ["routerLink", "/doctors", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"], ["routerLink", "/appointments", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"], ["routerLink", "/medical-records", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"], ["routerLink", "/schedules", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"], ["routerLink", "/user-management", "routerLinkActive", "bg-primary-50 text-primary-700", "class", "group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700", 4, "ngIf"], [1, "px-4", "mt-6"], [1, "w-full", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "text-red-600", "rounded-md", "hover:bg-red-50", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"], [1, "flex-1", "md:ml-64", "p-6", "pt-20"], [1, "relative"], [1, "flex", "items-center", "space-x-4"], [1, "hidden", "md:block", "text-sm", "text-gray-700"], [1, "flex", "items-center", "focus:outline-none"], [1, "w-8", "h-8", "rounded-full", "bg-primary-500", "flex", "items-center", "justify-center", "text-white"], [1, "absolute", "right-0", "mt-2", "w-48", "bg-white", "rounded-md", "shadow-lg", "py-1", "z-50"], ["href", "#", 1, "block", "px-4", "py-2", "text-sm", "text-gray-700", "hover:bg-gray-100"], [1, "block", "w-full", "text-left", "px-4", "py-2", "text-sm", "text-gray-700", "hover:bg-gray-100", 3, "click"], ["routerLink", "/user-management", "routerLinkActive", "bg-primary-50 text-primary-700", 1, "group", "flex", "items-center", "px-2", "py-2", "text-base", "font-medium", "rounded-md", "hover:bg-primary-50", "hover:text-primary-700"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"]], template: function MainLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "button", 3);
      \u0275\u0275listener("click", function MainLayoutComponent_Template_button_click_3_listener() {
        return ctx.toggleSidebar();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 4);
      \u0275\u0275element(5, "path", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "div", 6);
      \u0275\u0275element(7, "img", 7);
      \u0275\u0275elementStart(8, "span", 8);
      \u0275\u0275text(9, "Clinic Management");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(10, MainLayoutComponent_div_10_Template, 14, 4, "div", 9);
      \u0275\u0275pipe(11, "async");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "div", 10)(13, "aside", 11)(14, "nav", 12)(15, "div", 13)(16, "a", 14);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(17, "svg", 15);
      \u0275\u0275element(18, "path", 16);
      \u0275\u0275elementEnd();
      \u0275\u0275text(19, " Dashboard ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(20, "a", 17);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(21, "svg", 15);
      \u0275\u0275element(22, "path", 18);
      \u0275\u0275elementEnd();
      \u0275\u0275text(23, " Patients ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(24, "a", 19);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(25, "svg", 15);
      \u0275\u0275element(26, "path", 20);
      \u0275\u0275elementEnd();
      \u0275\u0275text(27, " Doctors ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(28, "a", 21);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(29, "svg", 15);
      \u0275\u0275element(30, "path", 22);
      \u0275\u0275elementEnd();
      \u0275\u0275text(31, " Appointments ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(32, "a", 23);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(33, "svg", 15);
      \u0275\u0275element(34, "path", 24);
      \u0275\u0275elementEnd();
      \u0275\u0275text(35, " Medical Records ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(36, "a", 25);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(37, "svg", 15);
      \u0275\u0275element(38, "path", 26);
      \u0275\u0275elementEnd();
      \u0275\u0275text(39, " Schedules ");
      \u0275\u0275elementEnd();
      \u0275\u0275template(40, MainLayoutComponent_a_40_Template, 4, 0, "a", 27);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(41, "div", 28)(42, "button", 29);
      \u0275\u0275listener("click", function MainLayoutComponent_Template_button_click_42_listener() {
        return ctx.logout();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(43, "svg", 15);
      \u0275\u0275element(44, "path", 30);
      \u0275\u0275elementEnd();
      \u0275\u0275text(45, " Sign out ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(46, "main", 31);
      \u0275\u0275element(47, "router-outlet");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", \u0275\u0275pipeBind1(11, 3, ctx.currentUser$));
      \u0275\u0275advance(3);
      \u0275\u0275property("ngClass", \u0275\u0275pureFunction2(5, _c0, ctx.isSidebarOpen, !ctx.isSidebarOpen));
      \u0275\u0275advance(27);
      \u0275\u0275property("ngIf", ctx.isAdmin());
    }
  }, dependencies: [CommonModule, NgClass, NgIf, AsyncPipe, RouterModule, RouterOutlet, RouterLink, RouterLinkActive], styles: ["\n\n.user-dropdown[_ngcontent-%COMP%] {\n  display: none;\n  position: absolute;\n  right: 0;\n  top: 100%;\n  z-index: 10;\n}\n.user-dropdown-toggle[_ngcontent-%COMP%]:hover    + .user-dropdown[_ngcontent-%COMP%], \n.user-dropdown[_ngcontent-%COMP%]:hover {\n  display: block;\n}\n/*# sourceMappingURL=main-layout.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MainLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-main-layout", standalone: true, imports: [CommonModule, RouterModule], template: `<div class="min-h-screen bg-gray-50">\r
  <!-- Mobile menu button -->\r
  <div class="fixed top-0 left-0 right-0 z-50 bg-white shadow-md">\r
    <div class="flex items-center justify-between px-4 py-3">\r
      <button \r
        (click)="toggleSidebar()" \r
        class="text-gray-600 focus:outline-none focus:text-primary-600 md:hidden"\r
      >\r
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />\r
        </svg>\r
      </button>\r
      \r
      <div class="flex items-center">\r
        <img src="assets/images/logo.png" alt="Clinic Logo" class="h-8 w-auto mr-2">\r
        <span class="text-xl font-semibold text-primary-700">Clinic Management</span>\r
      </div>\r
      \r
      <!-- User dropdown -->\r
      <div class="relative" *ngIf="currentUser$ | async as user">\r
        <div class="flex items-center space-x-4">\r
          <span class="hidden md:block text-sm text-gray-700">{{ user.firstName }} {{ user.lastName }}</span>\r
          <button class="flex items-center focus:outline-none">\r
            <div class="w-8 h-8 rounded-full bg-primary-500 flex items-center justify-center text-white">\r
              {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}\r
            </div>\r
          </button>\r
        </div>\r
        \r
        <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">\r
          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>\r
          <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>\r
          <button (click)="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">\r
            Sign out\r
          </button>\r
        </div>\r
      </div>\r
    </div>\r
  </div>\r
\r
  <!-- Sidebar -->\r
  <div class="flex pt-16">\r
    <aside \r
      class="fixed inset-y-0 left-0 z-40 w-64 mt-16 transform bg-white border-r border-gray-200 transition duration-300 ease-in-out"\r
      [ngClass]="{'translate-x-0': isSidebarOpen, '-translate-x-full': !isSidebarOpen, 'md:translate-x-0': true}"\r
    >\r
      <nav class="flex flex-col h-full py-4">\r
        <div class="flex-1 space-y-1 px-2">\r
          <a routerLink="/dashboard" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />\r
            </svg>\r
            Dashboard\r
          </a>\r
          \r
          <a routerLink="/patients" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />\r
            </svg>\r
            Patients\r
          </a>\r
          \r
          <a routerLink="/doctors" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />\r
            </svg>\r
            Doctors\r
          </a>\r
          \r
          <a routerLink="/appointments" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />\r
            </svg>\r
            Appointments\r
          </a>\r
          \r
          <a routerLink="/medical-records" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />\r
            </svg>\r
            Medical Records\r
          </a>\r
          \r
          <a routerLink="/schedules" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />\r
            </svg>\r
            Schedules\r
          </a>\r
\r
          <!-- Admin Only - User Management -->\r
          <a *ngIf="isAdmin()" routerLink="/user-management" routerLinkActive="bg-primary-50 text-primary-700" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-primary-50 hover:text-primary-700">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />\r
            </svg>\r
            User Management\r
          </a>\r
        </div>\r
        \r
        <div class="px-4 mt-6">\r
          <button (click)="logout()" class="w-full flex items-center px-2 py-2 text-base font-medium text-red-600 rounded-md hover:bg-red-50">\r
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-4 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />\r
            </svg>\r
            Sign out\r
          </button>\r
        </div>\r
      </nav>\r
    </aside>\r
\r
    <!-- Main content -->\r
    <main class="flex-1 md:ml-64 p-6 pt-20">\r
      <router-outlet></router-outlet>\r
    </main>\r
  </div>\r
</div> `, styles: ["/* src/app/layouts/main-layout/main-layout.component.scss */\n.user-dropdown {\n  display: none;\n  position: absolute;\n  right: 0;\n  top: 100%;\n  z-index: 10;\n}\n.user-dropdown-toggle:hover + .user-dropdown,\n.user-dropdown:hover {\n  display: block;\n}\n/*# sourceMappingURL=main-layout.component.css.map */\n"] }]
  }], () => [{ type: Router }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MainLayoutComponent, { className: "MainLayoutComponent", filePath: "src/app/layouts/main-layout/main-layout.component.ts", lineNumber: 16 });
})();
export {
  MainLayoutComponent
};
//# sourceMappingURL=chunk-VWFESNYS.js.map
