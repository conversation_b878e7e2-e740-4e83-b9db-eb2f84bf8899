{"version": 3, "sources": ["src/app/features/doctors/doctor-list/doctor-list.component.ts", "src/app/features/doctors/doctor-list/doctor-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DoctorService } from '../services/doctor.service';\r\nimport { Doctor } from '../models/doctor.model';\r\nimport { PaginatedResponse } from '../../../core/models/api-response.model';\r\nimport { debounceTime, distinctUntilChanged, Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-doctor-list',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, FormsModule],\r\n  templateUrl: './doctor-list.component.html',\r\n  styleUrls: ['./doctor-list.component.scss']\r\n})\r\nexport class DoctorListComponent implements OnInit {\r\n  doctors: Doctor[] = [];\r\n  loading = false;\r\n  currentPage = 1;\r\n  pageSize = 10;\r\n  totalItems = 0;\r\n  searchTerm = '';\r\n  searchTermChanged = new Subject<string>();\r\n  Math = Math; // Make Math available in the template\r\n\r\n  constructor(\r\n    private doctorService: DoctorService,\r\n    public router: Router\r\n  ) {\r\n    this.searchTermChanged\r\n      .pipe(\r\n        debounceTime(300),\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe(term => {\r\n        this.searchTerm = term;\r\n        this.currentPage = 1;\r\n        this.loadDoctors();\r\n      });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadDoctors();\r\n  }\r\n\r\n  loadDoctors(): void {\r\n    this.loading = true;\r\n    this.doctorService.getPaginatedDoctors(this.currentPage, this.pageSize, this.searchTerm)\r\n      .subscribe({\r\n        next: (response: PaginatedResponse<Doctor>) => {\r\n          this.doctors = response.items;\r\n          this.totalItems = response.totalCount;\r\n          this.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading doctors:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  onSearch(searchTerm: string): void {\r\n    this.searchTermChanged.next(searchTerm);\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.loadDoctors();\r\n  }\r\n\r\n  onDelete(id: string): void {\r\n    if (confirm('Are you sure you want to delete this doctor?')) {\r\n      this.loading = true;\r\n      this.doctorService.delete(id).subscribe({\r\n        next: () => {\r\n          this.loadDoctors();\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error deleting doctor:', error);\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto px-4 py-6\">\r\n  <div class=\"flex justify-between items-center mb-6\">\r\n    <h1 class=\"text-3xl font-bold text-gray-900\">Doctors</h1>\r\n    <a routerLink=\"/doctors/add\" class=\"bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md flex items-center\">\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n        <path fill-rule=\"evenodd\" d=\"M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z\" clip-rule=\"evenodd\" />\r\n      </svg>\r\n      Add Doctor\r\n    </a>\r\n  </div>\r\n\r\n  <div class=\"bg-white rounded-lg shadow mb-6\">\r\n    <div class=\"p-4 border-b border-gray-200\">\r\n      <div class=\"flex flex-col md:flex-row md:items-center md:justify-between\">\r\n        <div class=\"flex-1 mb-4 md:mb-0\">\r\n          <div class=\"relative\">\r\n            <input \r\n              type=\"text\" \r\n              placeholder=\"Search doctors...\" \r\n              class=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\r\n              [ngModel]=\"searchTerm\"\r\n              (ngModelChange)=\"onSearch($event)\"\r\n            >\r\n            <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-gray-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center p-8\">\r\n      <div class=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500\"></div>\r\n    </div>\r\n\r\n    <div *ngIf=\"!loading && doctors.length === 0\" class=\"p-8 text-center text-gray-500\">\r\n      No doctors found. Please try a different search term or add a new doctor.\r\n    </div>\r\n\r\n    <div *ngIf=\"!loading && doctors.length > 0\" class=\"overflow-x-auto\">\r\n      <table class=\"min-w-full divide-y divide-gray-200\">\r\n        <thead class=\"bg-gray-50\">\r\n          <tr>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Specialization</th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Email</th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Phone</th>\r\n            <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody class=\"bg-white divide-y divide-gray-200\">\r\n          <tr *ngFor=\"let doctor of doctors\">\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"flex items-center\">\r\n                <div class=\"ml-4\">\r\n                  <div class=\"text-sm font-medium text-gray-900\">{{ doctor.firstName }} {{ doctor.lastName }}</div>\r\n                </div>\r\n              </div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ doctor.specialization }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ doctor.email }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap\">\r\n              <div class=\"text-sm text-gray-900\">{{ doctor.phoneNumber }}</div>\r\n            </td>\r\n            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n              <div class=\"flex space-x-2\">\r\n                <a [routerLink]=\"['/doctors', doctor.id]\" class=\"text-primary-600 hover:text-primary-900\">View</a>\r\n                <a [routerLink]=\"['/doctors/edit', doctor.id]\" class=\"text-indigo-600 hover:text-indigo-900\">Edit</a>\r\n                <button (click)=\"onDelete(doctor.id)\" class=\"text-red-600 hover:text-red-900\">Delete</button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n\r\n    <!-- Pagination -->\r\n    <div *ngIf=\"totalItems > pageSize\" class=\"px-6 py-4 border-t border-gray-200\">\r\n      <div class=\"flex justify-between items-center\">\r\n        <div class=\"text-sm text-gray-700\">\r\n          Showing <span class=\"font-medium\">{{ (currentPage - 1) * pageSize + 1 }}</span> to \r\n          <span class=\"font-medium\">{{ Math.min(currentPage * pageSize, totalItems) }}</span> of \r\n          <span class=\"font-medium\">{{ totalItems }}</span> results\r\n        </div>\r\n        <div class=\"flex space-x-2\">\r\n          <button \r\n            [disabled]=\"currentPage === 1\"\r\n            (click)=\"onPageChange(currentPage - 1)\"\r\n            class=\"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Previous\r\n          </button>\r\n          <button \r\n            [disabled]=\"currentPage * pageSize >= totalItems\"\r\n            (click)=\"onPageChange(currentPage + 1)\"\r\n            class=\"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            Next\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiCI,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,6EAAA;AACF,IAAA,uBAAA;;;;;;AAcM,IAAA,yBAAA,GAAA,IAAA,EAAmC,GAAA,MAAA,EAAA,EACO,GAAA,OAAA,EAAA,EACP,GAAA,OAAA,EAAA,EACX,GAAA,OAAA,EAAA;AAC+B,IAAA,iBAAA,CAAA;AAA4C,IAAA,uBAAA,EAAM,EAC7F,EACF;AAER,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAwC,GAAA,OAAA,EAAA;AACH,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA,EAAM;AAEtE,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAwC,IAAA,OAAA,EAAA;AACH,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAM;AAE7D,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAwC,IAAA,OAAA,EAAA;AACH,IAAA,iBAAA,EAAA;AAAwB,IAAA,uBAAA,EAAM;AAEnE,IAAA,yBAAA,IAAA,MAAA,EAAA,EAA4D,IAAA,OAAA,EAAA,EAC9B,IAAA,KAAA,EAAA;AACgE,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAC9F,IAAA,yBAAA,IAAA,KAAA,EAAA;AAA6F,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AACjG,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAQ,IAAA,qBAAA,SAAA,SAAA,qEAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,UAAA,EAAA,CAAmB;IAAA,CAAA;AAA0C,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA,EAAS,EACzF,EACH;;;;AAnBgD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,UAAA,WAAA,KAAA,UAAA,UAAA,EAAA;AAKhB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,cAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,KAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,WAAA;AAI9B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,GAAA,KAAA,UAAA,EAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,GAAA,KAAA,UAAA,EAAA,CAAA;;;;;AAhCf,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoE,GAAA,SAAA,EAAA,EACf,GAAA,SAAA,EAAA,EACvB,GAAA,IAAA,EACpB,GAAA,MAAA,EAAA;AACqG,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;AAC3G,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA;AACrH,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AAC5G,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAC5G,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuG,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAK,EAChH;AAEP,IAAA,yBAAA,IAAA,SAAA,EAAA;AACE,IAAA,qBAAA,IAAA,2CAAA,IAAA,IAAA,MAAA,EAAA;AAyBF,IAAA,uBAAA,EAAQ,EACF;;;;AA1BmB,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,OAAA;;;;;;AA8B7B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8E,GAAA,OAAA,EAAA,EAC7B,GAAA,OAAA,EAAA;AAE3C,IAAA,iBAAA,GAAA,WAAA;AAAQ,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAsC,IAAA,uBAAA;AAAQ,IAAA,iBAAA,GAAA,MAAA;AAChF,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAkD,IAAA,uBAAA;AAAQ,IAAA,iBAAA,GAAA,MAAA;AACpF,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,EAAA;AAAgB,IAAA,uBAAA;AAAQ,IAAA,iBAAA,IAAA,WAAA;AACpD,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,UAAA,EAAA;AAGxB,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,OAAA,cAA2B,CAAC,CAAC;IAAA,CAAA;AAGtC,IAAA,iBAAA,IAAA,YAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,OAAA,cAA2B,CAAC,CAAC;IAAA,CAAA;AAGtC,IAAA,iBAAA,IAAA,QAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACF;;;;AApBgC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,OAAA,cAAA,KAAA,OAAA,WAAA,CAAA;AACR,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,IAAA,OAAA,cAAA,OAAA,UAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,UAAA;AAIxB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,gBAAA,CAAA;AAOA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,cAAA,OAAA,YAAA,OAAA,UAAA;;;ADnFN,IAAO,sBAAP,MAAO,qBAAmB;EAWpB;EACD;EAXT,UAAoB,CAAA;EACpB,UAAU;EACV,cAAc;EACd,WAAW;EACX,aAAa;EACb,aAAa;EACb,oBAAoB,IAAI,QAAO;EAC/B,OAAO;;EAEP,YACU,eACD,QAAc;AADb,SAAA,gBAAA;AACD,SAAA,SAAA;AAEP,SAAK,kBACF,KACC,aAAa,GAAG,GAChB,qBAAoB,CAAE,EAEvB,UAAU,UAAO;AAChB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,YAAW;IAClB,CAAC;EACL;EAEA,WAAQ;AACN,SAAK,YAAW;EAClB;EAEA,cAAW;AACT,SAAK,UAAU;AACf,SAAK,cAAc,oBAAoB,KAAK,aAAa,KAAK,UAAU,KAAK,UAAU,EACpF,UAAU;MACT,MAAM,CAAC,aAAuC;AAC5C,aAAK,UAAU,SAAS;AACxB,aAAK,aAAa,SAAS;AAC3B,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAc;AACpB,gBAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAK,UAAU;MACjB;KACD;EACL;EAEA,SAAS,YAAkB;AACzB,SAAK,kBAAkB,KAAK,UAAU;EACxC;EAEA,aAAa,MAAY;AACvB,SAAK,cAAc;AACnB,SAAK,YAAW;EAClB;EAEA,SAAS,IAAU;AACjB,QAAI,QAAQ,8CAA8C,GAAG;AAC3D,WAAK,UAAU;AACf,WAAK,cAAc,OAAO,EAAE,EAAE,UAAU;QACtC,MAAM,MAAK;AACT,eAAK,YAAW;QAClB;QACA,OAAO,CAAC,UAAc;AACpB,kBAAQ,MAAM,0BAA0B,KAAK;AAC7C,eAAK,UAAU;QACjB;OACD;IACH;EACF;;qCApEW,sBAAmB,4BAAA,aAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,GAAA,QAAA,mBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,YAAA,aAAA,eAAA,GAAA,CAAA,cAAA,gBAAA,GAAA,kBAAA,wBAAA,cAAA,eAAA,QAAA,QAAA,cAAA,QAAA,cAAA,GAAA,CAAA,SAAA,8BAAA,WAAA,aAAA,QAAA,gBAAA,GAAA,OAAA,OAAA,MAAA,GAAA,CAAA,aAAA,WAAA,KAAA,yFAAA,aAAA,SAAA,GAAA,CAAA,GAAA,YAAA,cAAA,UAAA,MAAA,GAAA,CAAA,GAAA,OAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,YAAA,eAAA,mBAAA,oBAAA,GAAA,CAAA,GAAA,UAAA,QAAA,SAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,QAAA,QAAA,eAAA,qBAAA,GAAA,UAAA,SAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,sBAAA,gBAAA,0BAAA,4BAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,YAAA,aAAA,UAAA,QAAA,QAAA,gBAAA,qBAAA,GAAA,CAAA,SAAA,8BAAA,WAAA,aAAA,QAAA,gBAAA,GAAA,OAAA,OAAA,eAAA,GAAA,CAAA,aAAA,WAAA,KAAA,oHAAA,aAAA,SAAA,GAAA,CAAA,SAAA,wCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,sCAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,KAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,cAAA,oBAAA,GAAA,CAAA,GAAA,OAAA,eAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,QAAA,QAAA,aAAA,WAAA,eAAA,iBAAA,aAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,QAAA,QAAA,mBAAA,GAAA,CAAA,GAAA,QAAA,cAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,eAAA,eAAA,GAAA,CAAA,GAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,QAAA,qBAAA,WAAA,aAAA,GAAA,CAAA,GAAA,QAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,0BAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,yBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,sBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,QAAA,QAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,QAAA,mBAAA,cAAA,GAAA,CAAA,GAAA,WAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,QAAA,QAAA,UAAA,mBAAA,cAAA,WAAA,eAAA,iBAAA,YAAA,oBAAA,uBAAA,+BAAA,GAAA,SAAA,UAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AChBhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyC,GAAA,OAAA,CAAA,EACa,GAAA,MAAA,CAAA;AACL,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AACpD,MAAA,yBAAA,GAAA,KAAA,CAAA;;AACE,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;AACA,MAAA,iBAAA,GAAA,cAAA;AACF,MAAA,uBAAA,EAAI;;AAGN,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6C,GAAA,OAAA,CAAA,EACD,IAAA,OAAA,CAAA,EACkC,IAAA,OAAA,CAAA,EACvC,IAAA,OAAA,EAAA,EACT,IAAA,SAAA,EAAA;AAMlB,MAAA,qBAAA,iBAAA,SAAA,6DAAA,QAAA;AAAA,eAAiB,IAAA,SAAA,MAAA;MAAgB,CAAA;AALnC,MAAA,uBAAA;AAOA,MAAA,yBAAA,IAAA,OAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA,EAAM,EACF,EACF,EACF,EACF;AAGR,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAAkE,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAIkB,IAAA,qCAAA,IAAA,GAAA,OAAA,EAAA,EAIhB,IAAA,qCAAA,IAAA,GAAA,OAAA,EAAA;AAmEtE,MAAA,uBAAA,EAAM;;;AAxFM,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,UAAA;AAaJ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,WAAA,CAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,SAAA,CAAA;AA0CA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,IAAA,QAAA;;oBDvEE,cAAY,SAAA,MAAE,cAAY,YAAE,aAAW,sBAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,wTAAA,EAAA,CAAA;;;sEAItC,qBAAmB,CAAA;UAP/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,yTAAA,EAAA,CAAA;;;;6EAIvC,qBAAmB,EAAA,WAAA,uBAAA,UAAA,iEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}