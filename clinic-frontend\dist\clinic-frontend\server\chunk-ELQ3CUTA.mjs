import './polyfills.server.mjs';
import {
  AppointmentService
} from "./chunk-COTX6EKK.mjs";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-YKEX2NSQ.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵpureFunction3,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/appointments/appointment-detail/appointment-detail.component.ts
var _c0 = (a0) => [a0, "edit"];
var _c1 = (a0, a1, a2) => ({ "bg-green-100 text-green-800": a0, "bg-yellow-100 text-yellow-800": a1, "bg-red-100 text-red-800": a2 });
function AppointmentDetailComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "div", 5);
    \u0275\u0275elementEnd();
  }
}
function AppointmentDetailComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 6)(1, "strong", 7);
    \u0275\u0275text(2, "Error!");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 8);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
function AppointmentDetailComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 9)(1, "div", 10)(2, "div", 11)(3, "div")(4, "h3", 12);
    \u0275\u0275text(5, "Appointment Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p", 13);
    \u0275\u0275text(7, "Appointment details and status.");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 14)(9, "button", 15);
    \u0275\u0275text(10, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "button", 16);
    \u0275\u0275listener("click", function AppointmentDetailComponent_div_3_Template_button_click_11_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onDelete());
    });
    \u0275\u0275text(12, " Delete ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(13, "div", 17)(14, "dl")(15, "div", 18)(16, "dt", 19);
    \u0275\u0275text(17, "Patient");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "dd", 20);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "div", 21)(21, "dt", 19);
    \u0275\u0275text(22, "Doctor");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "dd", 20);
    \u0275\u0275text(24);
    \u0275\u0275elementStart(25, "span", 22);
    \u0275\u0275text(26);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(27, "div", 18)(28, "dt", 19);
    \u0275\u0275text(29, "Date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "dd", 20);
    \u0275\u0275text(31);
    \u0275\u0275pipe(32, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(33, "div", 21)(34, "dt", 19);
    \u0275\u0275text(35, "Time");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "dd", 20);
    \u0275\u0275text(37);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(38, "div", 18)(39, "dt", 19);
    \u0275\u0275text(40, "Status");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "dd", 23)(42, "span", 24);
    \u0275\u0275text(43);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(44, "div", 21)(45, "dt", 19);
    \u0275\u0275text(46, "Notes");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(47, "dd", 20);
    \u0275\u0275text(48);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(49, "div", 18)(50, "dt", 19);
    \u0275\u0275text(51, "Created at");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(52, "dd", 20);
    \u0275\u0275text(53);
    \u0275\u0275pipe(54, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(55, "div", 21)(56, "dt", 19);
    \u0275\u0275text(57, "Last updated");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(58, "dd", 20);
    \u0275\u0275text(59);
    \u0275\u0275pipe(60, "date");
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(9);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(23, _c0, ctx_r0.appointment.id));
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate2(" ", ctx_r0.appointment.patient == null ? null : ctx_r0.appointment.patient.firstName, " ", ctx_r0.appointment.patient == null ? null : ctx_r0.appointment.patient.lastName, " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate2(" Dr. ", ctx_r0.appointment.doctor == null ? null : ctx_r0.appointment.doctor.firstName, " ", ctx_r0.appointment.doctor == null ? null : ctx_r0.appointment.doctor.lastName, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("(", ctx_r0.appointment.doctor == null ? null : ctx_r0.appointment.doctor.specialization, ")");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(32, 14, ctx_r0.appointment.date, "mediumDate"), " ");
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate2(" ", ctx_r0.appointment.startTime, " - ", ctx_r0.appointment.endTime, " ");
    \u0275\u0275advance(5);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction3(25, _c1, ctx_r0.appointment.status === "Completed", ctx_r0.appointment.status === "Scheduled", ctx_r0.appointment.status === "Cancelled" || ctx_r0.appointment.status === "NoShow"));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.appointment.status, " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.appointment.notes || "No notes available", " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(54, 17, ctx_r0.appointment.createdAt, "medium"), " ");
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(60, 20, ctx_r0.appointment.updatedAt, "medium"), " ");
  }
}
var AppointmentDetailComponent = class _AppointmentDetailComponent {
  appointmentService;
  route;
  router;
  appointment = null;
  loading = true;
  error = null;
  constructor(appointmentService, route, router) {
    this.appointmentService = appointmentService;
    this.route = route;
    this.router = router;
  }
  ngOnInit() {
    const appointmentId = this.route.snapshot.paramMap.get("id");
    if (appointmentId) {
      this.loadAppointment(appointmentId);
    } else {
      this.error = "Appointment ID not provided";
      this.loading = false;
    }
  }
  loadAppointment(id) {
    this.appointmentService.getAppointment(id).subscribe({
      next: (appointment) => {
        this.appointment = appointment;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading appointment:", error);
        this.error = "Failed to load appointment details";
        this.loading = false;
      }
    });
  }
  onDelete() {
    if (this.appointment && confirm("Are you sure you want to delete this appointment?")) {
      this.loading = true;
      this.appointmentService.deleteAppointment(this.appointment.id).subscribe({
        next: () => {
          this.router.navigate(["/appointments"]);
        },
        error: (error) => {
          console.error("Error deleting appointment:", error);
          this.error = "Failed to delete appointment";
          this.loading = false;
        }
      });
    }
  }
  static \u0275fac = function AppointmentDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppointmentDetailComponent)(\u0275\u0275directiveInject(AppointmentService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppointmentDetailComponent, selectors: [["app-appointment-detail"]], decls: 4, vars: 3, consts: [[1, "container", "mx-auto", "px-4", "py-8"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative", "role", "alert", 4, "ngIf"], ["class", "max-w-4xl mx-auto", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-indigo-600"], ["role", "alert", 1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "relative"], [1, "font-bold"], [1, "block", "sm:inline"], [1, "max-w-4xl", "mx-auto"], [1, "bg-white", "shadow", "overflow-hidden", "sm:rounded-lg"], [1, "px-4", "py-5", "sm:px-6", "flex", "justify-between", "items-center"], [1, "text-lg", "leading-6", "font-medium", "text-gray-900"], [1, "mt-1", "max-w-2xl", "text-sm", "text-gray-500"], [1, "flex", "space-x-4"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-indigo-600", "hover:bg-indigo-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-indigo-500", 3, "routerLink"], [1, "inline-flex", "items-center", "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-red-600", "hover:bg-red-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-red-500", 3, "click"], [1, "border-t", "border-gray-200"], [1, "bg-gray-50", "px-4", "py-5", "sm:grid", "sm:grid-cols-3", "sm:gap-4", "sm:px-6"], [1, "text-sm", "font-medium", "text-gray-500"], [1, "mt-1", "text-sm", "text-gray-900", "sm:mt-0", "sm:col-span-2"], [1, "bg-white", "px-4", "py-5", "sm:grid", "sm:grid-cols-3", "sm:gap-4", "sm:px-6"], [1, "text-gray-500"], [1, "mt-1", "text-sm", "sm:mt-0", "sm:col-span-2"], [1, "px-2", "inline-flex", "text-xs", "leading-5", "font-semibold", "rounded-full", 3, "ngClass"]], template: function AppointmentDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, AppointmentDetailComponent_div_1_Template, 2, 0, "div", 1)(2, AppointmentDetailComponent_div_2_Template, 5, 1, "div", 2)(3, AppointmentDetailComponent_div_3_Template, 61, 29, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.appointment && !ctx.loading);
    }
  }, dependencies: [CommonModule, NgClass, NgIf, DatePipe, RouterModule, RouterLink], styles: ["\n\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n/*# sourceMappingURL=appointment-detail.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppointmentDetailComponent, [{
    type: Component,
    args: [{ selector: "app-appointment-detail", standalone: true, imports: [CommonModule, RouterModule], template: `<div class="container mx-auto px-4 py-8">\r
  <div *ngIf="loading" class="flex justify-center items-center h-64">\r
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>\r
  </div>\r
\r
  <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">\r
    <strong class="font-bold">Error!</strong>\r
    <span class="block sm:inline">{{ error }}</span>\r
  </div>\r
\r
  <div *ngIf="appointment && !loading" class="max-w-4xl mx-auto">\r
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">\r
      <div class="px-4 py-5 sm:px-6 flex justify-between items-center">\r
        <div>\r
          <h3 class="text-lg leading-6 font-medium text-gray-900">Appointment Information</h3>\r
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Appointment details and status.</p>\r
        </div>\r
        <div class="flex space-x-4">\r
          <button\r
            [routerLink]="[appointment.id, 'edit']"\r
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"\r
          >\r
            Edit\r
          </button>\r
          <button\r
            (click)="onDelete()"\r
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"\r
          >\r
            Delete\r
          </button>\r
        </div>\r
      </div>\r
      <div class="border-t border-gray-200">\r
        <dl>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Patient</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }}\r
            </dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Doctor</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              Dr. {{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }}\r
              <span class="text-gray-500">({{ appointment.doctor?.specialization }})</span>\r
            </dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Date</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.date | date:'mediumDate' }}\r
            </dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Time</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.startTime }} - {{ appointment.endTime }}\r
            </dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Status</dt>\r
            <dd class="mt-1 text-sm sm:mt-0 sm:col-span-2">\r
              <span\r
                [ngClass]="{\r
                  'bg-green-100 text-green-800': appointment.status === 'Completed',\r
                  'bg-yellow-100 text-yellow-800': appointment.status === 'Scheduled',\r
                  'bg-red-100 text-red-800': appointment.status === 'Cancelled' || appointment.status === 'NoShow'\r
                }"\r
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"\r
              >\r
                {{ appointment.status }}\r
              </span>\r
            </dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Notes</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.notes || 'No notes available' }}\r
            </dd>\r
          </div>\r
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Created at</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.createdAt | date:'medium' }}\r
            </dd>\r
          </div>\r
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">\r
            <dt class="text-sm font-medium text-gray-500">Last updated</dt>\r
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">\r
              {{ appointment.updatedAt | date:'medium' }}\r
            </dd>\r
          </div>\r
        </dl>\r
      </div>\r
    </div>\r
  </div>\r
</div> `, styles: ["/* src/app/features/appointments/appointment-detail/appointment-detail.component.scss */\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n/*# sourceMappingURL=appointment-detail.component.css.map */\n"] }]
  }], () => [{ type: AppointmentService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppointmentDetailComponent, { className: "AppointmentDetailComponent", filePath: "src/app/features/appointments/appointment-detail/appointment-detail.component.ts", lineNumber: 14 });
})();
export {
  AppointmentDetailComponent
};
//# sourceMappingURL=chunk-ELQ3CUTA.mjs.map
