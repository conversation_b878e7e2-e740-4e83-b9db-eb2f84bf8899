import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/doctors/doctors.routes.ts
var DOCTORS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-EHBYXIUL.mjs").then((m) => m.DoctorListComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-list/doctor-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-JXVUHO6V.mjs").then((m) => m.DoctorFormComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-JXVUHO6V.mjs").then((m) => m.DoctorFormComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-form/doctor-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-NBOGXEIY.mjs").then((m) => m.DoctorDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/doctors/doctor-detail/doctor-detail.component.ts" } : {})
];
export {
  DOCTORS_ROUTES
};
//# sourceMappingURL=chunk-VGJZTCJC.mjs.map
