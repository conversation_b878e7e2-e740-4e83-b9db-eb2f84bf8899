{"version": 3, "sources": ["src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-medical-record-form',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule],\r\n  template: `\r\n    <div class=\"container mx-auto p-4\">\r\n      <h1 class=\"text-2xl font-bold mb-4\">Medical Record Form</h1>\r\n      <form class=\"space-y-4\">\r\n        <!-- Placeholder for form fields -->\r\n        <div class=\"grid gap-4\">\r\n          <p>Form fields will be added here</p>\r\n        </div>\r\n        <div class=\"flex gap-4\">\r\n          <button type=\"submit\" class=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600\">\r\n            Save\r\n          </button>\r\n          <a routerLink=\"..\" class=\"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600\">\r\n            Cancel\r\n          </a>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  `,\r\n  styles: []\r\n})\r\nexport class MedicalRecordFormComponent implements OnInit {\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {}\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA8BM,IAAO,6BAAP,MAAO,4BAA0B;EACrC,cAAA;EAAe;EAEf,WAAQ;EAAU;;qCAHP,6BAA0B;EAAA;yEAA1B,6BAA0B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,KAAA,GAAA,CAAA,GAAA,YAAA,aAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,QAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,eAAA,cAAA,QAAA,QAAA,WAAA,mBAAA,GAAA,CAAA,cAAA,MAAA,GAAA,eAAA,cAAA,QAAA,QAAA,WAAA,mBAAA,CAAA,GAAA,UAAA,SAAA,oCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApBnC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,MAAA,CAAA;AACG,MAAA,iBAAA,GAAA,qBAAA;AAAmB,MAAA,uBAAA;AACvD,MAAA,yBAAA,GAAA,QAAA,CAAA,EAAwB,GAAA,OAAA,CAAA,EAEE,GAAA,GAAA;AACnB,MAAA,iBAAA,GAAA,gCAAA;AAA8B,MAAA,uBAAA,EAAI;AAEvC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,UAAA,CAAA;AAEpB,MAAA,iBAAA,GAAA,QAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,CAAA;AACE,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA,EAAI,EACA,EACD;;oBAjBD,cAAc,cAAY,YAAE,aAAW,oBAAA,sBAAA,QAAE,mBAAmB,GAAA,eAAA,EAAA,CAAA;;;sEAsB3D,4BAA0B,CAAA;UAzBtC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,cAAc,aAAa,mBAAmB,GAAC,UAC7D;;;;;;;;;;;;;;;;;;IAkBT,CAAA;;;;6EAGU,4BAA0B,EAAA,WAAA,8BAAA,UAAA,oGAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}