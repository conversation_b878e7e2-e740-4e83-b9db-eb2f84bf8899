{"version": 3, "sources": ["src/app/features/doctors/services/doctor.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from '../../../../environments/environment';\r\nimport { Doctor } from '../models/doctor.model';\r\nimport { PaginationQuery, PaginatedResponse } from '../../../core/models/api-response.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class DoctorService {\r\n  private http = inject(HttpClient);\r\n  private apiUrl = `${environment.apiUrl}/doctors`;\r\n\r\n  getDoctors(): Observable<Doctor[]> {\r\n    return this.http.get<Doctor[]>(this.apiUrl);\r\n  }\r\n\r\n  getDoctor(id: string): Observable<Doctor> {\r\n    return this.http.get<Doctor>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  createDoctor(doctor: Omit<Doctor, 'id' | 'createdAt' | 'updatedAt'>): Observable<Doctor> {\r\n    return this.http.post<Doctor>(this.apiUrl, doctor);\r\n  }\r\n\r\n  updateDoctor(id: string, doctor: Partial<Doctor>): Observable<Doctor> {\r\n    return this.http.put<Doctor>(`${this.apiUrl}/${id}`, doctor);\r\n  }\r\n\r\n  deleteDoctor(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  getPaginatedDoctors(pageNumber: number = 1, pageSize: number = 10, searchTerm?: string): Observable<PaginatedResponse<Doctor>> {\r\n    const query: PaginationQuery = {\r\n      pageNumber,\r\n      pageSize,\r\n      searchTerm\r\n    };\r\n    return this.http.get<PaginatedResponse<Doctor>>(`${this.apiUrl}/paginated`, { params: query as any });\r\n  }\r\n\r\n  // Alias for deleteDoctor to maintain compatibility\r\n  delete(id: string): Observable<void> {\r\n    return this.deleteDoctor(id);\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;AAUM,IAAO,gBAAP,MAAO,eAAa;EAChB,OAAO,OAAO,UAAU;EACxB,SAAS,GAAG,YAAY,MAAM;EAEtC,aAAU;AACR,WAAO,KAAK,KAAK,IAAc,KAAK,MAAM;EAC5C;EAEA,UAAU,IAAU;AAClB,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EACrD;EAEA,aAAa,QAAsD;AACjE,WAAO,KAAK,KAAK,KAAa,KAAK,QAAQ,MAAM;EACnD;EAEA,aAAa,IAAY,QAAuB;AAC9C,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,MAAM,IAAI,EAAE,IAAI,MAAM;EAC7D;EAEA,aAAa,IAAU;AACrB,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EACtD;EAEA,oBAAoB,aAAqB,GAAG,WAAmB,IAAI,YAAmB;AACpF,UAAM,QAAyB;MAC7B;MACA;MACA;;AAEF,WAAO,KAAK,KAAK,IAA+B,GAAG,KAAK,MAAM,cAAc,EAAE,QAAQ,MAAY,CAAE;EACtG;;EAGA,OAAO,IAAU;AACf,WAAO,KAAK,aAAa,EAAE;EAC7B;;qCApCW,gBAAa;EAAA;4EAAb,gBAAa,SAAb,eAAa,WAAA,YAFZ,OAAM,CAAA;;;sEAEP,eAAa,CAAA;UAHzB;WAAW;MACV,YAAY;KACb;;;", "names": []}