import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/medical-records/medical-records.routes.ts
var MEDICAL_RECORDS_ROUTES = [
  // Comment out missing component routes
  // {
  //   path: 'list',
  //   loadComponent: () => import('./medical-record-list/medical-record-list.component').then(m => m.MedicalRecordListComponent)
  // },
  // {
  //   path: 'new',
  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  // },
  // {
  //   path: 'edit/:id',
  //   loadComponent: () => import('./medical-record-form/medical-record-form.component').then(m => m.MedicalRecordFormComponent)
  // },
  // {
  //   path: ':id',
  //   loadComponent: () => import('./medical-record-detail/medical-record-detail.component').then(m => m.MedicalRecordDetailComponent)
  // },
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-XSG5HTCQ.js").then((m) => m.MedicalRecordListComponent)
  }, false ? { \u0275entryName: "src/app/features/medical-records/components/medical-record-list/medical-record-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-KLER3UXW.js").then((m) => m.MedicalRecordFormComponent)
  }, false ? { \u0275entryName: "src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-KLER3UXW.js").then((m) => m.MedicalRecordFormComponent)
  }, false ? { \u0275entryName: "src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-DISNHZIJ.js").then((m) => m.MedicalRecordDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/medical-records/components/medical-record-detail/medical-record-detail.component.ts" } : {})
];
export {
  MEDICAL_RECORDS_ROUTES
};
//# sourceMappingURL=chunk-QCDAS7AN.js.map
