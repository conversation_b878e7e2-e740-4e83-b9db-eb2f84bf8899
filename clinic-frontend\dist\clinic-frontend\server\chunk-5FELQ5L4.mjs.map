{"version": 3, "sources": ["src/app/features/appointments/appointment-detail/appointment-detail.component.ts", "src/app/features/appointments/appointment-detail/appointment-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\r\nimport { AppointmentService } from '../services/appointment.service';\r\nimport { Appointment } from '../models/appointment.model';\r\n\r\n@Component({\r\n  selector: 'app-appointment-detail',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './appointment-detail.component.html',\r\n  styleUrls: ['./appointment-detail.component.scss']\r\n})\r\nexport class AppointmentDetailComponent implements OnInit {\r\n  appointment: Appointment | null = null;\r\n  loading = true;\r\n  error: string | null = null;\r\n\r\n  constructor(\r\n    private appointmentService: AppointmentService,\r\n    private route: ActivatedRoute,\r\n    public router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const appointmentId = this.route.snapshot.paramMap.get('id');\r\n    if (appointmentId) {\r\n      this.loadAppointment(appointmentId);\r\n    } else {\r\n      this.error = 'Appointment ID not provided';\r\n      this.loading = false;\r\n    }\r\n  }\r\n\r\n  private loadAppointment(id: string): void {\r\n    this.appointmentService.getAppointment(id).subscribe({\r\n      next: (appointment) => {\r\n        this.appointment = appointment;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading appointment:', error);\r\n        this.error = 'Failed to load appointment details';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  onDelete(): void {\r\n    if (this.appointment && confirm('Are you sure you want to delete this appointment?')) {\r\n      this.loading = true;\r\n      this.appointmentService.deleteAppointment(this.appointment.id).subscribe({\r\n        next: () => {\r\n          this.router.navigate(['/appointments']);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error deleting appointment:', error);\r\n          this.error = 'Failed to delete appointment';\r\n          this.loading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n} ", "<div class=\"container mx-auto px-4 py-8\">\r\n  <div *ngIf=\"loading\" class=\"flex justify-center items-center h-64\">\r\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\r\n  </div>\r\n\r\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\r\n    <strong class=\"font-bold\">Error!</strong>\r\n    <span class=\"block sm:inline\">{{ error }}</span>\r\n  </div>\r\n\r\n  <div *ngIf=\"appointment && !loading\" class=\"max-w-4xl mx-auto\">\r\n    <div class=\"bg-white shadow overflow-hidden sm:rounded-lg\">\r\n      <div class=\"px-4 py-5 sm:px-6 flex justify-between items-center\">\r\n        <div>\r\n          <h3 class=\"text-lg leading-6 font-medium text-gray-900\">Appointment Information</h3>\r\n          <p class=\"mt-1 max-w-2xl text-sm text-gray-500\">Appointment details and status.</p>\r\n        </div>\r\n        <div class=\"flex space-x-4\">\r\n          <button\r\n            [routerLink]=\"[appointment.id, 'edit']\"\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n          >\r\n            Edit\r\n          </button>\r\n          <button\r\n            (click)=\"onDelete()\"\r\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\r\n          >\r\n            Delete\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"border-t border-gray-200\">\r\n        <dl>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Patient</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }}\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Doctor</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              Dr. {{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }}\r\n              <span class=\"text-gray-500\">({{ appointment.doctor?.specialization }})</span>\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Date</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.date | date:'mediumDate' }}\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Time</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.startTime }} - {{ appointment.endTime }}\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Status</dt>\r\n            <dd class=\"mt-1 text-sm sm:mt-0 sm:col-span-2\">\r\n              <span\r\n                [ngClass]=\"{\r\n                  'bg-green-100 text-green-800': appointment.status === 'Completed',\r\n                  'bg-yellow-100 text-yellow-800': appointment.status === 'Scheduled',\r\n                  'bg-red-100 text-red-800': appointment.status === 'Cancelled' || appointment.status === 'NoShow'\r\n                }\"\r\n                class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n              >\r\n                {{ appointment.status }}\r\n              </span>\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Notes</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.notes || 'No notes available' }}\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Created at</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.createdAt | date:'medium' }}\r\n            </dd>\r\n          </div>\r\n          <div class=\"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6\">\r\n            <dt class=\"text-sm font-medium text-gray-500\">Last updated</dt>\r\n            <dd class=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">\r\n              {{ appointment.updatedAt | date:'medium' }}\r\n            </dd>\r\n          </div>\r\n        </dl>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,OAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAiH,GAAA,UAAA,CAAA;AACrF,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AAChC,IAAA,yBAAA,GAAA,QAAA,CAAA;AAA8B,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA,EAAO;;;;AAAlB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;;AAGhC,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA+D,GAAA,OAAA,EAAA,EACF,GAAA,OAAA,EAAA,EACQ,GAAA,KAAA,EAC1D,GAAA,MAAA,EAAA;AACqD,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA;AAC/E,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAgD,IAAA,iBAAA,GAAA,iCAAA;AAA+B,IAAA,uBAAA,EAAI;AAErF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,UAAA,EAAA;AAKxB,IAAA,iBAAA,IAAA,QAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,qBAAA,SAAA,SAAA,qEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AAGnB,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAER,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsC,IAAA,IAAA,EAChC,IAAA,OAAA,EAAA,EACwE,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACrD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAK;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACpD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,EAAA;AAA0C,IAAA,uBAAA,EAAO,EAC1E;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAClD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;;AACF,IAAA,uBAAA,EAAK;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAClD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAK;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACpD,IAAA,yBAAA,IAAA,MAAA,EAAA,EAA+C,IAAA,QAAA,EAAA;AAS3C,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO,EACJ;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAK;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0E,IAAA,MAAA,EAAA;AAC1B,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AACxD,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;;AACF,IAAA,uBAAA,EAAK;AAEP,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwE,IAAA,MAAA,EAAA;AACxB,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,iBAAA,EAAA;;AACF,IAAA,uBAAA,EAAK,EACD,EACH,EACD,EACF;;;;AA3EE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,IAAA,KAAA,OAAA,YAAA,EAAA,CAAA;AAkBE,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,WAAA,OAAA,OAAA,OAAA,YAAA,QAAA,WAAA,KAAA,OAAA,YAAA,WAAA,OAAA,OAAA,OAAA,YAAA,QAAA,UAAA,GAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,OAAA,YAAA,UAAA,OAAA,OAAA,OAAA,YAAA,OAAA,WAAA,KAAA,OAAA,YAAA,UAAA,OAAA,OAAA,OAAA,YAAA,OAAA,UAAA,GAAA;AAC4B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,UAAA,OAAA,OAAA,OAAA,YAAA,OAAA,gBAAA,GAAA;AAM5B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,IAAA,OAAA,YAAA,MAAA,YAAA,GAAA,GAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,WAAA,OAAA,OAAA,YAAA,SAAA,GAAA;AAOE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,0BAAA,IAAA,KAAA,OAAA,YAAA,WAAA,aAAA,OAAA,YAAA,WAAA,aAAA,OAAA,YAAA,WAAA,eAAA,OAAA,YAAA,WAAA,QAAA,CAAA;AAOA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,QAAA,GAAA;AAOF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,SAAA,sBAAA,GAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,IAAA,OAAA,YAAA,WAAA,QAAA,GAAA,GAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,IAAA,OAAA,YAAA,WAAA,QAAA,GAAA,GAAA;;;AD5ER,IAAO,6BAAP,MAAO,4BAA0B;EAM3B;EACA;EACD;EAPT,cAAkC;EAClC,UAAU;EACV,QAAuB;EAEvB,YACU,oBACA,OACD,QAAc;AAFb,SAAA,qBAAA;AACA,SAAA,QAAA;AACD,SAAA,SAAA;EACN;EAEH,WAAQ;AACN,UAAM,gBAAgB,KAAK,MAAM,SAAS,SAAS,IAAI,IAAI;AAC3D,QAAI,eAAe;AACjB,WAAK,gBAAgB,aAAa;IACpC,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,UAAU;IACjB;EACF;EAEQ,gBAAgB,IAAU;AAChC,SAAK,mBAAmB,eAAe,EAAE,EAAE,UAAU;MACnD,MAAM,CAAC,gBAAe;AACpB,aAAK,cAAc;AACnB,aAAK,UAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,aAAK,QAAQ;AACb,aAAK,UAAU;MACjB;KACD;EACH;EAEA,WAAQ;AACN,QAAI,KAAK,eAAe,QAAQ,mDAAmD,GAAG;AACpF,WAAK,UAAU;AACf,WAAK,mBAAmB,kBAAkB,KAAK,YAAY,EAAE,EAAE,UAAU;QACvE,MAAM,MAAK;AACT,eAAK,OAAO,SAAS,CAAC,eAAe,CAAC;QACxC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,+BAA+B,KAAK;AAClD,eAAK,QAAQ;AACb,eAAK,UAAU;QACjB;OACD;IACH;EACF;;qCAjDW,6BAA0B,4BAAA,kBAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAA1B,6BAA0B,WAAA,CAAA,CAAA,wBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,WAAA,QAAA,MAAA,GAAA,CAAA,SAAA,yCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,4EAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,QAAA,kBAAA,gBAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,gBAAA,QAAA,QAAA,cAAA,mBAAA,GAAA,CAAA,QAAA,SAAA,GAAA,cAAA,UAAA,kBAAA,gBAAA,QAAA,QAAA,WAAA,UAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,WAAA,GAAA,CAAA,GAAA,aAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,mBAAA,eAAA,GAAA,CAAA,GAAA,QAAA,QAAA,WAAA,QAAA,mBAAA,cAAA,GAAA,CAAA,GAAA,WAAA,aAAA,eAAA,eAAA,GAAA,CAAA,GAAA,QAAA,aAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,GAAA,CAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,iBAAA,uBAAA,sBAAA,gBAAA,uBAAA,yBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,gBAAA,QAAA,QAAA,UAAA,sBAAA,cAAA,aAAA,WAAA,eAAA,cAAA,cAAA,oBAAA,sBAAA,gBAAA,uBAAA,sBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,QAAA,QAAA,WAAA,kBAAA,YAAA,SAAA,GAAA,CAAA,GAAA,WAAA,eAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,iBAAA,WAAA,eAAA,GAAA,CAAA,GAAA,YAAA,QAAA,QAAA,WAAA,kBAAA,YAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,QAAA,WAAA,WAAA,eAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,aAAA,iBAAA,gBAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,oCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACbvC,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,OAAA,CAAA,EAAmE,GAAA,2CAAA,GAAA,GAAA,OAAA,CAAA,EAI8C,GAAA,2CAAA,IAAA,IAAA,OAAA,CAAA;AA2FnH,MAAA,uBAAA;;;AA/FQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA,CAAA,IAAA,OAAA;;oBDDI,cAAY,SAAA,MAAA,UAAE,cAAY,UAAA,GAAA,QAAA,CAAA,oSAAA,EAAA,CAAA;;;sEAIzB,4BAA0B,CAAA;UAPtC;uBACW,0BAAwB,YACtB,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,qUAAA,EAAA,CAAA;;;;6EAI1B,4BAA0B,EAAA,WAAA,8BAAA,UAAA,oFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}