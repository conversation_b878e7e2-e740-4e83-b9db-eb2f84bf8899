{"version": 3, "sources": ["src/app/features/appointments/appointments.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const APPOINTMENTS_ROUTES: Routes = [\r\n  {\r\n    path: '',\r\n    loadComponent: () => import('./appointment-list/appointment-list.component').then(m => m.AppointmentListComponent)\r\n  },\r\n  {\r\n    path: 'add',\r\n    loadComponent: () => import('./appointment-form/appointment-form.component').then(m => m.AppointmentFormComponent)\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    loadComponent: () => import('./appointment-form/appointment-form.component').then(m => m.AppointmentFormComponent)\r\n  },\r\n  {\r\n    path: ':id',\r\n    loadComponent: () => import('./appointment-detail/appointment-detail.component').then(m => m.AppointmentDetailComponent)\r\n  }\r\n]; "], "mappings": ";;;;;AAEO,IAAM,sBAA8B;EACzC;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEnH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEnH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEnH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAmD,EAAE,KAAK,OAAK,EAAE,0BAA0B;;;", "names": []}