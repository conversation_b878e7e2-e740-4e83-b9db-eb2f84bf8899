import './polyfills.server.mjs';
import {
  UserManagementService
} from "./chunk-MSWF3DHB.mjs";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-VHJENVVD.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  ActivatedRoute,
  CommonModule,
  Component,
  NgIf,
  Router,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-BUZS6RN2.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/user-management/user-form/user-form.component.ts
function UserFormComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275element(1, "div", 11);
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function UserFormComponent_div_10_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275text(1, " First name is required ");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275text(1, " Last name is required ");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_16_span_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_16_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Please enter a valid email address");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, UserFormComponent_div_10_div_16_span_1_Template, 2, 0, "span", 22)(2, UserFormComponent_div_10_div_16_span_2_Template, 2, 0, "span", 22);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_2_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["email"]);
  }
}
function UserFormComponent_div_10_div_17_div_4_span_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Password is required");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_17_div_4_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Password must be at least 6 characters");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_div_17_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, UserFormComponent_div_10_div_17_div_4_span_1_Template, 2, 0, "span", 22)(2, UserFormComponent_div_10_div_17_div_4_span_2_Template, 2, 0, "span", 22);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_3_0;
    let tmp_4_0;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_4_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors["minlength"]);
  }
}
function UserFormComponent_div_10_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "label", 35);
    \u0275\u0275text(2, " Password * ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "input", 36);
    \u0275\u0275template(4, UserFormComponent_div_10_div_17_div_4_Template, 3, 2, "div", 17);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275classProp("border-red-500", ((tmp_2_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_2_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.userForm.get("password")) == null ? null : tmp_3_0.touched));
  }
}
function UserFormComponent_div_10_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275text(1, " Role is required ");
    \u0275\u0275elementEnd();
  }
}
function UserFormComponent_div_10_span_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 37);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 38);
    \u0275\u0275element(2, "circle", 39)(3, "path", 40);
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.isEditMode ? "Updating..." : "Creating...", " ");
  }
}
function UserFormComponent_div_10_span_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.isEditMode ? "Update User" : "Create User", " ");
  }
}
function UserFormComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "form", 14);
    \u0275\u0275listener("ngSubmit", function UserFormComponent_div_10_Template_form_ngSubmit_1_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onSubmit());
    });
    \u0275\u0275elementStart(2, "div")(3, "label", 15);
    \u0275\u0275text(4, " First Name * ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(5, "input", 16);
    \u0275\u0275template(6, UserFormComponent_div_10_div_6_Template, 2, 0, "div", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div")(8, "label", 18);
    \u0275\u0275text(9, " Last Name * ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(10, "input", 19);
    \u0275\u0275template(11, UserFormComponent_div_10_div_11_Template, 2, 0, "div", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div")(13, "label", 20);
    \u0275\u0275text(14, " Email * ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(15, "input", 21);
    \u0275\u0275template(16, UserFormComponent_div_10_div_16_Template, 3, 2, "div", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275template(17, UserFormComponent_div_10_div_17_Template, 5, 3, "div", 22);
    \u0275\u0275elementStart(18, "div")(19, "label", 23);
    \u0275\u0275text(20, " Role * ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "select", 24)(22, "option", 25);
    \u0275\u0275text(23, "Select a role");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "option", 26);
    \u0275\u0275text(25, "Admin");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(26, "option", 27);
    \u0275\u0275text(27, "Doctor");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "option", 28);
    \u0275\u0275text(29, "Receptionist");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "option", 29);
    \u0275\u0275text(31, "Patient");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(32, UserFormComponent_div_10_div_32_Template, 2, 0, "div", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "div", 30)(34, "button", 31);
    \u0275\u0275text(35, " Cancel ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "button", 32);
    \u0275\u0275template(37, UserFormComponent_div_10_span_37_Template, 5, 1, "span", 33)(38, UserFormComponent_div_10_span_38_Template, 2, 1, "span", 22);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    let tmp_4_0;
    let tmp_5_0;
    let tmp_6_0;
    let tmp_9_0;
    let tmp_11_0;
    let tmp_12_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r0.userForm);
    \u0275\u0275advance(4);
    \u0275\u0275classProp("border-red-500", ((tmp_2_0 = ctx_r0.userForm.get("firstName")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.userForm.get("firstName")) == null ? null : tmp_2_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.userForm.get("firstName")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.userForm.get("firstName")) == null ? null : tmp_3_0.touched));
    \u0275\u0275advance(4);
    \u0275\u0275classProp("border-red-500", ((tmp_4_0 = ctx_r0.userForm.get("lastName")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.userForm.get("lastName")) == null ? null : tmp_4_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_5_0 = ctx_r0.userForm.get("lastName")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.userForm.get("lastName")) == null ? null : tmp_5_0.touched));
    \u0275\u0275advance(4);
    \u0275\u0275classProp("border-red-500", ((tmp_6_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_6_0.touched))("bg-gray-100", ctx_r0.isEditMode);
    \u0275\u0275property("readonly", ctx_r0.isEditMode);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_9_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx_r0.userForm.get("email")) == null ? null : tmp_9_0.touched));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isEditMode);
    \u0275\u0275advance(4);
    \u0275\u0275classProp("border-red-500", ((tmp_11_0 = ctx_r0.userForm.get("role")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx_r0.userForm.get("role")) == null ? null : tmp_11_0.touched));
    \u0275\u0275advance(11);
    \u0275\u0275property("ngIf", ((tmp_12_0 = ctx_r0.userForm.get("role")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx_r0.userForm.get("role")) == null ? null : tmp_12_0.touched));
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", ctx_r0.userForm.invalid || ctx_r0.loading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.loading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.loading);
  }
}
var UserFormComponent = class _UserFormComponent {
  fb;
  userManagementService;
  route;
  router;
  userForm;
  isEditMode = false;
  userId = null;
  loading = false;
  error = "";
  constructor(fb, userManagementService, route, router) {
    this.fb = fb;
    this.userManagementService = userManagementService;
    this.route = route;
    this.router = router;
    this.userForm = this.fb.group({
      firstName: ["", [Validators.required]],
      lastName: ["", [Validators.required]],
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(6)]],
      role: ["", [Validators.required]]
    });
  }
  ngOnInit() {
    const idParam = this.route.snapshot.paramMap.get("id");
    if (idParam) {
      this.userId = parseInt(idParam, 10);
      this.isEditMode = true;
      this.userForm.get("password")?.clearValidators();
      this.userForm.get("password")?.updateValueAndValidity();
      this.loadUser();
    }
  }
  loadUser() {
    if (!this.userId)
      return;
    this.loading = true;
    this.error = "";
    this.userManagementService.getUserById(this.userId).subscribe({
      next: (user) => {
        this.userForm.patchValue({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        });
        this.loading = false;
      },
      error: (error) => {
        this.error = "Failed to load user data. Please try again.";
        this.loading = false;
        console.error("Error loading user:", error);
      }
    });
  }
  onSubmit() {
    if (this.userForm.valid) {
      this.loading = true;
      this.error = "";
      if (this.isEditMode && this.userId) {
        const updateData = {
          firstName: this.userForm.value.firstName,
          lastName: this.userForm.value.lastName,
          role: this.userForm.value.role
        };
        this.userManagementService.updateUser(this.userId, updateData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(["/user-management"]);
          },
          error: (error) => {
            this.error = "Failed to update user. Please try again.";
            this.loading = false;
            console.error("Error updating user:", error);
          }
        });
      } else {
        const createData = {
          firstName: this.userForm.value.firstName,
          lastName: this.userForm.value.lastName,
          email: this.userForm.value.email,
          password: this.userForm.value.password,
          role: this.userForm.value.role
        };
        this.userManagementService.createUser(createData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(["/user-management"]);
          },
          error: (error) => {
            this.error = error.error?.message || "Failed to create user. Please try again.";
            this.loading = false;
            console.error("Error creating user:", error);
          }
        });
      }
    }
  }
  static \u0275fac = function UserFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(UserManagementService), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserFormComponent, selectors: [["app-user-form"]], decls: 11, vars: 4, consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "max-w-2xl", "mx-auto"], [1, "flex", "items-center", "mb-6"], ["routerLink", "/user-management", 1, "mr-4", "text-gray-600", "hover:text-gray-900"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 19l-7-7 7-7"], [1, "text-3xl", "font-bold", "text-gray-900"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], ["class", "bg-white shadow rounded-lg", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "bg-red-50", "border", "border-red-200", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "bg-white", "shadow", "rounded-lg"], [1, "p-6", "space-y-6", 3, "ngSubmit", "formGroup"], ["for", "firstName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["type", "text", "id", "firstName", "formControlName", "firstName", 1, "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500"], ["class", "mt-1 text-sm text-red-600", 4, "ngIf"], ["for", "lastName", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["type", "text", "id", "lastName", "formControlName", "lastName", 1, "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500"], ["for", "email", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["type", "email", "id", "email", "formControlName", "email", 1, "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500", 3, "readonly"], [4, "ngIf"], ["for", "role", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["id", "role", "formControlName", "role", 1, "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500"], ["value", ""], ["value", "Admin"], ["value", "Doctor"], ["value", "Receptionist"], ["value", "Patient"], [1, "flex", "justify-end", "space-x-3", "pt-6", "border-t", "border-gray-200"], ["type", "button", "routerLink", "/user-management", 1, "px-4", "py-2", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-primary-500"], ["type", "submit", 1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-primary-600", "hover:bg-primary-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-primary-500", "disabled:opacity-50", "disabled:cursor-not-allowed", 3, "disabled"], ["class", "inline-flex items-center", 4, "ngIf"], [1, "mt-1", "text-sm", "text-red-600"], ["for", "password", 1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1"], ["type", "password", "id", "password", "formControlName", "password", 1, "w-full", "px-3", "py-2", "border", "border-gray-300", "rounded-md", "shadow-sm", "focus:outline-none", "focus:ring-primary-500", "focus:border-primary-500"], [1, "inline-flex", "items-center"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", 1, "animate-spin", "-ml-1", "mr-2", "h-4", "w-4", "text-white"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"]], template: function UserFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "button", 3);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 4);
      \u0275\u0275element(5, "path", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "h1", 6);
      \u0275\u0275text(7);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(8, UserFormComponent_div_8_Template, 2, 0, "div", 7)(9, UserFormComponent_div_9_Template, 2, 1, "div", 8)(10, UserFormComponent_div_10_Template, 39, 20, "div", 9);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Edit User" : "Create New User", " ");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule, RouterLink], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=user-form.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserFormComponent, [{
    type: Component,
    args: [{ selector: "app-user-form", standalone: true, imports: [CommonModule, ReactiveFormsModule, RouterModule], template: `
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
          <button 
            routerLink="/user-management"
            class="mr-4 text-gray-600 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-3xl font-bold text-gray-900">
            {{ isEditMode ? 'Edit User' : 'Create New User' }}
          </h1>
        </div>

        <div *ngIf="loading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {{ error }}
        </div>

        <div *ngIf="!loading" class="bg-white shadow rounded-lg">
          <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="p-6 space-y-6">
            <!-- First Name -->
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                id="firstName"
                formControlName="firstName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched"
              />
              <div *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
                First name is required
              </div>
            </div>

            <!-- Last Name -->
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <input
                type="text"
                id="lastName"
                formControlName="lastName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched"
              />
              <div *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
                Last name is required
              </div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                id="email"
                formControlName="email"
                [readonly]="isEditMode"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('email')?.invalid && userForm.get('email')?.touched"
                [class.bg-gray-100]="isEditMode"
              />
              <div *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
                <span *ngIf="userForm.get('email')?.errors?.['required']">Email is required</span>
                <span *ngIf="userForm.get('email')?.errors?.['email']">Please enter a valid email address</span>
              </div>
            </div>

            <!-- Password (only for create) -->
            <div *ngIf="!isEditMode">
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                Password *
              </label>
              <input
                type="password"
                id="password"
                formControlName="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('password')?.invalid && userForm.get('password')?.touched"
              />
              <div *ngIf="userForm.get('password')?.invalid && userForm.get('password')?.touched" class="mt-1 text-sm text-red-600">
                <span *ngIf="userForm.get('password')?.errors?.['required']">Password is required</span>
                <span *ngIf="userForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
              </div>
            </div>

            <!-- Role -->
            <div>
              <label for="role" class="block text-sm font-medium text-gray-700 mb-1">
                Role *
              </label>
              <select
                id="role"
                formControlName="role"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('role')?.invalid && userForm.get('role')?.touched"
              >
                <option value="">Select a role</option>
                <option value="Admin">Admin</option>
                <option value="Doctor">Doctor</option>
                <option value="Receptionist">Receptionist</option>
                <option value="Patient">Patient</option>
              </select>
              <div *ngIf="userForm.get('role')?.invalid && userForm.get('role')?.touched" class="mt-1 text-sm text-red-600">
                Role is required
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                routerLink="/user-management"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                [disabled]="userForm.invalid || loading"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span *ngIf="loading" class="inline-flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isEditMode ? 'Updating...' : 'Creating...' }}
                </span>
                <span *ngIf="!loading">
                  {{ isEditMode ? 'Update User' : 'Create User' }}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;219558ef63f119a92210704329b58a3cdceaa4fb296db559e672f74512827dc7;F:/clinc/clinic-frontend/src/app/features/user-management/user-form/user-form.component.ts */\n:host {\n  display: block;\n}\n/*# sourceMappingURL=user-form.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: UserManagementService }, { type: ActivatedRoute }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserFormComponent, { className: "UserFormComponent", filePath: "src/app/features/user-management/user-form/user-form.component.ts", lineNumber: 171 });
})();
export {
  UserFormComponent
};
//# sourceMappingURL=chunk-S35PICXH.mjs.map
