import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  BEFORE_APP_SERIALIZED,
  DominoAdapter,
  ENABLE_DOM_EMULATION,
  INITIAL_CONFIG,
  INTERNAL_SERVER_PLATFORM_PROVIDERS,
  PlatformState,
  SERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderInternal,
  renderModule
} from "./chunk-Q4GG6L4S.js";
import "./chunk-754MTROE.js";
import "./chunk-SVWSKDNZ.js";
import "./chunk-NZIWEXW2.js";
import "./chunk-7VVESGAN.js";
import "./chunk-62SVJMVC.js";
import "./chunk-LEZCLC5X.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  BEFORE_APP_SERIALIZED,
  INITIAL_CONFIG,
  PlatformState,
  ServerModule,
  VERSION,
  platformServer,
  provideServerRendering,
  renderApplication,
  renderModule,
  DominoAdapter as ɵDominoAdapter,
  ENABLE_DOM_EMULATION as ɵENABLE_DOM_EMULATION,
  INTERNAL_SERVER_PLATFORM_PROVIDERS as ɵINTERNAL_SERVER_PLATFORM_PROVIDERS,
  SERVER_CONTEXT as ɵSERVER_CONTEXT,
  SERVER_RENDER_PROVIDERS as ɵSERVER_RENDER_PROVIDERS,
  renderInternal as ɵrenderInternal
};
