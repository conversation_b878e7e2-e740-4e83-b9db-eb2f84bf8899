{"version": 3, "sources": ["src/app/features/schedules/schedules.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const SCHEDULES_ROUTES: Routes = [\r\n  // Comment out missing component routes\r\n  // {\r\n  //   path: 'list',\r\n  //   loadComponent: () => import('./schedule-list/schedule-list.component').then(m => m.ScheduleListComponent)\r\n  // },\r\n  // {\r\n  //   path: 'new',\r\n  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)\r\n  // },\r\n  // {\r\n  //   path: 'edit/:id',\r\n  //   loadComponent: () => import('./schedule-form/schedule-form.component').then(m => m.ScheduleFormComponent)\r\n  // },\r\n  // {\r\n  //   path: ':id',\r\n  //   loadComponent: () => import('./schedule-detail/schedule-detail.component').then(m => m.ScheduleDetailComponent)\r\n  // },\r\n]; "], "mappings": ";;;;AAEO,IAAM,mBAA2B;;;;;;;;;;;;;;;;;;;", "names": []}