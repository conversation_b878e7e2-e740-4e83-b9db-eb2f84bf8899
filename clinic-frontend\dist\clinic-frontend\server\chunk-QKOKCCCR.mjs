import './polyfills.server.mjs';
import {
  Router
} from "./chunk-YKEX2NSQ.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵtext
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/auth/not-found/not-found.component.ts
var NotFoundComponent = class _NotFoundComponent {
  router;
  constructor(router) {
    this.router = router;
  }
  goHome() {
    this.router.navigate(["/dashboard"]);
  }
  static \u0275fac = function NotFoundComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotFoundComponent)(\u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotFoundComponent, selectors: [["app-not-found"]], decls: 17, vars: 0, consts: [[1, "min-h-screen", "flex", "items-center", "justify-center", "bg-gray-50", "py-12", "px-4", "sm:px-6", "lg:px-8"], [1, "max-w-md", "w-full", "space-y-8", "bg-white", "p-8", "rounded-lg", "shadow-md", "text-center"], [1, "text-9xl", "font-bold", "text-primary-600"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "mx-auto", "h-24", "w-24", "text-gray-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "mt-6", "text-center", "text-3xl", "font-extrabold", "text-gray-900"], [1, "mt-2", "text-center", "text-sm", "text-gray-600"], [1, "mt-8"], [1, "group", "relative", "w-full", "flex", "justify-center", "py-2", "px-4", "border", "border-transparent", "text-sm", "font-medium", "rounded-md", "text-white", "bg-primary-600", "hover:bg-primary-700", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-primary-500", 3, "click"], [1, "absolute", "left-0", "inset-y-0", "flex", "items-center", "pl-3"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "text-primary-500", "group-hover:text-primary-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"]], template: function NotFoundComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div")(3, "h1", 2);
      \u0275\u0275text(4, "404");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(5, "svg", 3);
      \u0275\u0275element(6, "path", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(7, "h2", 5);
      \u0275\u0275text(8, "Page Not Found");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "p", 6);
      \u0275\u0275text(10, " The page you are looking for doesn't exist or has been moved. ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(11, "div", 7)(12, "button", 8);
      \u0275\u0275listener("click", function NotFoundComponent_Template_button_click_12_listener() {
        return ctx.goHome();
      });
      \u0275\u0275elementStart(13, "span", 9);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(14, "svg", 10);
      \u0275\u0275element(15, "path", 11);
      \u0275\u0275elementEnd()();
      \u0275\u0275text(16, " Go to Home ");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  height: 100%;\n}\n/*# sourceMappingURL=not-found.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotFoundComponent, [{
    type: Component,
    args: [{ selector: "app-not-found", standalone: true, imports: [CommonModule], template: `<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">\r
  <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md text-center">\r
    <div>\r
      <h1 class="text-9xl font-bold text-primary-600">404</h1>\r
      <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />\r
      </svg>\r
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">Page Not Found</h2>\r
      <p class="mt-2 text-center text-sm text-gray-600">\r
        The page you are looking for doesn't exist or has been moved.\r
      </p>\r
    </div>\r
    \r
    <div class="mt-8">\r
      <button \r
        (click)="goHome()" \r
        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"\r
      >\r
        <span class="absolute left-0 inset-y-0 flex items-center pl-3">\r
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-500 group-hover:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">\r
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />\r
          </svg>\r
        </span>\r
        Go to Home\r
      </button>\r
    </div>\r
  </div>\r
</div> `, styles: ["/* src/app/features/auth/not-found/not-found.component.scss */\n:host {\n  display: block;\n  height: 100%;\n}\n/*# sourceMappingURL=not-found.component.css.map */\n"] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotFoundComponent, { className: "NotFoundComponent", filePath: "src/app/features/auth/not-found/not-found.component.ts", lineNumber: 12 });
})();
export {
  NotFoundComponent
};
//# sourceMappingURL=chunk-QKOKCCCR.mjs.map
