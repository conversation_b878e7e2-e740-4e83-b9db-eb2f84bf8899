import './polyfills.server.mjs';
import {
  PatientService
} from "./chunk-HLVG6LNG.mjs";
import "./chunk-QSZZESH5.mjs";
import {
  ActivatedRoute,
  CommonModule,
  Component,
  DatePipe,
  NgIf,
  RouterLink,
  RouterModule,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-BUZS6RN2.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/patients/patient-detail/patient-detail.component.ts
var _c0 = (a0) => ["/patients", a0, "edit"];
function PatientDetailComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "div", 5);
    \u0275\u0275elementEnd();
  }
}
function PatientDetailComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 6)(1, "div", 7)(2, "div", 8)(3, "div", 9)(4, "h1", 10);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 11)(7, "button", 12);
    \u0275\u0275text(8, " Edit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "button", 13);
    \u0275\u0275listener("click", function PatientDetailComponent_div_2_Template_button_click_9_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onDelete());
    });
    \u0275\u0275text(10, " Delete ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(11, "div", 14)(12, "div", 15)(13, "div")(14, "h2", 16);
    \u0275\u0275text(15, "Personal Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "dl", 17)(17, "div")(18, "dt", 18);
    \u0275\u0275text(19, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "dd", 19);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div")(23, "dt", 18);
    \u0275\u0275text(24, "Phone");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "dd", 19);
    \u0275\u0275text(26);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "div")(28, "dt", 18);
    \u0275\u0275text(29, "Date of Birth");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "dd", 19);
    \u0275\u0275text(31);
    \u0275\u0275pipe(32, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(33, "div")(34, "dt", 18);
    \u0275\u0275text(35, "Gender");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "dd", 19);
    \u0275\u0275text(37);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(38, "div")(39, "h2", 16);
    \u0275\u0275text(40, "Address Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "dl", 17)(42, "div")(43, "dt", 18);
    \u0275\u0275text(44, "Street");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(45, "dd", 19);
    \u0275\u0275text(46);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(47, "div")(48, "dt", 18);
    \u0275\u0275text(49, "City");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(50, "dd", 19);
    \u0275\u0275text(51);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(52, "div")(53, "dt", 18);
    \u0275\u0275text(54, "State");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(55, "dd", 19);
    \u0275\u0275text(56);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(57, "div")(58, "dt", 18);
    \u0275\u0275text(59, "Postal Code");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(60, "dd", 19);
    \u0275\u0275text(61);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(62, "div")(63, "dt", 18);
    \u0275\u0275text(64, "Country");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(65, "dd", 19);
    \u0275\u0275text(66);
    \u0275\u0275elementEnd()()()()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate2(" ", ctx_r1.patient.firstName, " ", ctx_r1.patient.lastName, " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(14, _c0, ctx_r1.patient.id));
    \u0275\u0275advance(14);
    \u0275\u0275textInterpolate(ctx_r1.patient.email);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.patient.phoneNumber);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind1(32, 12, ctx_r1.patient.dateOfBirth));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r1.patient.gender);
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r1.patient.street);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.patient.city);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.patient.state);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.patient.postalCode);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.patient.country);
  }
}
function PatientDetailComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.error, " ");
  }
}
var PatientDetailComponent = class _PatientDetailComponent {
  patientService;
  route;
  patient = null;
  loading = false;
  error = "";
  constructor(patientService, route) {
    this.patientService = patientService;
    this.route = route;
  }
  ngOnInit() {
  }
  onDelete() {
    if (this.patient && confirm("Are you sure you want to delete this patient?")) {
      this.patientService.deletePatient(this.patient.id).subscribe({
        next: () => {
        },
        error: (error) => {
          this.error = "Failed to delete patient. Please try again.";
          console.error("Error deleting patient:", error);
        }
      });
    }
  }
  static \u0275fac = function PatientDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PatientDetailComponent)(\u0275\u0275directiveInject(PatientService), \u0275\u0275directiveInject(ActivatedRoute));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PatientDetailComponent, selectors: [["app-patient-detail"]], decls: 4, vars: 3, consts: [[1, "container", "mx-auto", "px-4", "py-6"], ["class", "flex justify-center items-center h-64", 4, "ngIf"], ["class", "max-w-3xl mx-auto", 4, "ngIf"], ["class", "mt-4 text-red-600 text-center", 4, "ngIf"], [1, "flex", "justify-center", "items-center", "h-64"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-t-2", "border-b-2", "border-primary-500"], [1, "max-w-3xl", "mx-auto"], [1, "bg-white", "shadow", "rounded-lg", "overflow-hidden"], [1, "px-6", "py-4", "border-b", "border-gray-200"], [1, "flex", "justify-between", "items-center"], [1, "text-2xl", "font-bold", "text-gray-900"], [1, "flex", "space-x-3"], [1, "px-4", "py-2", "border", "border-gray-300", "rounded-md", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-50", 3, "routerLink"], [1, "px-4", "py-2", "border", "border-transparent", "rounded-md", "shadow-sm", "text-sm", "font-medium", "text-white", "bg-red-600", "hover:bg-red-700", 3, "click"], [1, "px-6", "py-4"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], [1, "text-lg", "font-medium", "text-gray-900", "mb-4"], [1, "space-y-3"], [1, "text-sm", "font-medium", "text-gray-500"], [1, "mt-1", "text-sm", "text-gray-900"], [1, "mt-4", "text-red-600", "text-center"]], template: function PatientDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, PatientDetailComponent_div_1_Template, 2, 0, "div", 1)(2, PatientDetailComponent_div_2_Template, 67, 16, "div", 2)(3, PatientDetailComponent_div_3_Template, 2, 1, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.patient);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
    }
  }, dependencies: [CommonModule, NgIf, DatePipe, RouterModule, RouterLink], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=patient-detail.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PatientDetailComponent, [{
    type: Component,
    args: [{ selector: "app-patient-detail", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="container mx-auto px-4 py-6">
      <div *ngIf="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>

      <div *ngIf="!loading && patient" class="max-w-3xl mx-auto">
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h1 class="text-2xl font-bold text-gray-900">
                {{ patient.firstName }} {{ patient.lastName }}
              </h1>
              <div class="flex space-x-3">
                <button
                  [routerLink]="['/patients', patient.id, 'edit']"
                  class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Edit
                </button>
                <button
                  (click)="onDelete()"
                  class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Personal Information -->
              <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h2>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.email }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Phone</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.phoneNumber }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.dateOfBirth | date }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Gender</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.gender }}</dd>
                  </div>
                </dl>
              </div>

              <!-- Address Information -->
              <div>
                <h2 class="text-lg font-medium text-gray-900 mb-4">Address Information</h2>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Street</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.street }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">City</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.city }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">State</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.state }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Postal Code</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.postalCode }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Country</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ patient.country }}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="error" class="mt-4 text-red-600 text-center">
        {{ error }}
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;219558ef63f119a92210704329b58a3cdceaa4fb296db559e672f74512827dc7;F:/clinc/clinic-frontend/src/app/features/patients/patient-detail/patient-detail.component.ts */\n:host {\n  display: block;\n}\n/*# sourceMappingURL=patient-detail.component.css.map */\n"] }]
  }], () => [{ type: PatientService }, { type: ActivatedRoute }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PatientDetailComponent, { className: "PatientDetailComponent", filePath: "src/app/features/patients/patient-detail/patient-detail.component.ts", lineNumber: 112 });
})();
export {
  PatientDetailComponent
};
//# sourceMappingURL=chunk-6ZNA6IDC.mjs.map
