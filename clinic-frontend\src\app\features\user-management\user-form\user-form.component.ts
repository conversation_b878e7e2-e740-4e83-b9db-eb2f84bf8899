import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { UserManagementService } from '../services/user-management.service';
import { UserManagement, CreateUserRequest, UpdateUserRequest } from '../models/user-management.model';
import { UserRole } from '../../../core/models/auth.model';

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  template: `
    <div class="container mx-auto px-4 py-6">
      <div class="max-w-2xl mx-auto">
        <div class="flex items-center mb-6">
          <button 
            routerLink="/user-management"
            class="mr-4 text-gray-600 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-3xl font-bold text-gray-900">
            {{ isEditMode ? 'Edit User' : 'Create New User' }}
          </h1>
        </div>

        <div *ngIf="loading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>

        <div *ngIf="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {{ error }}
        </div>

        <div *ngIf="!loading" class="bg-white shadow rounded-lg">
          <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="p-6 space-y-6">
            <!-- First Name -->
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                type="text"
                id="firstName"
                formControlName="firstName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched"
              />
              <div *ngIf="userForm.get('firstName')?.invalid && userForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
                First name is required
              </div>
            </div>

            <!-- Last Name -->
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <input
                type="text"
                id="lastName"
                formControlName="lastName"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched"
              />
              <div *ngIf="userForm.get('lastName')?.invalid && userForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
                Last name is required
              </div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <input
                type="email"
                id="email"
                formControlName="email"
                [readonly]="isEditMode"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('email')?.invalid && userForm.get('email')?.touched"
                [class.bg-gray-100]="isEditMode"
              />
              <div *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
                <span *ngIf="userForm.get('email')?.errors?.['required']">Email is required</span>
                <span *ngIf="userForm.get('email')?.errors?.['email']">Please enter a valid email address</span>
              </div>
            </div>

            <!-- Password (only for create) -->
            <div *ngIf="!isEditMode">
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                Password *
              </label>
              <input
                type="password"
                id="password"
                formControlName="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('password')?.invalid && userForm.get('password')?.touched"
              />
              <div *ngIf="userForm.get('password')?.invalid && userForm.get('password')?.touched" class="mt-1 text-sm text-red-600">
                <span *ngIf="userForm.get('password')?.errors?.['required']">Password is required</span>
                <span *ngIf="userForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
              </div>
            </div>

            <!-- Role -->
            <div>
              <label for="role" class="block text-sm font-medium text-gray-700 mb-1">
                Role *
              </label>
              <select
                id="role"
                formControlName="role"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                [class.border-red-500]="userForm.get('role')?.invalid && userForm.get('role')?.touched"
              >
                <option value="">Select a role</option>
                <option value="Admin">Admin</option>
                <option value="Doctor">Doctor</option>
                <option value="Receptionist">Receptionist</option>
                <option value="Patient">Patient</option>
              </select>
              <div *ngIf="userForm.get('role')?.invalid && userForm.get('role')?.touched" class="mt-1 text-sm text-red-600">
                Role is required
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                routerLink="/user-management"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                [disabled]="userForm.invalid || loading"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span *ngIf="loading" class="inline-flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isEditMode ? 'Updating...' : 'Creating...' }}
                </span>
                <span *ngIf="!loading">
                  {{ isEditMode ? 'Update User' : 'Create User' }}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class UserFormComponent implements OnInit {
  userForm: FormGroup;
  isEditMode = false;
  userId: number | null = null;
  loading = false;
  error = '';

  constructor(
    private fb: FormBuilder,
    private userManagementService: UserManagementService,
    private route: ActivatedRoute,
    private router: Router
  ) {
    this.userForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      role: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    if (idParam) {
      this.userId = parseInt(idParam, 10);
      this.isEditMode = true;
      // Remove password validation for edit mode
      this.userForm.get('password')?.clearValidators();
      this.userForm.get('password')?.updateValueAndValidity();
      this.loadUser();
    }
  }

  private loadUser(): void {
    if (!this.userId) return;

    this.loading = true;
    this.error = '';

    this.userManagementService.getUserById(this.userId).subscribe({
      next: (user: UserManagement) => {
        this.userForm.patchValue({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        });
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load user data. Please try again.';
        this.loading = false;
        console.error('Error loading user:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      this.loading = true;
      this.error = '';

      if (this.isEditMode && this.userId) {
        // Update user
        const updateData: UpdateUserRequest = {
          firstName: this.userForm.value.firstName,
          lastName: this.userForm.value.lastName,
          role: this.userForm.value.role
        };

        this.userManagementService.updateUser(this.userId, updateData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(['/user-management']);
          },
          error: (error) => {
            this.error = 'Failed to update user. Please try again.';
            this.loading = false;
            console.error('Error updating user:', error);
          }
        });
      } else {
        // Create user
        const createData: CreateUserRequest = {
          firstName: this.userForm.value.firstName,
          lastName: this.userForm.value.lastName,
          email: this.userForm.value.email,
          password: this.userForm.value.password,
          role: this.userForm.value.role
        };

        this.userManagementService.createUser(createData).subscribe({
          next: () => {
            this.loading = false;
            this.router.navigate(['/user-management']);
          },
          error: (error) => {
            this.error = error.error?.message || 'Failed to create user. Please try again.';
            this.loading = false;
            console.error('Error creating user:', error);
          }
        });
      }
    }
  }
}
