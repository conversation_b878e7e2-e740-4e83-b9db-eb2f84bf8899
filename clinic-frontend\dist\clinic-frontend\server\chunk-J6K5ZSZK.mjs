import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/features/appointments/appointments.routes.ts
var APPOINTMENTS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-5GXXQED7.mjs").then((m) => m.AppointmentListComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-list/appointment-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-3SCGFBMN.mjs").then((m) => m.AppointmentFormComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-3SCGFBMN.mjs").then((m) => m.AppointmentFormComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-5FELQ5L4.mjs").then((m) => m.AppointmentDetailComponent)
  }, true ? { \u0275entryName: "src/app/features/appointments/appointment-detail/appointment-detail.component.ts" } : {})
];
export {
  APPOINTMENTS_ROUTES
};
//# sourceMappingURL=chunk-J6K5ZSZK.mjs.map
