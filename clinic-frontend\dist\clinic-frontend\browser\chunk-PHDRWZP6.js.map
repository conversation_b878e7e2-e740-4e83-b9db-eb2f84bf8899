{"version": 3, "sources": ["src/app/features/auth/not-found/not-found.component.ts", "src/app/features/auth/not-found/not-found.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-not-found',\r\n  templateUrl: './not-found.component.html',\r\n  styleUrls: ['./not-found.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule]\r\n})\r\nexport class NotFoundComponent {\r\n  constructor(private router: Router) {}\r\n\r\n  goHome(): void {\r\n    this.router.navigate(['/dashboard']);\r\n  }\r\n} ", "<div class=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n  <div class=\"max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md text-center\">\r\n    <div>\r\n      <h1 class=\"text-9xl font-bold text-primary-600\">404</h1>\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mx-auto h-24 w-24 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n      </svg>\r\n      <h2 class=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Page Not Found</h2>\r\n      <p class=\"mt-2 text-center text-sm text-gray-600\">\r\n        The page you are looking for doesn't exist or has been moved.\r\n      </p>\r\n    </div>\r\n    \r\n    <div class=\"mt-8\">\r\n      <button \r\n        (click)=\"goHome()\" \r\n        class=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\r\n      >\r\n        <span class=\"absolute left-0 inset-y-0 flex items-center pl-3\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-primary-500 group-hover:text-primary-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\r\n          </svg>\r\n        </span>\r\n        Go to Home\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAWM,IAAO,oBAAP,MAAO,mBAAiB;EACR;EAApB,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,SAAM;AACJ,SAAK,OAAO,SAAS,CAAC,YAAY,CAAC;EACrC;;qCALW,oBAAiB,4BAAA,MAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,gBAAA,QAAA,gBAAA,kBAAA,cAAA,SAAA,QAAA,WAAA,SAAA,GAAA,CAAA,GAAA,YAAA,UAAA,aAAA,YAAA,OAAA,cAAA,aAAA,aAAA,GAAA,CAAA,GAAA,YAAA,aAAA,kBAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,WAAA,QAAA,QAAA,eAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,oFAAA,GAAA,CAAA,GAAA,QAAA,eAAA,YAAA,kBAAA,eAAA,GAAA,CAAA,GAAA,QAAA,eAAA,WAAA,eAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,SAAA,YAAA,UAAA,QAAA,kBAAA,QAAA,QAAA,UAAA,sBAAA,WAAA,eAAA,cAAA,cAAA,kBAAA,wBAAA,sBAAA,gBAAA,uBAAA,0BAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,UAAA,aAAA,QAAA,gBAAA,MAAA,GAAA,CAAA,SAAA,8BAAA,QAAA,QAAA,WAAA,aAAA,UAAA,gBAAA,GAAA,OAAA,OAAA,oBAAA,8BAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,kJAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACX9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiG,GAAA,OAAA,CAAA,EACV,GAAA,KAAA,EAC9E,GAAA,MAAA,CAAA;AAC6C,MAAA,iBAAA,GAAA,KAAA;AAAG,MAAA,uBAAA;;AACnD,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;;AACA,MAAA,yBAAA,GAAA,MAAA,CAAA;AAAmE,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA;AACjF,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,IAAA,iEAAA;AACF,MAAA,uBAAA,EAAI;AAGN,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,UAAA,CAAA;AAEd,MAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;AAGjB,MAAA,yBAAA,IAAA,QAAA,CAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AACF,MAAA,uBAAA,EAAM;AAER,MAAA,iBAAA,IAAA,cAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;;oBDjBI,YAAY,GAAA,QAAA,CAAA,oHAAA,EAAA,CAAA;;;sEAEX,mBAAiB,CAAA;UAP7B;uBACW,iBAAe,YAGb,MAAI,SACP,CAAC,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;UAAA,QAAA,CAAA,uKAAA,EAAA,CAAA;;;;6EAEZ,mBAAiB,EAAA,WAAA,qBAAA,UAAA,0DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}