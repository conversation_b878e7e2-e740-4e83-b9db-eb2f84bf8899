import {
  __spreadValues
} from "./chunk-Y5RQAIA6.js";

// src/app/features/appointments/appointments.routes.ts
var APPOINTMENTS_ROUTES = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-2K4IJDJA.js").then((m) => m.AppointmentListComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-list/appointment-list.component.ts" } : {}),
  __spreadValues({
    path: "add",
    loadComponent: () => import("./chunk-OTILAL4T.js").then((m) => m.AppointmentFormComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: "edit/:id",
    loadComponent: () => import("./chunk-OTILAL4T.js").then((m) => m.AppointmentFormComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-form/appointment-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-SZEF4SCF.js").then((m) => m.AppointmentDetailComponent)
  }, false ? { \u0275entryName: "src/app/features/appointments/appointment-detail/appointment-detail.component.ts" } : {})
];
export {
  APPOINTMENTS_ROUTES
};
//# sourceMappingURL=chunk-OVLIP7PR.js.map
