import './polyfills.server.mjs';
import {
  FormsModule,
  NgControlStatusGroup,
  NgForm,
  ReactiveFormsModule,
  ɵNgNoValidate
} from "./chunk-IIIRLQMQ.mjs";
import {
  RouterLink,
  RouterModule
} from "./chunk-YKEX2NSQ.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TCK56SA4.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts
var MedicalRecordFormComponent = class _MedicalRecordFormComponent {
  constructor() {
  }
  ngOnInit() {
  }
  static \u0275fac = function MedicalRecordFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MedicalRecordFormComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MedicalRecordFormComponent, selectors: [["app-medical-record-form"]], decls: 12, vars: 0, consts: [[1, "container", "mx-auto", "p-4"], [1, "text-2xl", "font-bold", "mb-4"], [1, "space-y-4"], [1, "grid", "gap-4"], [1, "flex", "gap-4"], ["type", "submit", 1, "bg-blue-500", "text-white", "px-4", "py-2", "rounded", "hover:bg-blue-600"], ["routerLink", "..", 1, "bg-gray-500", "text-white", "px-4", "py-2", "rounded", "hover:bg-gray-600"]], template: function MedicalRecordFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1", 1);
      \u0275\u0275text(2, "Medical Record Form");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "form", 2)(4, "div", 3)(5, "p");
      \u0275\u0275text(6, "Form fields will be added here");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "div", 4)(8, "button", 5);
      \u0275\u0275text(9, " Save ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "a", 6);
      \u0275\u0275text(11, " Cancel ");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule, RouterModule, RouterLink, FormsModule, \u0275NgNoValidate, NgControlStatusGroup, NgForm, ReactiveFormsModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MedicalRecordFormComponent, [{
    type: Component,
    args: [{ selector: "app-medical-record-form", standalone: true, imports: [CommonModule, RouterModule, FormsModule, ReactiveFormsModule], template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">Medical Record Form</h1>
      <form class="space-y-4">
        <!-- Placeholder for form fields -->
        <div class="grid gap-4">
          <p>Form fields will be added here</p>
        </div>
        <div class="flex gap-4">
          <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Save
          </button>
          <a routerLink=".." class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
            Cancel
          </a>
        </div>
      </form>
    </div>
  ` }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MedicalRecordFormComponent, { className: "MedicalRecordFormComponent", filePath: "src/app/features/medical-records/components/medical-record-form/medical-record-form.component.ts", lineNumber: 31 });
})();
export {
  MedicalRecordFormComponent
};
//# sourceMappingURL=chunk-M57MSR4W.mjs.map
